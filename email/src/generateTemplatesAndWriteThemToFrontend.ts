import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import generateEmailTemplates from './emailTemplateGeneration.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

(async function generateTemplatesAndWriteThemToFrontend(): Promise<void> {
  try {
    const htmlTemplates: Record<string, string> = await generateEmailTemplates();

    await Promise.all(
      Object.entries(htmlTemplates).map(async ([fileName, content]) => {
        const pathForMustacheFileInBackend: string = path.resolve(
          __dirname,
          `../../alleskoenner_backend/src/main/resources/${fileName}.mustache`,
        );

        fs.writeFileSync(pathForMustacheFileInBackend, content);
        console.debug(`You can find the html email template in this directory: "${pathForMustacheFileInBackend}"`);
      }),
    );
  } catch (error) {
    console.error('Error generating or writing email templates:', error);
  }
})();
