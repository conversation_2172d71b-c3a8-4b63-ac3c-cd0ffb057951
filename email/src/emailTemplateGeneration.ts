import fs from 'fs';
import path from 'path';
import mjml from 'mjml';

export default async function generateEmailTemplates(): Promise<Record<string, string>> {
  const currentDir: string = process.cwd();
  const mjmlDir: string = path.resolve(currentDir, './mjmlTemplates');

  return fs
    .readdirSync(mjmlDir)
    .filter((file: string) => file.endsWith('.mjml'))
    .reduce((htmlMap: Record<string, string>, file: string) => {
      const key: string = path.basename(file, '.mjml');
      const value: string = readInMjmlFileAndTransformToHtml(mjmlDir, file);

      htmlMap[key] = value;

      return htmlMap;
    }, {});
}

function readInMjmlFileAndTransformToHtml(mjmlDir: string, file: string): string {
  const mjmlContent: string = fs.readFileSync(path.join(mjmlDir, file), 'utf-8');

  const { html, errors } = mjml(mjmlContent);

  if (errors && errors.length > 0) {
    console.error('MJML Conversion Errors:', errors);
    throw new Error('Error during MJML to HTML conversion');
  }

  return html;
}
