{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Klose Brothers MainSandbox Env",
            "type": "al",
            "request": "launch",
            "environmentType": "Sandbox",
            "environmentName": "MainSandbox",
            "tenant": "b37c212c-6995-45f1-a9ef-cb27c169c009",
            "startupObjectId": 50002,
            "breakOnError": "All",
            "breakOnRecordWrite": "None",
            "launchBrowser": true,
            "enableSqlInformationDebugger": true,
            "enableLongRunningSqlStatements": true,
            "longRunningSqlStatementsThreshold": 500,
            "numberOfSqlStatements": 10,
            "schemaUpdateMode": "ForceSync"
        },
        {
            "name": "Attach Klose Env",
            "type": "al",
            "request": "attach",
            "environmentType": "Sandbox",
            "environmentName": "MainSandbox",
            "tenant": "b37c212c-6995-45f1-a9ef-cb27c169c009",
            "breakOnError": "All",
            "breakOnRecordWrite": "None",
            "enableSqlInformationDebugger": true,
            "enableLongRunningSqlStatements": true,
            "longRunningSqlStatementsThreshold": 500,
            "numberOfSqlStatements": 10,
            "breakOnNext": "WebServiceClient",
            "userId": "IT Vision"

        },
        {
            "name": "Klose Brothers DevSandbox Env",
            "type": "al",
            "request": "launch",
            "environmentType": "Sandbox",
            "environmentName": "DevSandbox",
            "tenant": "b37c212c-6995-45f1-a9ef-cb27c169c009",
            "startupObjectId": 50002,
            "breakOnError": "All",
            "breakOnRecordWrite": "None",
            "launchBrowser": true,
            "enableSqlInformationDebugger": true,
            "enableLongRunningSqlStatements": true,
            "longRunningSqlStatementsThreshold": 500,
            "numberOfSqlStatements": 10,
            "schemaUpdateMode": "ForceSync"
        },
    ]
}