{"version": "0.2.0", "configurations": [{"name": "<PERSON><PERSON> Env", "type": "al", "request": "launch", "environmentType": "Sandbox", "environmentName": "HandymanSandbox", "tenant": "b37c212c-6995-45f1-a9ef-cb27c169c009", "breakOnError": "All", "breakOnRecordWrite": "None", "launchBrowser": false, "enableSqlInformationDebugger": true, "enableLongRunningSqlStatements": true, "longRunningSqlStatementsThreshold": 500, "numberOfSqlStatements": 10, "schemaUpdateMode": "ForceSync"}]}