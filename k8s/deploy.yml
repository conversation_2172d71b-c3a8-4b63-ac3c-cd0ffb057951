apiVersion: apps/v1
kind: Deployment
metadata:
  name: alleskoenner
  namespace: kunden
  labels:
    app: alleskoenner
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: alleskoenner
  template:
    metadata:
      labels:
        app: alleskoenner
      annotations:
        co.elastic.logs.alleskoenner/json.keys_under_root: "true"
        co.elastic.logs.alleskoenner/json.add_error_key: "true"
        co.elastic.logs.alleskoenner/json.message_key: "message"
    spec:
      containers:
        - name: backend
          image: 'ghcr.io/klosebrothers/alleskoenner:latest'
          imagePullPolicy: IfNotPresent
          terminationMessagePolicy: FallbackToLogsOnError
          env:
            - name: BASE_URL
              value: 'https://deinhandwerker365.klosebrothers.de'
          envFrom:
            - secretRef:
                name: alleskoenner-secrets
                optional: false
            - configMapRef:
                name: alleskoenner-cfg
          ports:
            - containerPort: 80
          resources:
            requests:
              memory: 256M
              cpu: '0.1'
            limits:
              memory: 256M
              cpu: '0.1'
      imagePullSecrets:
        - name: kb-github-registry-secret