apiVersion: v1
data:
  AZURE_EMAIL_SENDER_ADDRESS: Do<PERSON><PERSON><PERSON><PERSON><PERSON>@2f95431e-cd3b-4e0c-a97d-d4c4d8acc60f.azurecomm.net
  BC_API_BASE_URL: https://api.businesscentral.dynamics.com
  BC_CLIENT_ID: 5ca29e2f-8726-4b3f-963c-2e7b84152561
  BC_COMPANY_ID: 048db3c3-8ab8-ef11-b8ec-002248e59eea
  BC_ENVIRONMENT: MainSandbox
  BC_TENANT_ID: b37c212c-6995-45f1-a9ef-cb27c169c009
  MICROSOFT_LOGIN_BASE_URL: https://login.microsoftonline.com
  AZURE_CLIENT_ID: 26b34276-b78b-4b68-97fa-15766d63f4db
  AZURE_TENANT_ID: b37c212c-6995-45f1-a9ef-cb27c169c009
  VITE_HERE_API_KEY: Vc7OPyzBW3n_nPqJ9oIhS_i7re-fpxXau6eaDIg26Ew

kind: ConfigMap
metadata:
  creationTimestamp: null
  name: alleskoenner-cfg
  namespace: kunden
