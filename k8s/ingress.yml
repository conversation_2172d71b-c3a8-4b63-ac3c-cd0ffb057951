apiVersion: v1
kind: Service
metadata:
  name: alleskoenner-svc
  namespace: kunden
spec:
  selector:
    app: alleskoenner
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: alleskoenner-ingress
  namespace: kunden
  annotations:
    cert-manager.io/cluster-issuer: "alleskoenner-issuer"
spec:
  tls:
    - hosts:
        - deinhandwerker365.klosebrothers.de
      secretName: alleskoenner-cert
  rules:
    - host: deinhandwerker365.klosebrothers.de
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: alleskoenner-svc
                port:
                  number: 80
  ingressClassName: nginx
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: alleskoenner-api-ingress
  namespace: kunden
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  tls:
    - hosts:
        - deinhandwerker365.klosebrothers.de
      secretName: alleskoenner-cert
  rules:
    - host: deinhandwerker365.klosebrothers.de
      http:
        paths:
          - path: /api/(.*)
            pathType: Prefix
            backend:
              service:
                name: alleskoenner-backend-svc
                port:
                  number: 8080
  ingressClassName: nginx