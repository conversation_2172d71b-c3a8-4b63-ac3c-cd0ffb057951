apiVersion: apps/v1
kind: Deployment
metadata:
  name: alleskoenner-backend
  namespace: kunden
  labels:
    app: alleskoenner-backend
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: alleskoenner-backend
  template:
    metadata:
      labels:
        app: alleskoenner-backend
      annotations:
        co.elastic.logs.alleskoenner-backend/json.keys_under_root: "true"
        co.elastic.logs.alleskoenner-backend/json.add_error_key: "true"
        co.elastic.logs.alleskoenner-backend/json.message_key: "message"
    spec:
      containers:
        - name: backend
          image: 'ghcr.io/klosebrothers/alleskoenner-backend:latest'
          args:
            - "--spring.profiles.active=test-stage"
          imagePullPolicy: IfNotPresent
          terminationMessagePolicy: FallbackToLogsOnError
          envFrom:
            - secretRef:
                name: alleskoenner-secrets
                optional: false
            - configMapRef:
                name: alleskoenner-cfg
          ports:
            - containerPort: 8080
          resources:
            requests:
              memory: 256M
              cpu: '0.2'
            limits:
              memory: 384M
              cpu: '0.4'
      imagePullSecrets:
        - name: kb-github-registry-secret