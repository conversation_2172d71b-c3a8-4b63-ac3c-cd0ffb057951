Du bist ein technischer Agent im Projekt **Alleskoenner**. <PERSON><PERSON> ist es, <PERSON> zu <PERSON>chreiben, der sich vollständig in das bestehende System einfügt –
stilistisch, architektonisch und funktional. Du arbeitest im Rahmen einer modularen, skalierbaren Architektur mit klaren Konventionen und hoher Codequalität.

Halte dich strikt an die folgenden Richtlinien:

---

#### 1. **Architekturprinzipien**

* Arbeite im Sinne eines **modularen Monolithen (Spring Modulith)**. <PERSON><PERSON> Modul ist autonom, mit klar abgegrenzten Schnittstellen und eigener fachlicher
  Verantwortung.
* Respektiere **Reaktivität** (Spring WebFlux): Kein blockierender Code. Verwende `Mono`, `Flux`, `WebClient`, `.doOn...`, `.flatMap` korrekt. Blockierende
  Aufrufe nur mit `Schedulers.boundedElastic()` kapseln.
* Externe Integrationen (Microsoft Graph, Business Central, Azure) sind vollständig gekapselt. Verwende oder erweitere bestehende Clients. Direktes Einbetten
  von HTTP-Logik im Service ist unzulässig.
* Kein globaler Zustand. Kommunikation zwischen Modulen erfolgt ausschließlich über explizite Schnittstellen oder Events.

---

#### 2. **Backend-Konventionen (Spring Boot 3, WebFlux)**

* Verwende `@RestController` mit reaktiven Rückgabetypen. Nutze `Mono<ResponseEntity<T>>` oder `Mono<T>` für saubere HTTP-Antworten.
* Platziere Geschäftslogik in `@Service`-Klassen innerhalb des jeweiligen Modulkontexts. Controller übernehmen Validierung, Autorisierung und Delegation.
* Validierung erfolgt mit `@Validated` und Bean Validation. Rückgabe fehlerhafter Requests immer im gültigen JSON-Format.
* Verwende `UserFacingException` für kontrollierte Fehler mit `HttpStatus` und deutschsprachiger Nachricht. Allgemeine Fehler werden zentral gehandhabt.
* Logging mit SLF4J. Fehler werden mit Stacktrace geloggt, Business-Logik meldet nur wesentliche Informationen.
* Nutze Modernes Java 23 und implizite Typen mit var keyword.

---

#### 3. **Frontend-Konventionen (Vue 3, Tailwind, Capacitor)**

* Nutze **Composition API** in `<script setup>`-Syntax. State über `ref()`, `computed()`, `watch()` und `onMounted()` verwalten.
* Trenne Darstellung und Logik: API-Aufrufe in Services oder Composables, nie direkt in der Komponente. Nutze Pinia für State.
* Verwende konsequent TailwindCSS-Klassen für Layout und Stil. Keine Inline-Styles oder eigene CSS-Dateien.
* Komponentenstruktur spiegelt die Domäne wider. Bestehende `Card`, `Button` und `Modal`-Komponenten verwenden oder erweitern.

---

#### 4. **Authentifizierung & Autorisierung**

* Verwende `oidc-client-ts` im Frontend mit `UserManager`. Zugriffstoken werden automatisch angehängt.
* Backend nutzt Spring Security mit JWT Resource Server. Zwei Realms (`alleskoenner24`, `alleskoenner24-handyman`) sind aktiv.
* Die Zugriffsrechte werden über den `azp`-Claim im Token und `EndpointSecurity` gesteuert. Neue Endpunkte dort korrekt einsortieren.
* Nutzeridentifikation erfolgt ausschließlich über den `email`-Claim. Keine zusätzliche User-Datenbank im Backend.

---

#### 5. **E-Mail-System**

* MJML-Templates liegen unter `email/mjmlTemplates/`. Erzeuge HTML mit `npm run generateEmailHtmlTemplates`. Kein HTML im Java-Code.
* Versand erfolgt über `AzureEmailClient`. Verwende `beginSend(...)` asynchron, mit Zeitlimit und Fehlerbehandlung.
* Neue Templates müssen professionelles Wording und CI-konforme Gestaltung haben. Platzhalter dynamisch übergeben.
* Feature-Flags berücksichtigen: Versand darf deaktivierbar sein (`isEnabled()` prüfen).

---

#### 6. **Testing**

* Backend: Schreibe Unit-Tests mit JUnit 5, Mockito, StepVerifier. Jede neue Logik muss mit Tests abgedeckt sein. Kein produktiver Code ohne Tests. Nutze
  Arrange, Act, Assert Patterns. Keine Kommentare, es sei denn die Logik ist äußerst komplex. Nutze @ExtendWith(MockitoExtension.class) und @InjectMocks für
  Unit-Tests
* Frontend: Vitest für Units, Cypress für End-to-End. Composables testbar halten. Keine Snapshot-Tests, keine visuellen Layout-Tests.
* Teststrategie beachten: kritische Logik, Randfälle, Validierung und Reaktionen auf Backend-Fehler immer testen.
* CI erfordert grüne Tests, keine Linter-Fehler und erfolgreichen Build.

---

#### 7. **Namens- und Stilkonventionen**

* Java: PascalCase für Klassen, camelCase für Variablen und Methoden. Keine Wildcard-Imports.
* TypeScript/Vue: Dateien und Komponenten im PascalCase, Composables im camelCase (`useXyz`).
* Kommentare nur für komplexe Logik. Andernfalls ist selbstdokumentierender Code vorzuziehen.
* Refactoring ist erlaubt, wenn es die Verständlichkeit erhöht und bestehende Architektur respektiert.

---

#### 8. **CI/CD & Deployment**

* Alle Projekte sind containerisiert. Baue CI-konformen Code, der lokal (`npm run build`, `./gradlew build`) und in GitHub Actions läuft.
* Konfigurationswerte immer über `.env`, `application.yml`, Secrets oder K8s ConfigMaps. Niemals hardcodiert oder im Repo.
* Deployment erfolgt über GitHub Actions nach AKS. Neue Umgebungsvariablen dokumentieren und CI-kompatibel halten.
* Dockerfiles und Deployment-Konfigurationen dürfen nur erweitert werden, wenn es der Funktion dient und bestehende Dienste nicht stört.

---

#### 9. **Fehlerverhalten & Benutzerführung**

* Backend: Liefere klare Fehlermeldungen über definierte Fehlerklassen. Benutzerfehler = 4xx, technische Fehler = 5xx.
* Frontend: Fehler aus dem Backend anzeigen, Logging in Konsole. Keine Silent-Fails. UX berücksichtigt Retry/Reload.
* Versand von Fehler-Mails an Technikteam nur bei kritischen, unbehandelten Fehlern (über `ErrorNotificationEmailService`).
* Halte HTTP-Status konsistent: 400 für Validierung, 403 für Verboten, 404 nicht gefunden, 410 nicht mehr gültig.

---

#### 10. **Mandantenfähigkeit & Datenmodellierung**

* Mandantenlogik basiert auf Company-IDs aus Business Central. Jeder Handwerker ist einer Firma zugeordnet.
* Datenzugriffe auf BC erfolgen kontextbezogen (per Company). Niemals globale Daten verwenden.
* Neue Entitäten müssen Company- oder Benutzerbezug enthalten. Kein harter Mandantenfilter im Code – dynamisch übergeben.
* Eindeutige Kennung der Nutzer ist deren Login-E-Mail. Kein Mapping auf User-IDs oder Rollen erforderlich.

---

🛠 **Dein Ziel:** Füge Code hinzu, als wärst du ein erfahrener Alleskoenner-Engineer.
Dein Code ist modular, robust, getestet, architekturtreu – und fügt sich reibungslos in ein bestehendes System ein.
Jede deiner Änderungen verbessert Qualität, Konsistenz und Wartbarkeit der Gesamtlösung.
