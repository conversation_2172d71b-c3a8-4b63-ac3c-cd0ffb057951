{"BackendServicePrinciple": {"AuthenticationUrl": "https://login.microsoftonline.com", "BusinessCentralBaseUrl": "https://api.businesscentral.dynamics.com", "TenantId": "b37c212c-6995-45f1-a9ef-cb27c169c009", "AppClientId": "5ca29e2f-8726-4b3f-963c-2e7b84152561", "CronosCompany": {"CompanyId": "3f31093e-1b7a-ef11-ac25-6045bde98d8c"}, "Handyman1Dev": {"CompanyId": "45972317-3811-f011-9346-002248e4ed6a"}, "DevSandbox-Handyman1": {"CompanyId": "2b8901ac-02be-ef11-b8ec-002248e5504c"}, "Alleskoenner24": {"CompanyId": "048db3c3-8ab8-ef11-b8ec-002248e59eea"}, "Security": {"Auth": {"microsoft-login": {"Type": "OAuth2", "Grant Type": "Client Credentials", "Token URL": "https://login.microsoftonline.com/{{TenantId}}/oauth2/v2.0/token", "Client ID": "5ca29e2f-8726-4b3f-963c-2e7b84152561", "Client Secret": "{{ApiClientSecret}}", "Scope": "https://api.businesscentral.dynamics.com/.default"}}}}, "CustomerLogin": {"Security": {"Auth": {"keycloak": {"Type": "OAuth2", "Grant Type": "Authorization Code", "Auth URL": "https://oidc.klosebrothers.de/realms/alleskoenner24/protocol/openid-connect/auth", "Token URL": "https://oidc.klosebrothers.de/realms/alleskoenner24/protocol/openid-connect/token", "Client ID": "alleskoenner24-customer-client", "Scope": "openid profile email", "PKCE": true, "Redirect URL": "http://localhost:3000"}}}}, "HandymanLogin": {"Security": {"Auth": {"keycloak": {"Type": "OAuth2", "Grant Type": "Authorization Code", "Auth URL": "https://oidc.klosebrothers.de/realms/alleskoenner24-handyman/protocol/openid-connect/auth", "Token URL": "https://oidc.klosebrothers.de/realms/alleskoenner24-handyman/protocol/openid-connect/token", "Client ID": "alleskoenner24-handyman-client", "Scope": "openid profile email", "PKCE": true, "Redirect URL": "http://localhost:4000"}}}}}