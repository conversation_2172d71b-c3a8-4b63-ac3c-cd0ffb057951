#!/bin/bash

# -------------------------------
# Konfiguration
# -------------------------------

# Ersetz<PERSON> den Token. Nutze den AuthenticationTokenRequest.http
BEARER_TOKEN="${1:-********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}"
ENVIRONMENT="MainSandbox"
COMPANY_ID="048db3c3-8ab8-ef11-b8ec-002248e59eea"

# API-Endpunkt zum Abrufen von Appointment Requests
FILTER="appointmentStatus eq 'Rejected' or appointmentStatus eq 'Timed Out'"
ENCODED_FILTER=$(jq -rn --arg f "$FILTER" '$f|@uri')
APPT_QUERY_URL="https://api.businesscentral.dynamics.com/v2.0/$ENVIRONMENT/api/ITV/handyman/v2.0/companies($COMPANY_ID)/handymanAppointmentQuery?\$filter=$ENCODED_FILTER"
# API-Endpunkt zum Archivieren
ARCHIVE_URL="https://api.businesscentral.dynamics.com/v2.0/$ENVIRONMENT/ODataV4/AppointmentApi_archiveAppointmentRequest?company=$COMPANY_ID"

# -------------------------------
# Abrufen der relevanten Requests
# -------------------------------

echo "🔍 Abrufen von 'Rejected' oder 'Timed Out' Appointment Requests..."

response=$(curl -s -X GET "$APPT_QUERY_URL" \
  -H "Authorization: Bearer $BEARER_TOKEN" \
  -H "Content-Type: application/json")

# Fehlerbehandlung
if echo "$response" | jq -e '.error' > /dev/null; then
  echo "❌ Fehler beim Abrufen:"
  echo "$response" | jq
  exit 1
fi

# Anzahl prüfen
count=$(echo "$response" | jq '.value | length')
if [ "$count" -eq 0 ]; then
  echo "✅ Keine Requests zum Archivieren gefunden."
  exit 0
fi

echo "📋 $count Requests gefunden:"
echo "----------------------------------------------------------------------------------------------------------------"
printf "%-20s | %-10s | %-10s | %-25s | %-20s | %-20s\n" "Appointment No" "Status" "Customer" "Name" "Erstellt am" "Login E-Mail"
echo "----------------------------------------------------------------------------------------------------------------"

echo "$response" | jq -r '.value[] | [.requestedHandymanAppointmentNo, .appointmentStatus, .customerNo, .sellToName, .systemCreatedAt, .loginEmail] | @tsv' | while IFS=$'\t' read -r apt_no app_stat cust_no name created_at login_email; do
  printf "%-20s | %-10s | %-10s | %-25s | %-20s | %-20s\n" "$apt_no" "$app_stat" "$cust_no" "$name" "$created_at" "$login_email"
done

echo "----------------------------------------------------------------------------------------------------------------"

# -------------------------------
# Benutzerbestätigung
# -------------------------------

echo
read -p "⚠️  Möchtest du diese $count Appointment Requests wirklich archivieren? (y/n): " confirm
if [[ "$confirm" != "y" ]]; then
  echo "🚫 Vorgang abgebrochen. Keine Requests wurden archiviert."
  exit 0
fi

# -------------------------------
# Archivierung durchführen
# -------------------------------

echo "🚀 Starte Archivierung..."

echo "$response" | jq -r '.value[].appointmentSystemId' | while read -r appt_id; do
  echo "➤ Archiviere Appointment Request: $appt_id"

  archive_body="{\"appointmentSystemID\": \"$appt_id\"}"

  response_status=$(curl -s -w "%{http_code}" -o /tmp/archive_response.json -X POST "$ARCHIVE_URL" \
    -H "Authorization: Bearer $BEARER_TOKEN" \
    -H "Content-Type: application/json" \
    -d "$archive_body")

  if [[ "$response_status" == "200" || "$response_status" == "204" ]]; then
    echo "   ✅ Erfolgreich archiviert (Status → Escalated)"
  else
    echo "   ⚠️ Fehler beim Archivieren (HTTP $response_status)"
    echo "   🔍 Antwort:"
    cat /tmp/archive_response.json | jq
  fi
done

echo "🏁 Verarbeitung abgeschlossen."