#!/bin/bash

# -------------------------------
# Konfiguration
# -------------------------------

# E<PERSON><PERSON><PERSON> den Token. Nutze den AuthenticationTokenRequest.http
BEARER_TOKEN="${1:-**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}"

TENANT_ID="b37c212c-6995-45f1-a9ef-cb27c169c009"
ENVIRONMENT="HandymanSandbox"
COMPANY_ID="45972317-3811-f011-9346-002248e4ed6a"
API_VERSION="v2.0"
BASE_URL="https://api.businesscentral.dynamics.com/v2.0/$TENANT_ID/$ENVIRONMENT/api/$API_VERSION/companies($COMPANY_ID)"

# -------------------------------
# Schritt 1: Kunden abrufen
# -------------------------------

echo "🔍 Abrufen der Kunden..."

response=$(curl -s -X GET "$BASE_URL/customers" \
  -H "Authorization: Bearer $BEARER_TOKEN" \
  -H "Content-Type: application/json")

# Fehler prüfen
if echo "$response" | jq -e '.error' > /dev/null; then
  echo "❌ Fehler beim Abrufen:"
  echo "$response" | jq
  exit 1
fi

# Prüfen, ob überhaupt Daten da sind
count=$(echo "$response" | jq '.value | length')
if [ "$count" -eq 0 ]; then
  echo "✅ Keine Kunden zum Löschen gefunden."
  exit 0
fi

# -------------------------------
# Schritt 2: Vorschau anzeigen
# -------------------------------

echo "📋 Gefundene Kunden ($count):"
echo "------------------------------------------------------------"
printf "%-10s | %-25s | %-30s\n" "Nummer" "Name" "E-Mail"
echo "------------------------------------------------------------"

echo "$response" | jq -r '
  .value[] |
  [.number, .displayName, .email] |
  @tsv' | while IFS=$'\t' read -r number name email; do
    printf "%-10s | %-25s | %-30s\n" "$number" "$name" "$email"
done

echo "------------------------------------------------------------"

# -------------------------------
# Schritt 3: Benutzerbestätigung
# -------------------------------

read -p "⚠️  Möchtest du alle diese Kunden wirklich löschen? (y/n): " confirm
if [[ "$confirm" != "y" ]]; then
  echo "🚫 Abgebrochen. Kein Kunde wurde gelöscht."
  exit 0
fi

# -------------------------------
# Schritt 4: Löschvorgang (tolerant)
# -------------------------------

echo "🗑 Starte Löschung..."

echo "$response" | jq -r '.value[] | [.id, .number] | @tsv' | while IFS=$'\t' read -r id number; do
  echo "➤ Lösche Kunde $number (ID: $id)..."
  status=$(curl -s -o /dev/null -w "%{http_code}" -X DELETE "$BASE_URL/customers($id)" \
    -H "Authorization: Bearer $BEARER_TOKEN" \
    -H "Content-Type: application/json")

  if [ "$status" = "204" ]; then
    echo "   ✅ Erfolgreich gelöscht"
  else
    echo "   ⚠️  Nicht gelöscht (HTTP $status) – vermutlich Abhängigkeiten"
  fi
done

echo "✅ Löschvorgang abgeschlossen."
