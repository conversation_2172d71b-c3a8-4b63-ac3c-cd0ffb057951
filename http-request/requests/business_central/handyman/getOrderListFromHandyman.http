# This is the standard sales order which however does not contain the work description
### get sales orders
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/companies({{Handyman1Dev.CompanyId}})/salesOrders
Authorization: Bearer {{$auth.token("microsoft-login")}}

### get single sales order
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/companies({{Handyman1Dev.CompanyId}})/salesOrders(b7c4c562-b816-f011-9346-002248e45714)
Authorization: Bearer {{$auth.token("microsoft-login")}}


# This is our own API extension
### get sales order information with work description - with own API extension
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/itv/handymanApp/v2.0/companies({{Handyman1Dev.CompanyId}})/salesOrderInformationList
Authorization: Bearer {{$auth.token("microsoft-login")}}


### get single sales order information with work description and sales lines - with own API extension
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/itv/handymanApp/v2.0/companies({{Handyman1Dev.CompanyId}})/salesOrderInformationList?
    $expand=salesOrderLines&$filter=orderNo eq '101011'
Authorization: Bearer {{$auth.token("microsoft-login")}}


### get single sales order information with work description - with own API extension
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/itv/handymanApp/v2.0/companies({{Handyman1Dev.CompanyId}})/salesOrderInformationList?
    $filter=orderNo eq '101011'
Authorization: Bearer {{$auth.token("microsoft-login")}}


### post single sales order
POST https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/companies({{Handyman1Dev.CompanyId}})/salesOrders(f8d13c52-fd19-f011-9af4-002248e45714)/salesOrderLines
Authorization: Bearer {{$auth.token("microsoft-login")}}
Content-Type: application/json

{
  "lineType": "Resource",
  "lineObjectNumber": "R0020",
  "quantity": 2,
  "unitPrice": 70.9
}


### patch single sales order
PATCH https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/companies({{Handyman1Dev.CompanyId}})/salesOrderLines(214eb0a4-b81a-f011-9af4-002248e45714)
Authorization: Bearer {{$auth.token("microsoft-login")}}
Content-Type: application/json
If-Match: *

{
  "quantity": 3
}


### We don't need this, but it may be usefull later on

### get work description - the ODATA-way
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/ODataV4/Company('{{Handyman1Dev.CompanyId}}')/salesDocuments(b7c4c562-b816-f011-9346-002248e45714)/workDescription
Authorization: Bearer {{$auth.token("microsoft-login")}}

### get sales documents - the ODATA-way
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/ODataV4/Company('{{Handyman1Dev.CompanyId}}')/salesDocuments
Authorization: Bearer {{$auth.token("microsoft-login")}}

### get metadata
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/$metadata
Authorization: Bearer {{$auth.token("microsoft-login")}}
