### get sales orders
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/DevSandbox/api/v2.0/companies({{DevSandbox-Handyman1.CompanyId}})/salesOrders
Authorization: Bearer {{$auth.token("microsoft-login")}}

### get sales order xyz
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/DevSandbox/api/v2.0/companies({{DevSandbox-Handyman1.CompanyId}})/salesOrders(16178729-2357-f011-8eed-7ced8d04153e)
Authorization: Bearer {{$auth.token("microsoft-login")}}


### post sales order xyz
POST https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/DevSandbox/api/v2.0/companies({{DevSandbox-Handyman1.CompanyId}})/salesOrders(xyz)/Microsoft.NAV.shipAndInvoice
Authorization: Bearer {{$auth.token("microsoft-login")}}
Content-Type: application/json

{}

### Upload Customer Signature
POST https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/DevSandbox
    /ODataV4/HandymanApi_UploadSignature?company={{DevSandbox-Handyman1.CompanyId}}
Authorization: Bearer {{$auth.token("microsoft-login")}}
Content-Type: application/json
If-Match: *

# To make a valid upload request, please adjust and encode the payload.json to base64 format and put it in the base64Payload field below.

{
  "base64Payload": "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"
}

