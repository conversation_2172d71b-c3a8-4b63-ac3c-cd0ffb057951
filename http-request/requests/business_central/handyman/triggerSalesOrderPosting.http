### get sales order xyz
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/companies({{Handyman1Dev.CompanyId}})/salesOrders(xyz)
Authorization: Bearer {{$auth.token("microsoft-login")}}


### post sales order xyz
POST https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/companies({{Handyman1Dev.CompanyId}})/salesOrders(xyz)/Microsoft.NAV.shipAndInvoice
Authorization: Bearer {{$auth.token("microsoft-login")}}
Content-Type: application/json

{}
