# This is the standard sales invoice which however does not contain the work description
### get posted sales orders
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/companies({{Handyman1Dev.CompanyId}})/salesInvoices
Authorization: Bearer {{$auth.token("microsoft-login")}}


# This is our own API extension
### get posted sales order information with work description and sales lines- with own API extension
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/itv/handymanApp/v2.0/companies({{Handyman1Dev.CompanyId}})/postedSalesOrderInformationList?
    $expand=postedSalesOrderLines
Authorization: Bearer {{$auth.token("microsoft-login")}}

### get single posted sales order information with work description and sales lines- with own API extension
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/itv/handymanApp/v2.0/companies(45972317-3811-f011-9346-002248e4ed6a)/postedSalesOrderInformationList?
    $filter=salesOrderNo eq '101017'&$expand=postedSalesOrderLines
Authorization: Bearer {{$auth.token("microsoft-login")}}
