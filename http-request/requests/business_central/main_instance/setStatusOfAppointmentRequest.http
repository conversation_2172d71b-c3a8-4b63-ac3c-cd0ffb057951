### get single appointment request
GET https://api.businesscentral.dynamics.com/v2.0/MainSandbox/api/ITV/appointments/v2.0/companies(048db3c3-8ab8-ef11-b8ec-002248e59eea)/appointmentRequests(f422f08a-68ef-ef11-9345-0022485cbb9c)
Authorization: Bearer {{$auth.token("microsoft-login")}}

### set status of appointment request
PATCH https://api.businesscentral.dynamics.com/v2.0/MainSandbox/api/ITV/appointments/v2.0/companies(048db3c3-8ab8-ef11-b8ec-002248e59eea)/appointmentRequests(fcda2118-fdd7-ef11-8eec-6045bd17c1f1)
Authorization: Bearer {{$auth.token("microsoft-login")}}
Content-Type: application/json
If-Match: *

{
  "status": "Escalated"
}
