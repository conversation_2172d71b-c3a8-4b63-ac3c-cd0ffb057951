### get metadata
GET https://api.businesscentral.dynamics.com/v2.0/MainSandbox/api/ITV/appointments/v2.0/$metadata
Authorization: Bearer {{$auth.token("microsoft-login")}}


### get appointment request
GET https://api.businesscentral.dynamics.com/v2.0/MainSandbox/api/ITV/appointments/v2.0/companies({{ Alleskoenner24.CompanyId }})/appointmentRequests(15ecd462-ba21-f011-9af8-0022485c4a3e)
Authorization: Bearer {{$auth.token("microsoft-login")}}


### sync sales order to handyman
POST https://api.businesscentral.dynamics.com/v2.0/MainSandbox/api/ITV/appointments/v2.0
    /companies({{ Alleskoenner24.CompanyId }})
    /appointmentRequests(c190f24c-d221-f011-9af8-0022485c4a3e)/Microsoft.NAV.sendOrderToHandyman
Authorization: Bearer {{$auth.token("microsoft-login")}}
Content-Type: application/json

{}