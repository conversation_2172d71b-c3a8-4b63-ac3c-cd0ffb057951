#!/bin/bash

# -------------------------------
# Konfiguration
# -------------------------------

# E<PERSON><PERSON><PERSON> den Token. Nutze den AuthenticationTokenRequest.http
BEARER_TOKEN="${1:-**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}"

TENANT_ID="b37c212c-6995-45f1-a9ef-cb27c169c009"
ENVIRONMENT="HandymanSandbox"
COMPANY_ID="45972317-3811-f011-9346-002248e4ed6a"
API_VERSION="v2.0"
BASE_URL="https://api.businesscentral.dynamics.com/v2.0/$TENANT_ID/$ENVIRONMENT/api/$API_VERSION/companies($COMPANY_ID)"

# -------------------------------
# Schritt 1: Sales Orders abrufen
# -------------------------------

echo "🔍 Abrufen der Sales Orders..."

response=$(curl -s -X GET "$BASE_URL/salesOrders" \
  -H "Authorization: Bearer $BEARER_TOKEN" \
  -H "Content-Type: application/json")

# Fehler prüfen
if echo "$response" | jq -e '.error' > /dev/null; then
  echo "❌ Fehler beim Abrufen:"
  echo "$response" | jq
  exit 1
fi

# Prüfen, ob überhaupt Daten da sind
count=$(echo "$response" | jq '.value | length')
if [ "$count" -eq 0 ]; then
  echo "✅ Keine Sales Orders zum Löschen gefunden."
  exit 0
fi

# -------------------------------
# Schritt 2: Vorschau anzeigen
# -------------------------------

echo "📋 Gefundene Sales Orders ($count):"
echo "------------------------------------------------------------"
printf "%-10s | %-20s | %-10s | %-30s\n" "Nummer" "Kunde" "Status" "E-Mail"
echo "------------------------------------------------------------"

# Wichtige Daten ausgeben
echo "$response" | jq -r '
  .value[] |
  [.number, .customerName, .status, .email] |
  @tsv' | while IFS=$'\t' read -r number name status email; do
    printf "%-10s | %-20s | %-10s | %-30s\n" "$number" "$name" "$status" "$email"
done

echo "------------------------------------------------------------"

# -------------------------------
# Schritt 3: Benutzerbestätigung
# -------------------------------

read -p "⚠️  Möchtest du alle diese Sales Orders wirklich löschen? (y/n): " confirm
if [[ "$confirm" != "y" ]]; then
  echo "🚫 Abgebrochen. Keine Sales Order wurde gelöscht."
  exit 0
fi

# -------------------------------
# Schritt 4: Löschvorgang
# -------------------------------

echo "🗑 Starte Löschung..."

echo "$response" | jq -r '.value[].id' | while read -r id; do
  echo "➤ Lösche Sales Order ID: $id"
  status=$(curl -s -o /dev/null -w "%{http_code}" -X DELETE "$BASE_URL/salesOrders($id)" \
    -H "Authorization: Bearer $BEARER_TOKEN" \
    -H "Content-Type: application/json")

  if [ "$status" = "204" ]; then
    echo "   ✅ Erfolgreich gelöscht"
  else
    echo "   ⚠️ Fehler beim Löschen (HTTP $status)"
  fi
done

echo "✅ Löschvorgang abgeschlossen."
