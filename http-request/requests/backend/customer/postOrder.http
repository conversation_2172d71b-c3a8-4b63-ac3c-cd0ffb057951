###
POST http://localhost:8080/order
Content-Type: application/json
Authorization: Bearer {{$auth.token("keycloak")}}

{
  "displayName": "Einar Testet",
  "addressLine1": "Herschelstraße, 10589 Berlin, Deutschland",
  "addressLine2": "",
  "city": "Testingen",
  "postalCode": "10589",
  "phoneNumber": "+49 177 123 45 67",
  "customerType": "Person",
  "taskDescription": "Ich bin Einar, der gern alles testet. Jetzt brauch ich einen, der alles repariert",
  "isConditionsChecked": true,
  "isAsSoonAsPossible": false,
  "appointmentRequestDate": "2035-04-05T07:00:00.000Z",
  "requestedHandymanSkill": ""
}

### dev stage
POST https://deinhandwerker365.klosebrothers.de/api/order
Content-Type: application/json
Authorization: Bearer {{$auth.token("keycloak")}}

{
  "displayName": "Einar Testet",
  "addressLine1": "Herschelstraße, 10589 Berlin, Deutschland",
  "addressLine2": "",
  "city": "Testingen",
  "postalCode": "42042",
  "email": "<EMAIL>",
  "phoneNumber": "+49 177 123 45 67",
  "customerType": "Person",
  "taskDescription": "Ich bin Einar, der gern alles testet. Jetzt brauch ich einen, der alles repariert",
  "isConditionsChecked": true,
  "isAsSoonAsPossible": false,
  "appointmentRequestDate": "2025-04-05T07:00:00.000Z",
  "requestedHandymanSkill": ""
}

### dev stage - asap request
POST https://deinhandwerker365.klosebrothers.de/api/order
Content-Type: application/json
Authorization: Bearer {{$auth.token("keycloak")}}

{
  "displayName": "Intellij Test Requester",
  "addressLine1": "Palisadenstraße 90",
  "addressLine2": "",
  "city": "Berlin",
  "postalCode": "10243",
  "email": "<EMAIL>",
  "phoneNumber": "+49 177 123 45 67",
  "customerType": "Person",
  "taskDescription": "Ich bin ein Intellij Test Requester, der gerne testet.",
  "isConditionsChecked": true,
  "isAsSoonAsPossible": true,
  "appointmentRequestDate": "",
  "requestedHandymanSkill": ""
}
