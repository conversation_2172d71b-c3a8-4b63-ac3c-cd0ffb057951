###
# curl --request POST "https://oidc.klosebrothers.de/auth/realms/alleskoenner24/protocol/openid-connect/token"
#  --header "Content-Type: application/x-www-form-urlencoded"
#  --data "grant_type=password"
#  --data "client_id=magic-link-client"
#  --data "username=magic-link-manager"
#  --data "password=8hFW!b88vkEP#wWZN"
POST https://oidc.klosebrothers.de/realms/alleskoenner24/protocol/openid-connect/token
Content-Type: application/x-www-form-urlencoded

grant_type=password&
client_id=magic-link-client&
username=magic-link-manager&
password=8hFW!b88vkEP#wWZN


###


# curl --request POST https://oidc.klosebrothers.de/auth/realms/test/magic-link
# --header "Accept: application/json"
# --header "Content-Type: application/json"
# --header "Authorization: Bearer <access_token>"
# --data '{"email":"<EMAIL>","client_id":"alleskoenner24-customer-client","redirect_uri":"localhost:3000/summary","expiration_seconds":3600,"force_create":true,"update_profile":true,"update_password":true,"send_email":false}'
POST https://oidc.klosebrothers.de/realms/alleskoenner24/magic-link
Accept: application/json
Authorization: Bearer [paste your token here]
Content-Type: application/json

{
  "email": "<EMAIL>",
  "client_id": "alleskoenner24-customer-client-magic-link",
  "redirect_uri": "http://localhost:3000/login-redirect-magic-link",
  "expiration_seconds": 3600,
  "force_create": true,
  "update_profile": false,
  "update_password": false,
  "send_email": false
}
