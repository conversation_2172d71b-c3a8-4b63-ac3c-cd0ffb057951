### companies
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/companies
Authorization: Bearer {{$auth.token("microsoft-login")}}

### cronos company
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/companies({{Handyman1Dev.CompanyId}})
Authorization: Bearer {{$auth.token("microsoft-login")}}

### get customers
GET https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/companies({{Handyman1Dev.CompanyId}})/contacts
Authorization: Bearer {{$auth.token("microsoft-login")}}

### create a customer
POST https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/HandymanSandbox/api/v2.0/companies({{Handyman1Dev.CompanyId}})/contacts
Authorization: Bearer {{$auth.token("microsoft-login")}}
Content-Type: application/json

{
  "displayName": "John Doeeee",
  "email": "<EMAIL>",
  "phoneNumber": "+1234567890"
}
