# Get an authentication bearer token from microsoft login
POST https://login.microsoftonline.com/{{TenantId}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id = {{AppClientId}} &
scope = https://api.businesscentral.dynamics.com/.default &
client_secret = {{ApiClientSecret}} &
grant_type = client_credentials

> {%
    client.global.set("BUSINESS_CENTRAL_TOKEN", response.body.access_token);
%}
