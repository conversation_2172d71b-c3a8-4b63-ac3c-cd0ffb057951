# Request Collection

This repository contains a collection of requests to access the Business Central API.

## How to use

The private environment variables are encrypted in /env/http-client.private.env.json.
You have to decrypt them to be able to use the requests in this repo.
The password can be requested from <PERSON> as long as we do not have a centralized password storage. 

### Encryption
```shell
openssl aes-256-cbc -a -salt -pbkdf2 -in ./env/http-client.private.env.json -out ./env/http-client.private.env.json.enc
```

### Decryption

```shell
openssl aes-256-cbc -d -a -salt -pbkdf2 -in ./env/http-client.private.env.json.enc -out ./env/http-client.private.env.json
```


### How to use openssl with Windows in any terminal (or IntelliJ terminal)

1. Locate the `openssl.exe` inside your Git installation folder, default: `C:\Program Files\Git\usr\bin\openssl.exe`
2. Add this file (or the whole folder) to your PATH variables
   1. Open the Windows start menu (by pressing the Windows-key)
   2. Search for "env" and select "Edit the system environment variables"
   3. Click the "Environment Variables..." button
   4. Select the `Path` Variable from the list (from either the user's list or the system variables)
   5. Add `C:\Program Files\Git\usr\bin` to the list (or `C:\Program Files\Git\usr\bin\openssl.exe`)
   6. Close all dialog windows with "OK" or "Apply"
   7. Restart your computer and open any terminal