# Alleskoenner Backend

## Start Backend

1. You need to be logged in with the azure CLI by running this command:

```shell
 az login --tenant b37c212c-6995-45f1-a9ef-cb27c169c009
 ```

2. You need to have the right Azure permissions. Ask an Azure admin to give you the following permissions:

Subscription: `Contributor` or `Owner`
Resource Group: `Reader` or `Owner`
Key Vault: At least `Key Vault Reader` and `Key Vault Secrets User`. Or `Key Vault Administrator`.

### Miscellaneous

#### Get Access Tokens for Customer Website

##### Get Customer Access Token directly from Microsoft

```shell
  oauth2c https://login.microsoftonline.com/b37c212c-6995-45f1-a9ef-cb27c169c009/v2.0/.well-known/openid-configuration \
    --client-id "c873764c-7ce4-4eba-96d0-2a1ec182afb4" \
    --client-secret "xxx" \
    --grant-type authorization_code \
    --scopes "openid profile offline_access" \
    --response-mode query \
    --auth-method client_secret_post \
    --response-types code
```

##### Get Customer Access Token from Keycloak via Google

```shell
  oauth2c https://oidc.klosebrothers.de/realms/alleskoenner24/.well-known/openid-configuration   \
    --client-id alleskoenner24-customer-client \
    --response-types code   \
    --response-mode query    \
    --grant-type authorization_code   \
    --auth-method none \
    --pkce  \
    --idp-hint "google"
```

##### Get Customer Access Token from Keycloak via Microsoft

```shell
  oauth2c https://oidc.klosebrothers.de/realms/alleskoenner24/.well-known/openid-configuration \
    --client-id "alleskoenner24-customer-client" \
    --response-types code \
    --response-mode query \
    --grant-type authorization_code \
    --auth-method none \
    --pkce \
    --idp-hint "microsoft"
```

#### Get Access Tokens for Handyman App

##### Get Handyman Access Token directly from Microsoft

```shell
  oauth2c https://login.microsoftonline.com/b37c212c-6995-45f1-a9ef-cb27c169c009/v2.0/.well-known/openid-configuration \
    --client-id "0399dd56-16dc-426e-b3b8-df36344a38f8" \
    --client-secret "xxx" \
    --grant-type authorization_code \
    --scopes "openid profile offline_access" \
    --response-mode query \
    --auth-method client_secret_post \
    --response-types code
```

##### Get Handyman Access Token from Keycloak via Microsoft

```shell
#[query form_post query.jwt form_post.jwt jwt]
  oauth2c https://oidc.klosebrothers.de/realms/alleskoenner24/.well-known/openid-configuration  \
     --client-id alleskoenner24-handyman-client \
     --response-types code \
     --response-mode form_post \
     --grant-type authorization_code  \
     --auth-method none \
     --pkce \
     --scopes "openid profile email"
```
