plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.1'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.alleskoenner'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(23)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

ext {
    set('springCloudAzureVersion', "5.19.0")
    set('springModulithVersion', "1.3.1")
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'com.azure.spring:spring-cloud-azure-starter-actuator'
    implementation 'com.azure.spring:spring-cloud-azure-starter-keyvault'
    implementation 'org.springframework.modulith:spring-modulith-starter-core'
    implementation 'com.azure:azure-communication-email:1.0.19'
    implementation 'io.jstach:jstachio:1.3.6'
    implementation 'com.microsoft.graph:microsoft-graph:6.27.0'
    implementation 'com.azure:azure-identity:1.15.0'
    implementation 'com.eatthepath:pushy:0.15.4'
    implementation("org.springframework.boot:spring-boot-starter-cache:3.4.4")
    implementation 'com.github.ben-manes.caffeine:caffeine:3.2.0'
    compileOnly 'org.projectlombok:lombok'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    runtimeOnly 'org.springframework.modulith:spring-modulith-actuator'
    runtimeOnly 'org.springframework.modulith:spring-modulith-observability'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'io.jstach:jstachio-apt:1.3.6'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'com.squareup.okhttp3:mockwebserver:4.12.0'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'org.springframework.modulith:spring-modulith-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.modulith:spring-modulith-bom:${springModulithVersion}"
        mavenBom "com.azure.spring:spring-cloud-azure-dependencies:${springCloudAzureVersion}"
    }
}

// This mockitoAgent configuration is needed to run tests with Mockito. Else a warning will be shown.
configurations {
    mockitoAgent
}

dependencies {
    mockitoAgent(libs.mockito) {
        transitive = false
    }
}

tasks {
    test {
        useJUnitPlatform()
        jvmArgs += "-javaagent:${configurations.mockitoAgent.asPath}"
    }
}


