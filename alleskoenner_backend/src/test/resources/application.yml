logging:
  level:
    root: INFO
    com:
      alleskoenner: DEBUG
    org:
      springframework:
        web: INFO
        security: INFO

security:
  client:
    customer-client-id: alleskoenner24-customer-client
    handyman-client-id: alleskoenner24-handyman-client

spring:
  application:
    name: Alleskoenner Backend Test
  #  cloud:
  #    azure:
  #      keyvault:
  #        secret:
  #          property-sources:
  #            - name: backend-kv
  #              endpoint: https://backend-kv.vault.azure.net/
  security:
    oauth2:
      client:
        registration:
          business-central:
            client-id: ${business-central.bcClientId}
            client-secret: wolololooo
            authorization-grant-type: client_credentials
            scope: https://api.businesscentral.dynamics.com/.default
            provider: business-central-provider
        provider:
          business-central-provider:
            token-uri: https://alleskoenner24.com/notreal/${business-central.bcTenantId}/oauth2/v2.0/token

# todo: split this into test (sandbox) and production (live) profiles

business-central:
  baseurl: https://api.businesscentral.dynamics.com
  microsoftLogin: https://login.microsoftonline.com
  bcCompanyId: 048db3c3-8ab8-ef11-b8ec-002248e59eea
  bcTenantId: b37c212c-6995-45f1-a9ef-cb27c169c009
  bcEnvironment: MainSandbox
  bcClientId: 5ca29e2f-8726-4b3f-963c-2e7b84152561

shared-config:
  defaultAsapWindow: 24h

appointment-coordination:
  appointmentRequestAcceptanceTimeoutWindow: 15m
  quiet-hours:
    start: "22:01"
    end: "06:00"
  calendar:
    travelTimeToCustomerInMinutes: 30
    fixedDurationOfAppointmentInMinutes: 60
    availabilityViewIntervalInMinutes: 30
    defaultAsapWindow: ${shared-config.defaultAsapWindow}
    staticEmailAddressForApiRequest: "<EMAIL>"
    microsoftGraphClient:
      baseurl: https://graph.microsoft.com/v1.0
      clientId: f5ebc6c4-a71f-4d3b-bb09-357e80626db1
      clientSecret: ${microsoft-graph-api-client-secret}
      scope: https://graph.microsoft.com/.default
      tenant: 056998c4-a0f4-4928-9603-ff846f225d5a
  escalation-job:
    enabled: false
    initialDelayInSeconds: 60
    fixedDelayInSeconds: 60

handyman-invoicing:
  salesOrderPostingEnabled: true
  defaultAsapWindow: ${shared-config.defaultAsapWindow}
  businessCentral:
    baseurl: https://api.businesscentral.dynamics.com
    microsoftLogin: https://login.microsoftonline.com
    bcTenantId: b37c212c-6995-45f1-a9ef-cb27c169c009
    bcEnvironment: HandymanSandbox
    bcClientId: 5ca29e2f-8726-4b3f-963c-2e7b84152561
    bcMainCompanyId: 048db3c3-8ab8-ef11-b8ec-002248e59eea
    bcMainEnvironment: MainSandbox
  defaultSalesOrderLines:
    travelCharge:
      lineType: "Item"
      lineObjectNumber: "1000"
      quantity: 1.0
    defaultWorkingHour:
      lineType: "Resource"
      lineObjectNumber: "R0020"
      quantity: 1.0
    smallPartsCharge:
      lineType: "Item"
      lineObjectNumber: "1003"
      quantity: 1.0


azure:
  email:
    connection-string: endpoint=https://bla.bla.bla/;accesskey=bla
    sender-address: "<EMAIL>"
    internal-business-notification:
      enabled: false
      recipientAddresses:
        - "<EMAIL>"
    internal-error-notification:
      enabled: false
      recipientAddresses:
        - "<EMAIL>"

pushNotification:
  certificateBase64: MIIDCTCCAfGgAwIBAgIUf4DbvgXktsjnmPyJA62trz5OHlowDQYJKoZIhvcNAQELBQAwEzERMA8GA1UEAwwIVGVzdENlcnQwIBcNMjUwMjEzMDg1MzI1WhgPMjA1MjA2MzAwODUzMjVaMBMxETAPBgNVBAMMCFRlc3RDZXJ0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAquj1nj/IFZt8o3Z8qh7CgBC9cG4YwAqO5xZcbwr5C6Nc0q0F1/8JWhfiQYLkmBbctRDeUAI3KrNvNc1mzajNS2RaoKeLcEilEG5E+k7ZE04QZCKJMlzKz88CxQPv+7kTXvLu8u3oB2OJrdCZZkzpJJDLVgrpHEwLuf+DP89khkCMrNmL28XWqCPNdyQxLK1G9NKvV9/ffT9IuNCrB1YyLvMWqpUcpRGhNrSjPSW7EZcp7MrKlt1Jkr+MDEqNkAS8oZ9w7+hNgNr7/oFGQ+Vl9Z88abo/dANK45PgI0IKbfKxHN8VJU3Jd9YFRZWABcLmYQYi1aSgNZxZJC3L47GdxwIDAQABo1MwUTAdBgNVHQ4EFgQUJBBbAaCOU6SQC0LvKXPXidmS+nswHwYDVR0jBBgwFoAUJBBbAaCOU6SQC0LvKXPXidmS+nswDwYDVR0TAQH/BAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAn2mFo4X2LlKgoJShD29jL67u6vwb2jb3VYRdiH05QPM5Je2TcqW7SwKZjQfOCQnV37CVLZ3+0PYkI/HOEczZa17PMHpAYYXh8cIHVwbeU/nmVHADIYSsxzm9FCQT+ZxLDytPTC8z3i7E1Q/HtoRtL6Qa7UxLYbXIe9mDVGoi6goQ5U0Q/lzS5mVEXAr2UAeBvQvr6k16oWaZzrFF31vMsno0oO4Uxeei6OPqDZVjVMeuV5Le4mvrRIPaTQk/cYzHTl7jXdk/hfvSLcgPFz2OcGp3Je8qxhpTcf/GLhuuce1B+rvjS8QxZFSUE7hC4Z9YfvTugJv/xfwb/ED6uH9noQ==
  privateKey: -----BEGIN PRIVATE KEY-----MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCq6PWeP8gVm3yjdnyqHsKAEL1wbhjACo7nFlxvCvkLo1zSrQXX/wlaF+JBguSYFty1EN5QAjcqs281zWbNqM1LZFqgp4twSKUQbkT6TtkTThBkIokyXMrPzwLFA+/7uRNe8u7y7egHY4mt0JlmTOkkkMtWCukcTAu5/4M/z2SGQIys2YvbxdaoI813JDEsrUb00q9X3999P0i40KsHVjIu8xaqlRylEaE2tKM9JbsRlynsysqW3UmSv4wMSo2QBLyhn3Dv6E2A2vv+gUZD5WX1nzxpuj90A0rjk+AjQgpt8rEc3xUlTcl31gVFlYAFwuZhBiLVpKA1nFkkLcvjsZ3HAgMBAAECggEAHLiwiisSz1rFa9z+H0KzEf1+MHY4D5HWqQwPdZPE/YkzEK6GBGH2Kt9HpRW/n7V72qveiFiO98yWvFZr19TtxyUyu7vcSX7TynqVefElNsLQK1vIbZ+9b4XM49oBCu7zy+mMcWO8sf0uxnh/RvtW1M/GdAu6WAiJkfTYFDGgpZTN3sZt2ygaBgfeytTh0o2xm0zWWa/oMwKFTcCYg0DQmRfyd+gLM/QGQv/ZnRZ6YCGcqQYrr5PjW4f6IXW+cgtlECZAgi3WEsO0Hl7smnwn+8XIdbcXQ1MYnd+vZSIw0mAHgn+GG0bfhRJPaRqKBx2NtXU7MB4LlYAFtOee64ZkVQKBgQDj+H880bd+Ev4EXB7SqYVR7Q64zSHmsmUHwwqCRvvzxeMAlWfc2da4FGbaSfVo+x0f6WvqDMjFZX5hOIWg4j4zA1vKM8C6q08ZktXWNyPJQafWm3XB7rekUcCLGxKBy6NTzHzs1cn7+aVZJxgchQYBSMcPW9v5rl4EX/bj1j0/XQKBgQC/7HIYnGoEV7FBQAiG3aI9vQNwgPNF//vSnQ2j++h9eVsg8+n9U7/otBPGxDXIrVroidGp6OQ6Xd+nc9TrMjOS2FsW480Z7VeQgZx4Ejlp2Kms3/p7NbjmVz48082JhwNeyhPhU9O+hmM9kloH/KOh7ioGan7Miwqbo8UDkf9TcwKBgQDeiYpzbWlIyfNrgB2amV8KnXWckqqAUC7th8ivaemzir3QqQgtf/zpzwikGERXSaKhrzPn0rj+cNgQwaM4bQI4vBOTbDe8QVRC66KQrVzirpPPnAw01vovjpp9cFz9lTMHr3tXmk7FCWsOf2o7FOPKxyeCZ7JxpnQioycYO9+oBQKBgAMH7elblwUIiYEZTMkseOJ2uLYvIyho2YZC+rMrSEmq//huQbTBfNdk+Kb79El+C2B+xLPlPK17TAvt9V/dhBj+l7mt0VBmgiqDEYCH8SYtyMg1JVSKzae9mvsE8UsJudDmahf15fMtViXjyM41fDQTedBuICCE1Z8NoA/lX13nAoGAGPXlNyDNjQo9ZS69Dv8cekmhqfpqf+zLFdSZLaXHIyvULURtcWUViXife1UhhH7Lk77dLCyv3wJX4cTe5PEjeJblwVwlMtb5OoPYWh6JnnLlvD1mwrw/HTWsfXBUf/3nReL0RcqDC2sjTJ7GY/0Ti+LZw2wmCj22W32dNHHPz8g=-----END PRIVATE KEY-----
  notificationTopic: com.alleskoenner24.handwerker.app
  appointmentRequestNotificationRetry:
    maximum-attempts: 3
    maximum-backoff-duration: 300ms
    minimum-backoff-duration: 10ms