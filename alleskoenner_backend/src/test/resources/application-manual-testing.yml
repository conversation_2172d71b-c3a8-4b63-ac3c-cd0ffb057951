logging:
  level:
    com:
      alleskoenner: DEBUG
    org:
      springframework:
        web: DEBUG
        security: DEBUG

spring:
  application:
    name: Alleskoenner Backend Test
  cloud:
    azure:
      keyvault:
        secret:
          property-sources:
            - name: backend-kv
              endpoint: https://backend-kv.vault.azure.net/
  security:
    oauth2:
      client:
        registration:
          business-central:
            client-id: ${business-central.bcClientId}
            client-secret: ${business-central-client-secret}
            authorization-grant-type: client_credentials
            scope: https://api.businesscentral.dynamics.com/.default
            provider: business-central-manual-provider
        provider:
          business-central-manual-provider:
            token-uri: ${business-central.microsoftLogin}/${business-central.bcTenantId}/oauth2/v2.0/token

# todo: split this into test (sandbox) and production (live) profiles

business-central:
  baseurl: https://api.businesscentral.dynamics.com
  microsoftLogin: https://login.microsoftonline.com
  bcCompanyId: 048db3c3-8ab8-ef11-b8ec-002248e59eea
  bcTenantId: b37c212c-6995-45f1-a9ef-cb27c169c009
  bcEnvironment: MainSandbox
  bcClientId: 5ca29e2f-8726-4b3f-963c-2e7b84152561

business-central-appointment-coordination:
  baseurl: https://api.businesscentral.dynamics.com
  microsoftLogin: https://login.microsoftonline.com
  bcCompanyId: 048db3c3-8ab8-ef11-b8ec-002248e59eea
  bcTenantId: b37c212c-6995-45f1-a9ef-cb27c169c009
  bcEnvironment: MainSandbox
  bcClientId: 5ca29e2f-8726-4b3f-963c-2e7b84152561

shared-config:
  defaultAsapWindow: 24h

appointment-coordination:
  appointmentRequestAcceptanceTimeoutWindow: 15m
  quiet-hours:
    start: "22:01"
    end: "06:00"
  calendar:
    travelTimeToCustomerInMinutes: 30
    fixedDurationOfAppointmentInMinutes: 60
    availabilityViewIntervalInMinutes: 30
    defaultAsapWindow: ${shared-config.defaultAsapWindow}
    staticEmailAddressForApiRequest: "<EMAIL>"
    microsoftGraphClient:
      baseurl: https://graph.microsoft.com/v1.0
      clientId: f5ebc6c4-a71f-4d3b-bb09-357e80626db1
      clientSecret: ${microsoft-graph-api-client-secret}
      scope: https://graph.microsoft.com/.default
      tenant: 056998c4-a0f4-4928-9603-ff846f225d5a
  escalation-job:
    enabled: false
    initialDelayInSeconds: 60
    fixedDelayInSeconds: 60

handyman-invoicing:
  salesOrderPostingEnabled: false
  defaultAsapWindow: ${shared-config.defaultAsapWindow}
  businessCentral:
    baseurl: https://api.businesscentral.dynamics.com
    microsoftLogin: https://login.microsoftonline.com
    bcTenantId: b37c212c-6995-45f1-a9ef-cb27c169c009
    bcEnvironment: HandymanSandbox
    bcClientId: 5ca29e2f-8726-4b3f-963c-2e7b84152561
    bcMainCompanyId: 048db3c3-8ab8-ef11-b8ec-002248e59eea
    bcMainEnvironment: MainSandbox
  defaultSalesOrderLines:
    travelCharge:
      lineType: "Item"
      lineObjectNumber: "1000"
      quantity: 1.0
    defaultWorkingHour:
      lineType: "Resource"
      lineObjectNumber: "R0020"
      quantity: 1.0
    smallPartsCharge:
      lineType: "Item"
      lineObjectNumber: "1003"
      quantity: 1.0

azure:
  email:
    connection-string: endpoint=https://bla.bla.bla/;accesskey=bla
    sender-address: "<EMAIL>"
    internal-business-notification:
      enabled: false
      recipientAddresses:
        - "<EMAIL>"
    internal-error-notification:
      enabled: false
      recipientAddresses:
        - "<EMAIL>"

pushNotification:
  certificateBase64: ${apple-push-notification-service-certificate-base64}
  privateKey: ${apple-push-notification-service-certificate-private-key}
  notificationTopic: com.alleskoenner24.handwerker.app
  appointmentRequestNotificationRetry:
    maximum-attempts: 3
    maximum-backoff-duration: 300ms
    minimum-backoff-duration: 10ms