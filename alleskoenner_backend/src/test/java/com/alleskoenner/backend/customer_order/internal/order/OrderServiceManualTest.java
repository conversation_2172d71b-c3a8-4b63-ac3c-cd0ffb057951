package com.alleskoenner.backend.customer_order.internal.order;

import java.time.Duration;
import java.time.temporal.ChronoUnit;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;

@SpringBootTest
@Profile("manual-testing")
@Disabled("This test is only for manually triggering an order. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class OrderServiceManualTest {

    @Autowired
    OrderService service;

    @Test
    @DisplayName("Manually make an order request")
    void manuallyCreateOrder() {
        final var email = "<EMAIL>";
        var orderDetails = new OrderDetails(
                "<PERSON> Doe",
                "Dies und Das Straße 1",
                "Suite 5A",
                "<PERSON>örlin",
                "10243",
                email,
                "+0123456789",
                "<PERSON>",
                "Dies und Das Straße 1",
                "Suite 5A",
                "Börlin",
                "10243",
                email,
                "+0123456789",
                "Person",
                "other",
                "Manueller Test über Spring Backend.",
                true,
                true,
                null,
                "",
                new OrderEmail(email)
        );

        service.createOrder(orderDetails)

                .doOnSuccess(System.out::println)
                .delayElement(Duration.of(1L, ChronoUnit.MINUTES))
                .block();
    }
}
