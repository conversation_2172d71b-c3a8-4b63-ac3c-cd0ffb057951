package com.alleskoenner.backend.customer_order.internal.user_details;

import com.alleskoenner.backend.TestSecurityConfig;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.OIDCUserMock;
import com.alleskoenner.backend.shared.UserFacingErrorMessage;
import com.alleskoenner.backend.shared.UserFacingException;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

@WebFluxTest(UserDetailsController.class)
@Import( {TestSecurityConfig.class, OIDCUserMock.class})
@ActiveProfiles("test")
class UserDetailsControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @Autowired
    private OIDCUserMock oidcUserMock;

    @MockitoBean
    private UserDetailsService userDetailsService;

    @Test
    void itShouldReturnUserDetailsWhenRequestIsSuccessful() {
        UserDetailResponse userDetailResponse = new UserDetailResponse(
                "Test User",
                "Person",
                "Street 1",
                "Apt 2",
                "Berlin",
                "Germany",
                "12345",
                "123456789"
        );

        when(userDetailsService.getUserDetails(Mockito.any(LoginEmail.class)))
                .thenReturn(Mono.just(userDetailResponse));

        webTestClient
                .mutateWith(oidcUserMock.customer("<EMAIL>"))
                .get()
                .uri("/customer/details")
                .exchange()
                .expectStatus().isOk()
                .expectBody(UserDetailResponse.class)
                .value(response -> {
                    assertThat(response.displayName()).isEqualTo("Test User");
                    assertThat(response.type()).isEqualTo("Person");
                    assertThat(response.addressLine1()).isEqualTo("Street 1");
                    assertThat(response.addressLine2()).isEqualTo("Apt 2");
                    assertThat(response.city()).isEqualTo("Berlin");
                    assertThat(response.country()).isEqualTo("Germany");
                    assertThat(response.postalCode()).isEqualTo("12345");
                    assertThat(response.phoneNumber()).isEqualTo("123456789");
                });
    }

    @Test
    void itShouldReturnNotFoundWhenUserDoesNotExist() {
        when(userDetailsService.getUserDetails(Mockito.any(LoginEmail.class)))
                .thenReturn(Mono.error(new UserFacingException(HttpStatus.NOT_FOUND, "Customer not found. Probably not yet created.")));

        webTestClient
                .mutateWith(oidcUserMock.customer("<EMAIL>"))
                .get()
                .uri("/customer/details")
                .exchange()
                .expectStatus().isNotFound()
                .expectBody(UserFacingErrorMessage.class)
                .value(msg -> assertThat(msg.getMessage()).isEqualTo("Customer not found. Probably not yet created."));
    }

    @Test
    void itShouldReturnInternalServerErrorOnUnexpectedException() {
        when(userDetailsService.getUserDetails(Mockito.any(LoginEmail.class)))
                .thenReturn(Mono.error(new RuntimeException("Unexpected error")));

        webTestClient
                .mutateWith(oidcUserMock.customer("<EMAIL>"))
                .get()
                .uri("/customer/details")
                .exchange()
                .expectStatus().is5xxServerError()
                .expectBody(UserFacingErrorMessage.class)
                .value(msg -> assertThat(msg.getMessage()).isEqualTo("An error occurred while fetching user details."));
    }

    @Test
    void itShouldFailWhenTokenIsNotSet() {
        webTestClient
                .get()
                .uri("/customer/details")
                .exchange()
                .expectStatus().isUnauthorized();
    }
}