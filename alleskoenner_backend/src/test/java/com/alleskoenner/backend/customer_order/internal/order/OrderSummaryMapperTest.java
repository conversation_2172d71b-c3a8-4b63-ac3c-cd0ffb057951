package com.alleskoenner.backend.customer_order.internal.order;

import java.time.ZonedDateTime;

import com.alleskoenner.backend.customer_order.internal.order.business_central.BusinessCentralOrderResponse;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatusCode;

@ExtendWith(MockitoExtension.class)
class OrderSummaryMapperTest {

    @InjectMocks
    private OrderSummaryMapper orderSummaryMapper;

    @Test
    void shouldSuccessfullyMap() {
        OrderSummary expectedOrderSummary = new OrderSummary(
                "12345",
                "<PERSON> Doe",
                "123 Main St",
                "Apt 4",
                "Springfield",
                "12345",
                "<EMAIL>",
                "123-456-7890",
                "<PERSON>",
                "123 Main St",
                "Apt 4",
                "Springfield",
                "12345",
                "<EMAIL>",
                "123-456-7890",
                "<EMAIL>",
                "Person",
                "other",
                "Task description",
                true
        );

        OrderDetails orderDetails = new OrderDetails(
                "<PERSON> Doe",
                "123 Main St",
                "Apt 4",
                "Springfield",
                "12345",
                "<EMAIL>",
                "123-456-7890",
                "John Doe",
                "123 Main St",
                "Apt 4",
                "Springfield",
                "12345",
                "<EMAIL>",
                "123-456-7890",
                "Person",
                "other",
                "Task description",
                true,
                false,
                ZonedDateTime.parse("2021-12-31T08:00:00+01:00"),
                "Handyman skill",
                new OrderEmail("<EMAIL>"));

        var businessCentralOrderResponse = new BusinessCentralOrderResponse(
                HttpStatusCode.valueOf(200),
                "12345",
                "appointmentRequestId"
        );

        OrderSummary orderSummary = orderSummaryMapper.mapBusinessCentralResponseToOrderSummary(orderDetails, businessCentralOrderResponse);

        assertThat(orderSummary).usingRecursiveComparison().isEqualTo(expectedOrderSummary);
    }
}
