package com.alleskoenner.backend.customer_order.internal.order;

import java.time.ZonedDateTime;
import java.util.Set;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

class OrderDetailsRequestValidationTest {

    private static Validator validator;

    @BeforeAll
    public static void setUp() {
        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            validator = factory.getValidator();
        }
    }

    @Test
    public void testValidOrderDetailsRequest() {
        final var email = "<EMAIL>";
        OrderDetailsRequest orderDetails = new OrderDetailsRequest(
                "<PERSON> Mustermann",
                "Musterstraße 1",
                "Etage 2",
                "Musterstadt",
                "12345",
                email,
                "0123456789",
                "<PERSON> Mustermann",
                "Musterstraße 1",
                "Etage 2",
                "Musterstadt",
                "12345",
                email,
                "0123456789",
                email,
                "Person",
                "other",
                "Reparatur der Heizung",
                true,
                false,
                ZonedDateTime.now().plusMinutes(1),
                ""
        );

        Set<ConstraintViolation<OrderDetailsRequest>> violations = validator.validate(orderDetails);
        assertEquals(0, violations.size());
    }

    @Test
    public void testInvalidOrderDetailsRequest() {
        OrderDetailsRequest orderDetailsRequest = new OrderDetailsRequest(
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                null,
                null,
                null,
                ""
        );

        Set<ConstraintViolation<OrderDetailsRequest>> violations = validator.validate(orderDetailsRequest);

        assertThat(violations).anyMatch(v -> v.getMessage().equals("shipToCity is required"));
        assertThat(violations).anyMatch(v -> v.getMessage().equals("shipToAddressLine1 is required"));
        assertThat(violations).anyMatch(v -> v.getMessage().equals("Task category must not be empty"));
        assertThat(violations).anyMatch(v -> v.getMessage().equals("Task description must not be empty"));
        assertThat(violations).anyMatch(v -> v.getMessage().equals("shipToName is required"));
        assertThat(violations).anyMatch(v -> v.getMessage().equals("shipToPostalCode is required"));
        assertThat(violations).anyMatch(v -> v.getMessage().equals("Customer type is required"));
        assertThat(violations).anyMatch(v -> v.getMessage().equals("Customer type must be 'Person'"));
        assertThat(violations).anyMatch(v -> v.getMessage().equals("Conditions must be checked"));
        assertThat(violations).anyMatch(v -> v.getMessage().equals("ASAP must be true or false"));
        System.out.println(violations);
        assertEquals(17, violations.size());
    }
}
