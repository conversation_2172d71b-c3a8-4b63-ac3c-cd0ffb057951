package com.alleskoenner.backend.customer_order.internal.user_details.business_central;

import com.alleskoenner.backend.customer_order.internal.business_central.BusinessCentralConfig;
import com.alleskoenner.backend.shared.LoginEmail;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class BusinessCentralCustomerUrlBuilderTest {
    @Test
    void buildOrderRequestUrl_shouldReturnCorrectUrl() {
        BusinessCentralConfig config = mock(BusinessCentralConfig.class, RETURNS_DEEP_STUBS);
        when(config.getBcEnvironment()).thenReturn("sandbox");
        when(config.getBcCompanyId()).thenReturn("company-123");
        BusinessCentralCustomerUrlBuilder urlBuilder = new BusinessCentralCustomerUrlBuilder(config);

        LoginEmail email = new LoginEmail("<EMAIL>");
        String url = urlBuilder.buildCustomerRequestUrl(email);

        assertThat(url).isEqualTo("v2.0/sandbox/api/v2.0/companies(company-123)/customers?$filter=email eq '<EMAIL>'");
    }
}
