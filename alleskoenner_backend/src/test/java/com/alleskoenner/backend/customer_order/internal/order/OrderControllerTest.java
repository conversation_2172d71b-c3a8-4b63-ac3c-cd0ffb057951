package com.alleskoenner.backend.customer_order.internal.order;

import java.time.ZonedDateTime;
import java.util.List;

import com.alleskoenner.backend.TestSecurityConfig;
import com.alleskoenner.backend.shared.OIDCUserMock;
import com.alleskoenner.backend.shared.UserFacingErrorMessage;
import static org.assertj.core.api.Assertions.assertThat;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

@ActiveProfiles(value = "test")
@WebFluxTest(OrderController.class)
@Import(TestSecurityConfig.class)
public class OrderControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @SuppressWarnings("unused")
    @MockitoBean
    private OrderService orderService;

    @Autowired
    private OIDCUserMock oidcUserMock;

    @Test
    public void itShouldCreateOrderWithValidData() {
        mockReturningOrderSummary();

        webTestClient
                .mutateWith(oidcUserMock.customer("<EMAIL>"))
                .post()
                .uri("/order")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(getOrderDetailsRequest())
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .json("""
                            {
                              "salesOrderNumber": "SO1234",
                              "shipToName": "Person Test 3",
                              "shipToAddressLine1": "123 Main St",
                              "shipToAddressLine2": "Apt 4B",
                              "shipToCity": "Springfield",
                              "shipToPostalCode": "12345",
                              "shipToEmail": "<EMAIL>",
                              "shipToPhoneNumber": "+013456789",
                              "billToName": "Person Test 3",
                              "billToAddressLine1": "123 Main St",
                              "billToAddressLine2": "Apt 4B",
                              "billToCity": "Springfield",
                              "billToPostalCode": "12345",
                              "billToEmail": "<EMAIL>",
                              "billToPhoneNumber": "+013456789",
                              "email": "<EMAIL>",
                              "type": "Person",
                              "taskCategory": "other",
                              "taskDescription": "Fix the leaking faucet in the kitchen.",
                              "isConditionsChecked": true
                            }
                        """);
    }

    @Test
    public void itShouldCreateOrderWithInvalidData() {
        webTestClient
                .mutateWith(oidcUserMock.customer("<EMAIL>"))
                .post()
                .uri("/order")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(getInvalidOrderDetailsRequest())
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody(UserFacingErrorMessage.class)
                .value(msg -> {
                    var expectedMessages = List.of(
                            "shipToCity is required",
                            "shipToEmail must not be empty",
                            "billToCity is required",
                            "billToName is required",
                            "email must not be empty",
                            "billToAddressLine1 is required",
                            "ASAP must be true or false",
                            "shipToPostalCode is required",
                            "Task category must not be empty",
                            "Task description must not be empty",
                            "shipToName is required",
                            "Customer type is required",
                            "billToPostalCode is required",
                            "billToEmail must not be empty",
                            "Customer type must be 'Person'",
                            "shipToAddressLine1 is required",
                            "Conditions must be checked"
                    );
                    expectedMessages.forEach(expectedMessage -> assertThat(msg.getMessage()).contains(expectedMessage));
                });
    }

    @Test
    public void itShouldCreateOrderWhenServiceRunsIntoException() {
        var mockedException = new RuntimeException("Some errors that are not propagated to the response.");
        when(orderService.createOrder(any(OrderDetails.class))).thenReturn(Mono.error(mockedException));

        webTestClient
                .mutateWith(oidcUserMock.customer("<EMAIL>"))
                .post()
                .uri("/order")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(getOrderDetailsRequest())
                .exchange()
                .expectStatus().is5xxServerError()
                .expectBody()
                .jsonPath("$.timestamp").isNotEmpty()
                .jsonPath("$.path").isEqualTo("/order")
                .jsonPath("$.status").isEqualTo("500")
                .jsonPath("$.error").isEqualTo("Internal Server Error")
                .jsonPath("$.requestId").isNotEmpty();
    }

    @Test
    public void itShouldUseEmailAddressFromOrderRequestWhenUserHasCustomerImpersonationRole() {
        mockReturningOrderSummary();

        var email = "<EMAIL>";

        var orderDetailsRequest = new OrderDetailsRequest(
                "John Doe",
                "456 Elm St",
                "Suite 5A",
                "Metropolis",
                "67890",
                email,
                "+0123456789",
                "John Doe",
                "456 Elm St",
                "Suite 5A",
                "Metropolis",
                "67890",
                email,
                "+0123456789",
                email,
                "Person",
                "other",
                "Install new light fixtures.",
                true,
                false,
                ZonedDateTime.now().plusMinutes(1),
                "Electrician"
        );
        webTestClient
                .mutateWith(oidcUserMock.customer("<EMAIL>", "ROLE_CUSTOMER_IMPERSONATION"))
                .post()
                .uri("/order")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(orderDetailsRequest)
                .exchange()
                .expectStatus().isOk();

        var orderDetailsArgumentCaptor = ArgumentCaptor.forClass(OrderDetails.class);
        verify(orderService).createOrder(orderDetailsArgumentCaptor.capture());
        assertThat(orderDetailsArgumentCaptor.getValue().orderEmail().email()).isEqualTo(email);
    }

    private void mockReturningOrderSummary() {
        var email = "<EMAIL>";
        var mockedOrderSummary = new OrderSummary(
                "SO1234",
                "Person Test 3",
                "123 Main St",
                "Apt 4B",
                "Springfield",
                "12345",
                email,
                "+013456789",
                "Person Test 3",
                "123 Main St",
                "Apt 4B",
                "Springfield",
                "12345",
                email,
                "+013456789",
                email,
                "Person",
                "other",
                "Fix the leaking faucet in the kitchen.",
                true
        );
        when(orderService.createOrder(any(OrderDetails.class))).thenReturn(Mono.just(mockedOrderSummary));
    }

    private @NotNull OrderDetailsRequest getInvalidOrderDetailsRequest() {
        return new OrderDetailsRequest(
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                null,
                null,
                null,
                ""
        );
    }

    private @NotNull OrderDetailsRequest getOrderDetailsRequest() {
        var email = "<EMAIL>";
        return new OrderDetailsRequest(
                "John Doe",
                "456 Elm St",
                "Suite 5A",
                "Metropolis",
                "67890",
                email,
                "+0123456789",
                "John Doe",
                "456 Elm St",
                "Suite 5A",
                "Metropolis",
                "67890",
                email,
                "+0123456789",
                email,
                "Person",
                "other",
                "Install new light fixtures.",
                true,
                false,
                ZonedDateTime.now().plusMinutes(1),
                "Electrician"
        );
    }


}
