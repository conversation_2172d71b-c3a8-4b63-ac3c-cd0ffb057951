package com.alleskoenner.backend.customer_order.internal.order.business_central;

import java.io.IOException;
import java.time.ZonedDateTime;

import com.alleskoenner.backend.customer_order.internal.order.OrderDetails;
import com.alleskoenner.backend.customer_order.internal.order.OrderEmail;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import static org.assertj.core.api.Assertions.assertThat;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.test.StepVerifier;

class BusinessCentralCustomerClientTest {

    private MockWebServer mockWebServer;
    private BusinessCentralClient businessCentralClient;
    private final BusinessCentralOrderRequestMapper businessCentralOrderRequestMapper = new BusinessCentralOrderRequestMapper();

    @NotNull
    private static OrderDetails getOrderDetails(String requestedHandymanSkill) {
        final var email = "<EMAIL>";
        return new OrderDetails(
                "Person Test 3",
                "123 Main St",
                "Apt 4B",
                "Springfield",
                "12345",
                email,
                "+013456789",
                "Person Test 3",
                "123 Main St",
                "Apt 4B",
                "Springfield",
                "12345",
                email,
                "+013456789",
                "Person",
                "other",
                "Fix the leaking faucet in the kitchen.",
                true,
                false,
                ZonedDateTime.parse("2025-01-15T08:00:00+01:00"),
                requestedHandymanSkill,
                new OrderEmail(email));
    }

    @BeforeEach
    void setUp() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
        WebClient webClient = WebClient.builder().baseUrl(mockWebServer.url("/").toString()).build();
        businessCentralClient = new BusinessCentralClient(webClient, Mockito.mock(BusinessCentralUrlBuilder.class), businessCentralOrderRequestMapper);
    }

    @AfterEach
    void tearDown() throws IOException {
        mockWebServer.shutdown();
    }

    @Test
    void sendOrderRequest_successfulResponse() {
        OrderDetails orderRequestPayload = getOrderDetails("");

        // language=JSON
        var businessCentralJsonResponseObject = """
                {
                  "id" : 0,
                  "addressLine1" : "123 Main St",
                  "addressLine2" : "Apt 4B",
                  "appointmentRequestDate" : "2025-01-15",
                  "city" : "Springfield",
                  "displayName" : "Person Test 3",
                  "customerNo" : "D00070",
                  "customerID" : "40e1a358-f9d7-ef11-8eec-0022485d42cc",
                  "appointmentNo" : "APTM-00000006",
                  "appointmentSystemID" : "0b43a299-01d8-ef11-8eec-6045bd17c1f1",
                  "salesOrderNo" : "101006",
                  "salesOrderID" : "0943a299-01d8-ef11-8eec-6045bd17c1f1",
                  "email" : "<EMAIL>",
                  "phoneNumber" : "+013456789",
                  "postalCode" : "12345",
                  "requestedHandymanSkill" : "",
                  "taskCategory": "other",
                  "taskDescription" : "Fix the leaking faucet in the kitchen.",
                  "customerType" : "Person",
                  "isConditionsChecked" : true
                }""";

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setBody(businessCentralJsonResponseObject)
                .addHeader("Content-Type", "application/json"));

        StepVerifier.create(businessCentralClient.sendOrderRequest(orderRequestPayload))
                .expectNextMatches(response -> response.orderNumber().equals("101006"))
                .verifyComplete();
    }

    @Test
    void sendCustomerRequest_errorResponse() {
        OrderDetails orderRequestPayload = getOrderDetails("ASDF");

        String exampleErrorMessage = "\"The field Requested Handyman Skill of table Appointments Creation contains a value (ASDF) that cannot be found in the related table (Handyman Skills).  CorrelationId:  7fc2e2bb-dfb8-4436-894a-d1d66acc96bd.\"";
        // language=JSON
        var businessCentralJsonResponseObject = "{\n" +
                "  \"error\": {\n" +
                "    \"code\": \"Internal_InvalidTableRelation\",\n" +
                "    \"message\": " + exampleErrorMessage + "\n" +
                "  }\n" +
                "}";

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(400)
                .setBody(businessCentralJsonResponseObject)
                .addHeader("Content-Type", "application/json"));


        StepVerifier.create(businessCentralClient.sendOrderRequest(orderRequestPayload))
                .expectErrorSatisfies(throwable -> {
                    assertThat(throwable).isInstanceOf(WebClientResponseException.class);
                    WebClientResponseException exception = (WebClientResponseException) throwable;
                    assertThat(exception.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
                    // language=JSON
                    String errorMessage = "{\n" +
                            "\"error\": {\n" +
                            "\"code\": \"Internal_InvalidTableRelation\",\n" +
                            "    \"message\": " + exampleErrorMessage + "\n" +
                            "  }\n" +
                            "}";
                    String responseBody = exception.getResponseBodyAsString().replace(" ", "");
                    String expectedErrorMessage = errorMessage.replace(" ", "");
                    assertThat(responseBody).contains(expectedErrorMessage);
                })
                .verify();
    }
}
