package com.alleskoenner.backend.customer_order.internal.user_details.business_central;

import com.alleskoenner.backend.shared.LoginEmail;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import static org.assertj.core.api.Assertions.assertThat;
import org.assertj.core.api.SoftAssertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;
import java.io.IOException;

class CustomerBCClientTest {
    static MockWebServer mockWebServer;
    BusinessCentralCustomerClient client;
    BusinessCentralCustomerUrlBuilder urlBuilder;
    final LoginEmail customerEmail = new LoginEmail("<EMAIL>");

    @BeforeAll
    static void startServer() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
    }

    @AfterAll
    static void shutdownServer() throws IOException {
        mockWebServer.shutdown();
    }

    @BeforeEach
    void setUp() {
        WebClient webClient = WebClient.builder().baseUrl(mockWebServer.url("/").toString()).build();
        urlBuilder = mock(BusinessCentralCustomerUrlBuilder.class);
        when(urlBuilder.buildCustomerRequestUrl(customerEmail)).thenReturn("/customer-url");
        client = new BusinessCentralCustomerClient(webClient, urlBuilder);
    }

    @Test
    void sendCustomerRequest_shouldReturnCustomerList_whenResponseIsValid() {
        String responseBody = """
                {
                  "@odata.context": "https://api.businesscentral.dynamics.com/v2.0/sandbox/api/v2.0/$metadata#companies('company-123')/customers",
                  "value": [
                    {
                      "@odata.etag": "W/\\"JzIwOzE2MzI0ODQwNDU3MTc5NjA4ODkxMTswMDsn\\"",
                      "id": "customer-123",
                      "number": "C00123",
                      "displayName": "Test User",
                      "type": "Person",
                      "addressLine1": "Street 1",
                      "addressLine2": "Apt 2",
                      "city": "Berlin",
                      "state": "Berlin",
                      "country": "Germany",
                      "postalCode": "12345",
                      "phoneNumber": "*********",
                      "email": "<EMAIL>",
                      "website": "website.com",
                      "salespersonCode": "sales-123",
                      "balanceDue": 0,
                      "creditLimit": 1000,
                      "taxLiable": true,
                      "taxAreaId": "tax-123",
                      "taxAreaDisplayName": "Tax Area",
                      "taxRegistrationNumber": "tax-reg-123",
                      "currencyId": "currency-123",
                      "currencyCode": "EUR",
                      "paymentTermsId": "payment-terms-123",
                      "shipmentMethodId": "shipment-123",
                      "paymentMethodId": "payment-123",
                      "blocked": "",
                      "lastModifiedDateTime": "2023-01-01T00:00:00Z"
                    }
                  ]
                }
                """;

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .setBody(responseBody));

        var result = client.sendCustomerRequest(customerEmail);

        StepVerifier.create(result)
                .consumeNextWith(customers -> {
                    assertThat(customers).hasSize(1);
                    var customer = customers.getFirst();
                    assertThat(customer.id()).isEqualTo("customer-123");
                    assertThat(customer.displayName()).isEqualTo("Test User");
                    assertThat(customer.type()).isEqualTo("Person");
                    assertThat(customer.addressLine1()).isEqualTo("Street 1");
                    assertThat(customer.addressLine2()).isEqualTo("Apt 2");
                    assertThat(customer.city()).isEqualTo("Berlin");
                    assertThat(customer.country()).isEqualTo("Germany");
                    assertThat(customer.postalCode()).isEqualTo("12345");
                    assertThat(customer.phoneNumber()).isEqualTo("*********");
                    assertThat(customer.email()).isEqualTo("<EMAIL>");
                })
                .verifyComplete();
    }

    @Test
    void sendCustomerRequest_shouldReturnEmptyList_whenNoCustomersFound() {
        String responseBody = """
                {
                  "@odata.context": "https://api.businesscentral.dynamics.com/v2.0/sandbox/api/v2.0/$metadata#companies('company-123')/customers",
                  "value": []
                }
                """;

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .setBody(responseBody));

        var result = client.sendCustomerRequest(customerEmail);

        StepVerifier.create(result)
                .consumeNextWith(customers -> {
                    assertThat(customers).isEmpty();
                })
                .verifyComplete();
    }

    @Test
    void sendCustomerRequest_shouldReturnError_whenResponseIsError() {
        String errorBody = "{\"error\":\"Not found\"}";
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(404)
                .setHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .setBody(errorBody));

        var result = client.sendCustomerRequest(customerEmail);

        var assertions = new SoftAssertions();
        StepVerifier.create(result)
                .expectErrorSatisfies(error -> {
                    assertions.assertThat(error.getMessage()).contains("Request failed with status code 404");
                    assertions.assertThat(error.getMessage()).contains("NOT_FOUND");
                })
                .verify();
        assertions.assertAll();
    }
}
