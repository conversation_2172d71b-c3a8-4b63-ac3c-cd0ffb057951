package com.alleskoenner.backend.customer_order.internal.user_details;

import com.alleskoenner.backend.customer_order.internal.user_details.business_central.BusinessCentralCustomerClient;
import com.alleskoenner.backend.customer_order.internal.user_details.business_central.BusinessCentralCustomerResponseValue;
import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingException;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class UserDetailsServiceTest {
    @Mock
    private BusinessCentralCustomerClient bcClient;

    @Mock
    private ErrorNotificationEmailServiceInterface errorNotificationEmailService;

    @InjectMocks
    private UserDetailsService service;

    private final LoginEmail customerEmail = new LoginEmail("<EMAIL>");

    @Test
    void getUserDetails_shouldReturnUserDetails_whenCustomerExists() {
        BusinessCentralCustomerResponseValue customerResponse = createTestCustomerResponse();
        when(bcClient.sendCustomerRequest(customerEmail)).thenReturn(Mono.just(List.of(customerResponse)));

        var result = service.getUserDetails(customerEmail);

        StepVerifier.create(result)
                .consumeNextWith(response -> {
                    assertThat(response.displayName()).isEqualTo("Test User");
                    assertThat(response.type()).isEqualTo("Person");
                    assertThat(response.addressLine1()).isEqualTo("Street 1");
                    assertThat(response.addressLine2()).isEqualTo("Apt 2");
                    assertThat(response.city()).isEqualTo("Berlin");
                    assertThat(response.country()).isEqualTo("Germany");
                    assertThat(response.postalCode()).isEqualTo("12345");
                    assertThat(response.phoneNumber()).isEqualTo("*********");
                })
                .verifyComplete();
    }

    @Test
    void getUserDetails_shouldReturnError_whenNoCustomerFound() {
        when(bcClient.sendCustomerRequest(customerEmail)).thenReturn(Mono.just(Collections.emptyList()));

        var result = service.getUserDetails(customerEmail);

        StepVerifier.create(result)
                .expectErrorSatisfies(error -> {
                    assertThat(error).isInstanceOf(UserFacingException.class);
                    assertThat(((UserFacingException) error).getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
                    assertThat(error.getMessage()).isEqualTo("Customer not found. Probably not yet created.");
                })
                .verify();
    }

    @Test
    void getUserDetails_shouldReturnError_whenMultipleCustomersFound() {
        BusinessCentralCustomerResponseValue customer1 = createTestCustomerResponse();
        BusinessCentralCustomerResponseValue customer2 = createTestCustomerResponse();
        when(bcClient.sendCustomerRequest(customerEmail)).thenReturn(Mono.just(List.of(customer1, customer2)));

        var result = service.getUserDetails(customerEmail);

        StepVerifier.create(result)
                .expectErrorSatisfies(error -> {
                    assertThat(error).isInstanceOf(RuntimeException.class);
                    assertThat(error.getMessage()).isEqualTo("Expected exactly 1 customer, but got 2");
                })
                .verify();
    }

    @Test
    void getUserDetails_shouldSendErrorNotification_whenErrorOccurs() {
        RuntimeException exception = new RuntimeException("Test error");
        when(bcClient.sendCustomerRequest(customerEmail)).thenReturn(Mono.error(exception));

        var result = service.getUserDetails(customerEmail);

        StepVerifier.create(result)
                .expectError(RuntimeException.class)
                .verify();

        verify(errorNotificationEmailService).sendErrorNotificationEmail(exception);
    }

    private BusinessCentralCustomerResponseValue createTestCustomerResponse() {
        return new BusinessCentralCustomerResponseValue(
                "etag",
                "id-123",
                "number-123",
                "Test User",
                "Person",
                "Street 1",
                "Apt 2",
                "Berlin",
                "State",
                "Germany",
                "12345",
                "*********",
                "<EMAIL>",
                "website.com",
                "sales-123",
                BigDecimal.ZERO,
                BigDecimal.valueOf(1000),
                true,
                "tax-123",
                "Tax Area",
                "tax-reg-123",
                "currency-123",
                "EUR",
                "payment-terms-123",
                "shipment-123",
                "payment-123",
                "not-blocked",
                LocalDate.now()
        );
    }
}