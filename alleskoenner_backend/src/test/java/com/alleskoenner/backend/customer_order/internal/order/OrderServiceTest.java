package com.alleskoenner.backend.customer_order.internal.order;

import java.time.ZonedDateTime;

import com.alleskoenner.backend.appointment_coordination.AppointmentCoordinationDetails;
import com.alleskoenner.backend.appointment_coordination.AppointmentCoordinationServiceInterface;
import com.alleskoenner.backend.customer_order.internal.order.business_central.BusinessCentralClient;
import com.alleskoenner.backend.customer_order.internal.order.business_central.BusinessCentralOrderResponse;
import static org.assertj.core.api.Assertions.assertThat;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatusCode;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class OrderServiceTest {

    @Mock private BusinessCentralClient businessCentralClient;
    @Mock private ConfirmationEmailSender confirmationEmailSender;
    @Mock private OrderSummaryMapper orderSummaryMapper;
    @Mock private AppointmentCoordinationServiceInterface appointmentCoordinationService;


    @InjectMocks
    private OrderService orderService;


    @Test
    void verifyOrderCreationSteps() {

        var businessCentralOrderResponse = getMockedBCOrderResponse();
        when(businessCentralClient.sendOrderRequest(any(OrderDetails.class)))
                .thenReturn(Mono.just(businessCentralOrderResponse));
        doNothing()
                .when(confirmationEmailSender).sendConfirmationEmail(any(), any());
        when(orderSummaryMapper.mapBusinessCentralResponseToOrderSummary(any(), any()))
                .thenReturn(getMockOrderSummary());
        when(appointmentCoordinationService.startAppointmentCoordination(any(AppointmentCoordinationDetails.class)))
                .thenReturn(Mono.empty());
        OrderDetails orderDetails = mock(OrderDetails.class);
        when(orderDetails.isAsSoonAsPossible()).thenReturn(false);
        when(orderDetails.appointmentRequestDate()).thenReturn(ZonedDateTime.now());


        Mono<OrderSummary> orderSummaryMono = orderService.createOrder(orderDetails);


        StepVerifier.create(orderSummaryMono)
                .assertNext(orderSummary -> {
                    verify(businessCentralClient)
                            .sendOrderRequest(any(OrderDetails.class));
                    verify(confirmationEmailSender)
                            .sendConfirmationEmail(any(OrderDetails.class), any(BusinessCentralOrderResponse.class));
                    verify(orderSummaryMapper)
                            .mapBusinessCentralResponseToOrderSummary(orderDetails, businessCentralOrderResponse);
                    verify(appointmentCoordinationService)
                            .startAppointmentCoordination(any(AppointmentCoordinationDetails.class));
                    assertThat(orderSummary.getShipToName()).isEqualTo("John Doe");
                })
                .verifyComplete();
    }


    @Test
    void itShouldRespondOrderSummaryEvenWhenSendingEmailOrStartingAppointmentCoordinationFails() {

        when(businessCentralClient.sendOrderRequest(any(OrderDetails.class)))
                .thenReturn(Mono.just(getMockedBCOrderResponse()));
        doThrow(new RuntimeException("This exception should be fine"))
                .when(confirmationEmailSender).sendConfirmationEmail(any(), any());
        when(orderSummaryMapper.mapBusinessCentralResponseToOrderSummary(any(), any()))
                .thenReturn(getMockOrderSummary());
        when(appointmentCoordinationService.startAppointmentCoordination((any())))
                .thenReturn(Mono.error(new RuntimeException("This exception should also be fine")));
        OrderDetails orderDetails = mock(OrderDetails.class);
        when(orderDetails.isAsSoonAsPossible()).thenReturn(false);
        when(orderDetails.appointmentRequestDate()).thenReturn(ZonedDateTime.now());


        Mono<OrderSummary> orderSummaryMono = orderService.createOrder(orderDetails);


        StepVerifier.create(orderSummaryMono)
                .assertNext(orderSummary -> assertThat(orderSummary.getShipToName()).isEqualTo("John Doe"))
                .verifyComplete();
    }

    @NotNull
    private static OrderSummary getMockOrderSummary() {
        return new OrderSummary(
                "12345",
                "John Doe",
                "123 Main St",
                "Apt 4",
                "Springfield",
                "12345",
                "<EMAIL>",
                "123-456-7890",
                "John Doe",
                "123 Main St",
                "Apt 4",
                "Springfield",
                "12345",
                "<EMAIL>",
                "123-456-7890",
                "<EMAIL>",
                "Person",
                "other",
                "Task description",
                true
        );
    }

    private static BusinessCentralOrderResponse getMockedBCOrderResponse() {
        return new BusinessCentralOrderResponse(HttpStatusCode.valueOf(200), "12345", "appointmentRequestId");
    }

}
