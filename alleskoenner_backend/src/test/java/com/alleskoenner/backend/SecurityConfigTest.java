package com.alleskoenner.backend;

import java.time.Instant;
import java.util.Collection;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.ReactiveJwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.test.util.ReflectionTestUtils;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class SecurityConfigTest {

    @Mock
    private EndpointSecurity endpointSecurity;

    @Mock
    private ServerHttpSecurity http;

    @Mock
    private ServerHttpSecurity.CsrfSpec csrfSpec;

    @Mock
    private ServerHttpSecurity.OAuth2ResourceServerSpec oauth2ResourceServerSpec;

    @Mock
    private ServerHttpSecurity.OAuth2ResourceServerSpec.JwtSpec jwtSpec;

    @Mock
    private SecurityWebFilterChain securityWebFilterChain;

    @Mock
    private ReactiveJwtDecoder mockCustomerJwtDecoder;

    @Mock
    private ReactiveJwtDecoder mockHandymanJwtDecoder;

    @InjectMocks
    private SecurityConfig securityConfig;

    @Test
    void orderSecurityFilterChain_shouldConfigureSecurityCorrectly() {
        when(http.csrf(any())).thenReturn(http);
        when(http.authorizeExchange(any())).thenReturn(http);
        when(http.oauth2ResourceServer(any())).thenReturn(http);
        when(http.build()).thenReturn(securityWebFilterChain);

        var result = securityConfig.orderSecurityFilterChain(http);

        assertNotNull(result);
        verify(http).csrf(any());
        verify(http).authorizeExchange(any());
        verify(http).oauth2ResourceServer(any());
        verify(http).build();
    }

    @Test
    void reactiveJwtAuthenticationConverter_shouldExtractAuthoritiesCorrectly() {
        var converter = securityConfig.reactiveJwtAuthenticationConverter();

        var jwt = Jwt.withTokenValue("token")
                .header("alg", "RS256")
                .claim("azp", "test-client")
                .claim("resource_access", Map.of(
                        "test-client", Map.of(
                                "roles", java.util.List.of("user", "admin")
                        )
                ))
                .issuedAt(Instant.now())
                .expiresAt(Instant.now().plusSeconds(3600))
                .build();

        var result = converter.convert(jwt);

        assertNotNull(result);
        StepVerifier.create(result)
                .assertNext(token -> {
                    assertInstanceOf(JwtAuthenticationToken.class, token);
                    var jwtToken = (JwtAuthenticationToken) token;

                    Collection<? extends GrantedAuthority> authorities = jwtToken.getAuthorities();
                    assertEquals(2, authorities.size());

                    assertTrue(authorities.stream()
                            .anyMatch(auth -> auth.getAuthority().equals("ROLE_USER")));
                    assertTrue(authorities.stream()
                            .anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN")));
                })
                .verifyComplete();
    }

    @Test
    void reactiveJwtAuthenticationConverter_shouldHandleEmptyRoles() {
        var converter = securityConfig.reactiveJwtAuthenticationConverter();

        var jwt = Jwt.withTokenValue("token")
                .header("alg", "RS256")
                .claim("azp", "test-client")
                .issuedAt(Instant.now())
                .expiresAt(Instant.now().plusSeconds(3600))
                .build();

        var result = converter.convert(jwt);

        assertNotNull(result);
        StepVerifier.create(result)
                .assertNext(token -> {
                    assertInstanceOf(JwtAuthenticationToken.class, token);
                    var jwtToken = (JwtAuthenticationToken) token;

                    Collection<? extends GrantedAuthority> authorities = jwtToken.getAuthorities();
                    assertTrue(authorities.isEmpty());
                })
                .verifyComplete();
    }

    @Test
    void reactiveJwtAuthenticationConverter_shouldHandleNullClientId() {
        var converter = securityConfig.reactiveJwtAuthenticationConverter();

        var jwt = Jwt.withTokenValue("token")
                .header("alg", "RS256")
                .claim("resource_access", Map.of(
                        "test-client", Map.of(
                                "roles", java.util.List.of("user", "admin")
                        )
                ))
                .issuedAt(Instant.now())
                .expiresAt(Instant.now().plusSeconds(3600))
                .build();

        var result = converter.convert(jwt);

        assertNotNull(result);
        StepVerifier.create(result)
                .assertNext(token -> {
                    assertInstanceOf(JwtAuthenticationToken.class, token);
                    var jwtToken = (JwtAuthenticationToken) token;

                    Collection<? extends GrantedAuthority> authorities = jwtToken.getAuthorities();
                    assertTrue(authorities.isEmpty());
                })
                .verifyComplete();
    }

    @Test
    void customerOrHandymanJwtDecoder_shouldUseCustomerDecoderFirst() {
        ReflectionTestUtils.setField(securityConfig, "customerJwtDecoder", mockCustomerJwtDecoder);
        ReflectionTestUtils.setField(securityConfig, "handymanJwtDecoder", mockHandymanJwtDecoder);

        var testJwt = Jwt.withTokenValue("test-token")
                .header("alg", "RS256")
                .claim("sub", "user123")
                .build();

        when(mockCustomerJwtDecoder.decode(anyString())).thenReturn(Mono.just(testJwt));

        var decoder = securityConfig.customerOrHandymanJwtDecoder();
        var result = decoder.decode("test-token");

        StepVerifier.create(result)
                .expectNext(testJwt)
                .verifyComplete();

        verify(mockCustomerJwtDecoder, times(1)).decode(anyString());
        verify(mockHandymanJwtDecoder, never()).decode(anyString());
    }

    @Test
    void customerOrHandymanJwtDecoder_shouldFallbackToHandymanDecoder() {
        ReflectionTestUtils.setField(securityConfig, "customerJwtDecoder", mockCustomerJwtDecoder);
        ReflectionTestUtils.setField(securityConfig, "handymanJwtDecoder", mockHandymanJwtDecoder);

        var testJwt = Jwt.withTokenValue("test-token")
                .header("alg", "RS256")
                .claim("sub", "handyman123")
                .build();

        when(mockCustomerJwtDecoder.decode(anyString())).thenReturn(Mono.error(new RuntimeException("Invalid token")));
        when(mockHandymanJwtDecoder.decode(anyString())).thenReturn(Mono.just(testJwt));

        var decoder = securityConfig.customerOrHandymanJwtDecoder();
        var result = decoder.decode("test-token");

        StepVerifier.create(result)
                .expectNext(testJwt)
                .verifyComplete();

        verify(mockCustomerJwtDecoder, times(1)).decode(anyString());
        verify(mockHandymanJwtDecoder, times(1)).decode(anyString());
    }

    @Test
    void customerOrHandymanJwtDecoder_shouldPropagateErrorWhenBothDecodersFail() {
        ReflectionTestUtils.setField(securityConfig, "customerJwtDecoder", mockCustomerJwtDecoder);
        ReflectionTestUtils.setField(securityConfig, "handymanJwtDecoder", mockHandymanJwtDecoder);

        var customerError = new RuntimeException("Invalid customer token");
        var handymanError = new RuntimeException("Invalid handyman token");

        when(mockCustomerJwtDecoder.decode(anyString())).thenReturn(Mono.error(customerError));
        when(mockHandymanJwtDecoder.decode(anyString())).thenReturn(Mono.error(handymanError));

        var decoder = securityConfig.customerOrHandymanJwtDecoder();
        var result = decoder.decode("test-token");

        StepVerifier.create(result)
                .expectErrorMatches(error -> error instanceof RuntimeException &&
                        "Invalid handyman token".equals(error.getMessage()))
                .verify();

        verify(mockCustomerJwtDecoder, times(1)).decode(anyString());
        verify(mockHandymanJwtDecoder, times(1)).decode(anyString());
    }
}
