package com.alleskoenner.backend.push_notification.internal;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import com.eatthepath.pushy.apns.ApnsClient;
import com.eatthepath.pushy.apns.PushNotificationResponse;
import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.Disposable;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class AppointmentRequestNotificationSenderTest {

    @Mock
    private PushNotificationClient pushNotificationClient;
    @Mock
    private PushNotificationConfiguration configuration;
    @Mock
    private NotificationSender notificationSender;
    @Mock
    private ApnsClient apnsClient;
    @Mock
    private ErrorNotificationEmailServiceInterface errorNotificationEmailService;

    @InjectMocks
    private AppointmentRequestNotificationSender sender;

    @BeforeEach
    void setUp() {
        lenient().when(pushNotificationClient.getApnsClient()).thenReturn(apnsClient);
        lenient().when(configuration.getNotificationTopic()).thenReturn("test.topic");
    }

    @Test
    void sendAppointmentRequestNotification_sendsNotificationsSuccessfully() {
        var deviceTokens = List.of("E1", "E2");
        var zonedDateTime = ZonedDateTime.of(2025, 2, 20, 10, 0, 0, 0, ZoneId.of("Europe/Berlin"));
        var asSoonAsPossible = false;

        PushNotificationResponse<SimpleApnsPushNotification> response = mock(PushNotificationResponse.class);
        SimpleApnsPushNotification pushNotification = mock(SimpleApnsPushNotification.class);
        when(pushNotification.getToken()).thenReturn("TOKEN");

        when(response.getPushNotification()).thenReturn(pushNotification);
        when(response.isAccepted()).thenReturn(true);

        when(notificationSender.sendNotificationWithRetry(any(), any()))
                .thenReturn(Mono.just(response));

        sender.sendAppointmentRequestNotification(deviceTokens, zonedDateTime, asSoonAsPossible).block();

        ArgumentCaptor<SimpleApnsPushNotification> notificationCaptor = ArgumentCaptor.forClass(SimpleApnsPushNotification.class);
        verify(notificationSender, times(deviceTokens.size()))
                .sendNotificationWithRetry(notificationCaptor.capture(), any());
        assertThat(notificationCaptor.getAllValues())
                .extracting(SimpleApnsPushNotification::getToken)
                .containsExactlyInAnyOrderElementsOf(deviceTokens);

        assertThat(notificationCaptor.getAllValues())
                .extracting(SimpleApnsPushNotification::getPayload)
                .contains("{\"route\":\"appointment-requests\",\"aps\":{\"alert\":{\"subtitle\":\"20.02.2025, 10 Uhr\",\"title\":\"Neue Anfrage\"},\"sound\":\"default\"}}");

        verify(errorNotificationEmailService, never()).sendErrorNotificationEmail(any());
    }

    @Test
    void sendAppointmentRequestNotification_withAsSoonAsPossible_sendsNotificationsSuccessfully() {
        var deviceTokens = List.of("E1", "E2");
        var zonedDateTime = ZonedDateTime.of(2025, 2, 20, 10, 0, 0, 0, ZoneId.of("Europe/Berlin"));
        var asSoonAsPossible = true;

        PushNotificationResponse<SimpleApnsPushNotification> response = mock(PushNotificationResponse.class);
        SimpleApnsPushNotification pushNotification = mock(SimpleApnsPushNotification.class);
        when(pushNotification.getToken()).thenReturn("TOKEN");

        when(response.getPushNotification()).thenReturn(pushNotification);
        when(response.isAccepted()).thenReturn(true);

        when(notificationSender.sendNotificationWithRetry(any(), any()))
                .thenReturn(Mono.just(response));

        sender.sendAppointmentRequestNotification(deviceTokens, zonedDateTime, asSoonAsPossible).block();

        ArgumentCaptor<SimpleApnsPushNotification> notificationCaptor = ArgumentCaptor.forClass(SimpleApnsPushNotification.class);
        verify(notificationSender, times(deviceTokens.size()))
                .sendNotificationWithRetry(notificationCaptor.capture(), any());

        assertThat(notificationCaptor.getAllValues())
                .extracting(SimpleApnsPushNotification::getPayload)
                .contains("{\"route\":\"appointment-requests\",\"aps\":{\"alert\":{\"subtitle\":\"So schnell wie möglich\",\"title\":\"Neue Anfrage\"},\"sound\":\"default\"}}");
    }

    @Test
    void sendAppointmentRequestNotification_withRejectedNotifications_sendsErrorEmail() {
        var deviceTokens = List.of("E1");
        var zonedDateTime = ZonedDateTime.of(2025, 2, 20, 10, 0, 0, 0, ZoneId.of("Europe/Berlin"));
        var asSoonAsPossible = false;

        PushNotificationResponse<SimpleApnsPushNotification> rejectedResponse = mock(PushNotificationResponse.class);
        SimpleApnsPushNotification rejectedNotification = mock(SimpleApnsPushNotification.class);
        when(rejectedNotification.getToken()).thenReturn("E1");
        when(rejectedResponse.getPushNotification()).thenReturn(rejectedNotification);
        when(rejectedResponse.isAccepted()).thenReturn(false);
        when(rejectedResponse.getRejectionReason()).thenReturn(Optional.of("example rejected reason"));
        when(notificationSender.sendNotificationWithRetry(any(), any()))
                .thenReturn(Mono.just(rejectedResponse));

        Disposable disposable = mock(Disposable.class);
        when(errorNotificationEmailService.sendErrorNotificationEmail(any())).thenReturn(disposable);


        sender.sendAppointmentRequestNotification(deviceTokens, zonedDateTime, asSoonAsPossible).block();

        var throwableCaptor = ArgumentCaptor.forClass(Throwable.class);
        verify(errorNotificationEmailService).sendErrorNotificationEmail(throwableCaptor.capture());
        assertThat(throwableCaptor.getValue().getMessage())
                .isEqualTo("Notification for device with device token 'E1' was rejected with reason 'example rejected reason'.");
    }

    @Test
    void sendAppointmentRequestNotification_withEmptyDeviceTokens_completesSuccessfully() {
        List<String> deviceTokens = List.of();
        var zonedDateTime = ZonedDateTime.of(2025, 2, 20, 10, 0, 0, 0, ZoneId.of("Europe/Berlin"));
        var asSoonAsPossible = false;

        StepVerifier.create(sender.sendAppointmentRequestNotification(deviceTokens, zonedDateTime, asSoonAsPossible))
                .verifyComplete();

        verify(notificationSender, never()).sendNotificationWithRetry(any(), any());
    }

    @Test
    void sendAppointmentRequestNotification_withNullZonedDateTime_usesDefaultSubtitle() {
        var deviceTokens = List.of("E1");
        ZonedDateTime zonedDateTime = null;
        var asSoonAsPossible = false;

        PushNotificationResponse<SimpleApnsPushNotification> response = mock(PushNotificationResponse.class);
        SimpleApnsPushNotification pushNotification = mock(SimpleApnsPushNotification.class);
        when(pushNotification.getToken()).thenReturn("TOKEN");
        when(response.getPushNotification()).thenReturn(pushNotification);
        when(response.isAccepted()).thenReturn(true);

        when(notificationSender.sendNotificationWithRetry(any(), any()))
                .thenReturn(Mono.just(response));

        sender.sendAppointmentRequestNotification(deviceTokens, zonedDateTime, asSoonAsPossible).block();

        ArgumentCaptor<SimpleApnsPushNotification> notificationCaptor = ArgumentCaptor.forClass(SimpleApnsPushNotification.class);
        verify(notificationSender).sendNotificationWithRetry(notificationCaptor.capture(), any());

        assertThat(notificationCaptor.getValue().getPayload())
                .contains("\"subtitle\":\"Zeit leider unbekannt\"");
    }
}
