package com.alleskoenner.backend.push_notification.internal;

import java.time.Duration;
import com.eatthepath.pushy.apns.ApnsClient;
import com.eatthepath.pushy.apns.ApnsPushNotification;
import com.eatthepath.pushy.apns.PushNotificationResponse;
import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
import com.eatthepath.pushy.apns.util.concurrent.PushNotificationFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class NotificationSenderTest {

    @Mock private ApnsClient apnsClient;
    @Mock private PushNotificationConfiguration configuration;
    @Mock private PushNotificationConfiguration.AppointmentRequestNotificationRetry retryConfig;
    @InjectMocks private NotificationSender notificationSender;

    @Test
    void sendNotificationWithRetry_ShouldReturnEmptyMono_OnErrorAfterRetries() {
        int maximumRetryAttempts = 3;
        when(retryConfig.getMaximumAttempts()).thenReturn(maximumRetryAttempts);
        when(retryConfig.getMaximumBackoffDuration()).thenReturn(Duration.ofMillis(200));
        when(retryConfig.getMinimumBackoffDuration()).thenReturn(Duration.ofMillis(10));
        when(configuration.getAppointmentRequestNotificationRetry()).thenReturn(retryConfig);
        ApnsPushNotification notification = mock(SimpleApnsPushNotification.class);
        PushNotificationFuture<ApnsPushNotification, PushNotificationResponse<ApnsPushNotification>> pushNotificationFuture = new PushNotificationFuture<>(notification);


        when(apnsClient.sendNotification(any())).thenReturn(pushNotificationFuture);

        Mono<PushNotificationResponse<SimpleApnsPushNotification>> result = notificationSender.sendNotificationWithRetry(null, apnsClient);

        pushNotificationFuture.completeExceptionally(new RuntimeException("APNs failure"));

        StepVerifier.create(result)
                .verifyComplete();

        verify(apnsClient, times(maximumRetryAttempts + 1)).sendNotification(any());
    }
}
