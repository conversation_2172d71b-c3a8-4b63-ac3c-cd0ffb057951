package com.alleskoenner.backend.push_notification;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;

@SpringBootTest
@Profile( {"manual-testing", "test"})
@Disabled("This test is only for manually testing push notifications. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class PushNotificationClientManualTest {

    @Autowired
    AppointmentRequestNotificationSenderInterface appointmentRequestNotificationSender;

    @Test
    void shouldSendAThing() throws InterruptedException {
        appointmentRequestNotificationSender.sendAppointmentRequestNotification(List.of("73E4B388DAF8F39E8F4290218343BF4D409BE70180DF47889752583E5774EBF6"), ZonedDateTime.of(2025, 2, 20, 10, 0, 0, 0, ZoneId.of("Europe/Berlin")), false)
                .subscribe();

        Thread.sleep(1000); // Wait for the notification to be sent
    }
}
