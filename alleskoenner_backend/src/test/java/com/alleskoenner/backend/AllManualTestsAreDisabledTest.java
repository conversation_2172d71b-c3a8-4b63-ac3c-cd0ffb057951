package com.alleskoenner.backend;

import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import org.assertj.core.api.SoftAssertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

class AllManualTestsAreDisabledTest {

    private static final String PACKAGE_NAME = "com.alleskoenner.backend";

    @Test
    void allManualTestsShouldBeDisabled() throws Exception {
        String packagePath = PACKAGE_NAME.replace(".", "/");
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        URL packageURL = classLoader.getResource(packagePath);

        if (packageURL == null) {
            throw new IllegalStateException("Package not found: " + PACKAGE_NAME);
        }

        File directory = new File(packageURL.toURI());
        List<File> classFiles = recursivelyGetAllClassFiles(directory);


        SoftAssertions testAsserts = new SoftAssertions();

        for (File file : classFiles) {
            if (file.getName().endsWith("ManualTest.class")) {
                String className = getClassNameFromFile(file, PACKAGE_NAME);
                Class<?> clazz = Class.forName(className);

                boolean isDisabled = clazz.isAnnotationPresent(Disabled.class);

                testAsserts.assertThat(isDisabled).as("Class " + clazz.getName() + " should be annotated with @Disabled").isTrue();
            }
        }

        testAsserts.assertAll();
    }

    private List<File> recursivelyGetAllClassFiles(File directory) {
        List<File> classFiles = new ArrayList<>();
        File[] files = directory.listFiles();

        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    classFiles.addAll(recursivelyGetAllClassFiles(file));
                } else if (file.getName().endsWith(".class")) {
                    classFiles.add(file);
                }
            }
        }

        return classFiles;
    }

    private String getClassNameFromFile(File file, String basePackage) {
        String filePath = file.getPath().replace(File.separator, ".");
        int startIndex = filePath.indexOf(basePackage);
        return filePath.substring(startIndex).replace(".class", "");
    }
}
