package com.alleskoenner.backend;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.modulith.core.ApplicationModules;
import org.springframework.modulith.docs.Documenter;
import org.springframework.test.context.ActiveProfiles;

@ActiveProfiles("test")
@SpringBootTest
class AlleskoennerBackendModuleTest {

    @Test
    void verifyModuleStructure() {

        var modules = ApplicationModules.of(AlleskoennerBackendApplication.class).verify();

        modules.forEach(System.out::println);
    }

    @Test
    void printModulesAndWriteDocumentationSnippets() {

        var modules = ApplicationModules.of(AlleskoennerBackendApplication.class);

        modules.forEach(System.out::println);

        new Documenter(modules)
                .writeModulesAsPlantUml()
                .writeIndividualModulesAsPlantUml();
    }

}
