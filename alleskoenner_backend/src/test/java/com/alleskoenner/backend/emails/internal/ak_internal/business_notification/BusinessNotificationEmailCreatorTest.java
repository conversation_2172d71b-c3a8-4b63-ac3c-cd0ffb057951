package com.alleskoenner.backend.emails.internal.ak_internal.business_notification;

import java.util.List;
import com.alleskoenner.backend.emails.BusinessNotificationEmailValues;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BusinessNotificationEmailCreatorTest {

    @InjectMocks
    BusinessNotificationEmailCreator businessNotificationEmailCreator;

    @Test
    void shouldCreateCorrectEmailMessage() {
        var notificationText = "This is a business notification";
        var senderAddress = "<EMAIL>";
        var recipientAddresses = List.of("<EMAIL>", "<EMAIL>");
        var businessNotificationEmailValues = new BusinessNotificationEmailValues(notificationText);

        var emailMessage = businessNotificationEmailCreator.createEmailMessage(
                businessNotificationEmailValues,
                senderAddress,
                recipientAddresses
        );

        assertThat(emailMessage.getSenderAddress()).isEqualTo(senderAddress);
        assertThat(emailMessage.getToRecipients())
                .hasSize(2)
                .allMatch(email ->
                        recipientAddresses.contains(email.getAddress()));
        assertThat(emailMessage.getSubject()).isEqualTo("Alleskönner24 Business Notification");
        assertThat(emailMessage.getBodyPlainText()).isEqualTo(notificationText);
        assertThat(emailMessage.getBodyHtml()).contains(notificationText);
        assertThat(emailMessage.getAttachments()).isNull();
    }
}
