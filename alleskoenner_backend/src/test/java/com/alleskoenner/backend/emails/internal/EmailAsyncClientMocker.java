package com.alleskoenner.backend.emails.internal;

import com.azure.communication.email.EmailAsyncClient;
import com.azure.communication.email.models.EmailSendResult;
import com.azure.core.models.ResponseError;
import com.azure.core.util.polling.AsyncPollResponse;
import com.azure.core.util.polling.LongRunningOperationStatus;
import com.azure.core.util.polling.PollerFlux;
import org.jetbrains.annotations.NotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import reactor.core.publisher.Mono;

public class EmailAsyncClientMocker {
    @NotNull
    public static EmailAsyncClient mockEmailAsyncClient(AzureEmailClient azureEmailClient) {
        var mockResult = new EmailSendResult("email-id", null, new ResponseError("code", "message"));
        var emailAsyncClient = mock(EmailAsyncClient.class);
        when(azureEmailClient.getEmailClient()).thenReturn(emailAsyncClient);
        var pollerFlux = mock(PollerFlux.class);
        when(emailAsyncClient.beginSend(any())).thenReturn(pollerFlux);
        var asyncPollResponse = mock(AsyncPollResponse.class);
        var asyncPollResponseMono = Mono.just(asyncPollResponse);
        when(pollerFlux.last()).thenReturn(asyncPollResponseMono);
        lenient().when(asyncPollResponse.getStatus()).thenReturn(LongRunningOperationStatus.SUCCESSFULLY_COMPLETED);
        when(asyncPollResponse.getFinalResult()).thenReturn(Mono.just(mockResult));
        return emailAsyncClient;
    }
}
