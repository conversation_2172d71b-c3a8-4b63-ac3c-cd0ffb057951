package com.alleskoenner.backend.emails.internal.invoice;

import com.alleskoenner.backend.emails.internal.AzureEmailClient;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.alleskoenner.backend.emails.internal.EmailAsyncClientMocker;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import reactor.test.StepVerifier;

class InvoiceEmailServiceTest {

    @Mock
    private AzureEmailClient azureEmailClient;
    @Mock
    private AzureEmailConfiguration azureEmailConfiguration;
    @Mock
    private InvoiceEmailCreator invoiceEmailCreator;

    @InjectMocks
    private InvoiceEmailService invoiceEmailService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void shouldSendInvoiceEmail() {
        var emailAsyncClient = EmailAsyncClientMocker.mockEmailAsyncClient(azureEmailClient);
        when(azureEmailConfiguration.getSenderAddress()).thenReturn("<EMAIL>");

        var resultValue = invoiceEmailService.sendInvoiceEmail(null);

        StepVerifier.create(resultValue)
                .assertNext(emailSendResult -> {
                    assertThat(emailSendResult.getId()).isEqualTo("email-id");
                    verify(azureEmailClient).getEmailClient();
                    verify(emailAsyncClient).beginSend(any());
                })
                .verifyComplete();
    }
}