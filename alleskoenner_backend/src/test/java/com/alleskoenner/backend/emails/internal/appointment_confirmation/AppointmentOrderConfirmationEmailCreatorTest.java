package com.alleskoenner.backend.emails.internal.appointment_confirmation;

import com.alleskoenner.backend.emails.AsapAppointmentConfirmationEmailValues;
import com.alleskoenner.backend.emails.ScheduledAppointmentConfirmationEmailValues;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class AppointmentOrderConfirmationEmailCreatorTest {

    @InjectMocks
    AppointmentConfirmationEmailCreator appointmentConfirmationEmailCreator;

    @Test
    void shouldCreateCorrectAsapAppointmentEmailMessage() {
        var orderNumber = "12345";
        var shipToName = "John Doe";
        var shipToAddressLine1 = "123 Main St";
        var shipToCity = "Berlin";
        var shipToPostalCode = "10115";
        var email = "<EMAIL>";
        var taskDescription = "Fix plumbing";
        var senderAddress = "<EMAIL>";

        var values = new AsapAppointmentConfirmationEmailValues(
                orderNumber,
                shipToName,
                shipToAddressLine1,
                shipToCity,
                shipToPostalCode,
                email,
                taskDescription
        );

        var emailMessage = appointmentConfirmationEmailCreator.createAppointmentEmailMessage(values, senderAddress);

        assertThat(emailMessage.getSenderAddress()).isEqualTo(senderAddress);
        assertThat(emailMessage.getToRecipients())
                .hasSize(1)
                .allMatch(recipient -> recipient.getAddress().equals(email));
        assertThat(emailMessage.getSubject()).isEqualTo("Dein Auftrag wurde angenommen - Auftragsnummer: " + orderNumber);
        assertThat(emailMessage.getBodyPlainText()).isEqualTo("Vielen Dank für Ihren Auftrag mit der Auftragsnummer: " + orderNumber);
        var emailValues = List.of(orderNumber, shipToName, shipToAddressLine1, shipToCity, shipToPostalCode, email, taskDescription);
        assertThat(emailMessage.getBodyHtml()).contains(emailValues);
    }

    @Test
    void shouldCreateCorrectScheduledAppointmentEmailMessage() {
        var orderNumber = "67890";
        var shipToName = "Jane Smith";
        var shipToAddress = "456 Elm St";
        var shipToCity = "Munich";
        var shipToPostCode = "80331";
        var email = "<EMAIL>";
        var taskDescription = "Install new sink";
        var weekday = "Monday";
        var date = "2023-10-30";
        var time = "10:00 AM";
        var senderAddress = "<EMAIL>";

        var values = new ScheduledAppointmentConfirmationEmailValues(
                orderNumber,
                shipToName,
                shipToAddress,
                shipToCity,
                shipToPostCode,
                email,
                taskDescription,
                weekday,
                date,
                time
        );

        var emailMessage = appointmentConfirmationEmailCreator.createAppointmentEmailMessage(values, senderAddress);

        assertThat(emailMessage.getSenderAddress()).isEqualTo(senderAddress);
        assertThat(emailMessage.getToRecipients())
                .hasSize(1)
                .allMatch(recipient -> recipient.getAddress().equals(email));
        assertThat(emailMessage.getSubject()).isEqualTo("Dein Auftrag wurde angenommen - Auftragsnummer: " + orderNumber);
        assertThat(emailMessage.getBodyPlainText()).isEqualTo("Vielen Dank für Ihren Auftrag mit der Auftragsnummer: " + orderNumber);
        var emailValues = List.of(orderNumber, shipToName, shipToAddress, shipToCity, shipToPostCode, email, taskDescription, weekday, date, time);
        assertThat(emailMessage.getBodyHtml()).contains(emailValues);
    }
}