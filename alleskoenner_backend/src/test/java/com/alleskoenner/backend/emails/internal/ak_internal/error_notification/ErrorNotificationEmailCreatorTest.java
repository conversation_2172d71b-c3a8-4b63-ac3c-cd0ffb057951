package com.alleskoenner.backend.emails.internal.ak_internal.error_notification;

import java.util.List;
import com.alleskoenner.backend.emails.ErrorNotificationEmailValues;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ErrorNotificationEmailCreatorTest {

    @InjectMocks
    ErrorNotificationEmailCreator errorNotificationEmailCreator;

    @Test
    void shouldCreateCorrectEmailMessage() {
        var errorText = "An error occurred";
        var stacktrace = "at com.example.Class.method(Class.java:123)\nat com.example.AnotherClass.method(AnotherClass.java:456)";
        var senderAddress = "<EMAIL>";
        var recipientAddresses = List.of("<EMAIL>", "<EMAIL>");

        var errorNotificationEmailValues = new ErrorNotificationEmailValues(errorText, stacktrace);

        var emailMessage = errorNotificationEmailCreator.createEmailMessage(
                errorNotificationEmailValues,
                senderAddress,
                recipientAddresses
        );

        assertThat(emailMessage.getSenderAddress()).isEqualTo(senderAddress);
        assertThat(emailMessage.getToRecipients())
                .hasSize(2)
                .allMatch(email ->
                        recipientAddresses.contains(email.getAddress()));
        assertThat(emailMessage.getSubject()).isEqualTo("Alleskönner24 Error Notification");
        assertThat(emailMessage.getBodyPlainText())
                .contains(errorText)
                .contains("Stacktrace:")
                .contains(stacktrace);
        assertThat(emailMessage.getBodyHtml())
                .contains(errorText)
                .contains(stacktrace);
        assertThat(emailMessage.getAttachments()).isNull();
    }
}
