package com.alleskoenner.backend.emails.internal.ak_internal.error_notification;

import java.util.List;
import com.alleskoenner.backend.emails.ErrorNotificationEmailValues;
import com.alleskoenner.backend.emails.internal.AzureEmailClient;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.alleskoenner.backend.emails.internal.EmailAsyncClientMocker;
import com.azure.communication.email.models.EmailMessage;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import reactor.core.Disposable;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

class ErrorNotificationEmailServiceTest {

    @Mock
    private AzureEmailClient azureEmailClient;

    @Mock
    private AzureEmailConfiguration azureEmailConfiguration;

    @Mock
    private ErrorNotificationEmailCreator errorNotificationEmailCreator;

    @Mock
    private AzureEmailConfiguration.InternalErrorNotification internalErrorNotification;

    @InjectMocks
    private ErrorNotificationEmailService errorNotificationEmailService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(azureEmailConfiguration.getInternalErrorNotification()).thenReturn(internalErrorNotification);
    }

    @Test
    void shouldSendErrorNotificationEmail() {
        Throwable throwable = new RuntimeException("Test error");
        String senderAddress = "<EMAIL>";
        String[] recipientAddressesArray = {"<EMAIL>", "<EMAIL>"};
        List<String> recipientAddresses = List.of(recipientAddressesArray);

        when(internalErrorNotification.isEnabled()).thenReturn(true);
        when(internalErrorNotification.getRecipientAddresses()).thenReturn(recipientAddressesArray);
        when(azureEmailConfiguration.getSenderAddress()).thenReturn(senderAddress);

        var emailAsyncClient = EmailAsyncClientMocker.mockEmailAsyncClient(azureEmailClient);

        var emailMessage = mock(EmailMessage.class);
        when(errorNotificationEmailCreator.createEmailMessage(any(ErrorNotificationEmailValues.class), eq(senderAddress), eq(recipientAddresses)))
                .thenReturn(emailMessage);

        Mono<Void> mono = errorNotificationEmailService.createErrorNotificationEmailMono(throwable, "");

        StepVerifier.create(mono).verifyComplete();

        verify(azureEmailClient).getEmailClient();
        verify(emailAsyncClient).beginSend(emailMessage);
        verify(errorNotificationEmailCreator).createEmailMessage(any(ErrorNotificationEmailValues.class), eq(senderAddress), eq(recipientAddresses));

        Disposable result = errorNotificationEmailService.sendErrorNotificationEmail(throwable);
        assertThat(result).isNotNull();
    }

    @Test
    void shouldNotSendEmailWhenDisabled() {
        when(internalErrorNotification.isEnabled()).thenReturn(false);
        Throwable throwable = new RuntimeException("Test error");

        Mono<Void> mono = errorNotificationEmailService.createErrorNotificationEmailMono(throwable, "");

        StepVerifier.create(mono).verifyComplete();

        verify(azureEmailClient, never()).getEmailClient();
        verify(errorNotificationEmailCreator, never()).createEmailMessage(any(), any(), any());

        Disposable result = errorNotificationEmailService.sendErrorNotificationEmail(throwable);
        assertThat(result).isNotNull();
    }

    @Test
    void shouldNotSendEmailWhenNoRecipients() {
        when(internalErrorNotification.isEnabled()).thenReturn(true);
        when(internalErrorNotification.getRecipientAddresses()).thenReturn(new String[0]);
        Throwable throwable = new RuntimeException("Test error");

        Mono<Void> mono = errorNotificationEmailService.createErrorNotificationEmailMono(throwable, "");

        StepVerifier.create(mono).verifyComplete();

        verify(azureEmailClient, never()).getEmailClient();
        verify(errorNotificationEmailCreator, never()).createEmailMessage(any(), any(), any());

        Disposable result = errorNotificationEmailService.sendErrorNotificationEmail(throwable);
        assertThat(result).isNotNull();
    }
}
