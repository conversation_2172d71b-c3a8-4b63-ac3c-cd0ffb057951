package com.alleskoenner.backend.emails.internal.order_confirmation;

import com.alleskoenner.backend.emails.internal.AzureEmailClient;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.azure.communication.email.EmailAsyncClient;
import com.azure.communication.email.models.EmailSendResult;
import com.azure.core.models.ResponseError;
import com.azure.core.util.polling.AsyncPollResponse;
import com.azure.core.util.polling.LongRunningOperationStatus;
import com.azure.core.util.polling.PollerFlux;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

class ConfirmationEmailServiceTest {

    @Mock
    private AzureEmailClient azureEmailClient;
    @Mock
    private AzureEmailConfiguration azureEmailConfiguration;
    @Mock
    private OrderConfirmationEmailCreator orderConfirmationEmailCreator;

    @InjectMocks
    private OrderConfirmationEmailService orderConfirmationEmailService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void shouldSendConfirmationEmail() {
        EmailSendResult mockResult = new EmailSendResult("email-id", null, new ResponseError("asdf", "asdf"));
        EmailAsyncClient emailAsyncClient = mock(EmailAsyncClient.class);
        when(azureEmailClient.getEmailClient()).thenReturn(emailAsyncClient);
        PollerFlux pollerFlux = mock(PollerFlux.class);
        when(emailAsyncClient.beginSend(any())).thenReturn(pollerFlux);
        AsyncPollResponse asyncPollResponse = mock(AsyncPollResponse.class);
        Mono<AsyncPollResponse> asyncPollResponseMono = Mono.just(asyncPollResponse);
        when(pollerFlux.last()).thenReturn(asyncPollResponseMono);
        when(asyncPollResponse.getStatus()).thenReturn(LongRunningOperationStatus.SUCCESSFULLY_COMPLETED);
        when(asyncPollResponse.getFinalResult()).thenReturn(Mono.just(mockResult));
        when(azureEmailConfiguration.getSenderAddress()).thenReturn("<EMAIL>");

        Mono<EmailSendResult> resultValue = orderConfirmationEmailService.sendConfirmationEmail(null);

        StepVerifier.create(resultValue)
                .assertNext(emailSendResult -> {
                    assertThat(emailSendResult.getId()).isEqualTo("email-id");
                    verify(azureEmailClient).getEmailClient();
                    verify(emailAsyncClient).beginSend(any());
                })
                .verifyComplete();
    }
}