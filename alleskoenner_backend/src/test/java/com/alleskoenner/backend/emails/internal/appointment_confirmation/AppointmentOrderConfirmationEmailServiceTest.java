package com.alleskoenner.backend.emails.internal.appointment_confirmation;

import com.alleskoenner.backend.emails.AsapAppointmentConfirmationEmailValues;
import com.alleskoenner.backend.emails.ScheduledAppointmentConfirmationEmailValues;
import com.alleskoenner.backend.emails.internal.AzureEmailClient;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.alleskoenner.backend.emails.internal.EmailAsyncClientMocker;
import com.azure.communication.email.models.EmailMessage;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class AppointmentOrderConfirmationEmailServiceTest {

    @Mock
    private AzureEmailClient azureEmailClient;

    @Mock
    private AzureEmailConfiguration azureEmailConfiguration;

    @Mock
    private AppointmentConfirmationEmailCreator appointmentConfirmationEmailCreator;

    @InjectMocks
    private AppointmentConfirmationEmailService appointmentConfirmationEmailService;


    @Test
    void shouldSendAsapAppointmentConfirmationEmail() {
        var senderAddress = "<EMAIL>";
        var values = mock(AsapAppointmentConfirmationEmailValues.class);
        when(azureEmailConfiguration.getSenderAddress()).thenReturn(senderAddress);
        var emailAsyncClient = EmailAsyncClientMocker.mockEmailAsyncClient(azureEmailClient);
        var emailMessage = mock(EmailMessage.class);
        when(appointmentConfirmationEmailCreator.createAppointmentEmailMessage(eq(values), eq(senderAddress)))
                .thenReturn(emailMessage);

        var mono = appointmentConfirmationEmailService.sendAsapAppointmentConfirmationEmail(values);

        StepVerifier.create(mono).expectNextCount(1).verifyComplete();
        verify(azureEmailClient).getEmailClient();
        verify(emailAsyncClient).beginSend(emailMessage);
        verify(appointmentConfirmationEmailCreator).createAppointmentEmailMessage(eq(values), eq(senderAddress));
    }

    @Test
    void shouldSendScheduledAppointmentConfirmationEmail() {
        var senderAddress = "<EMAIL>";
        var values = mock(ScheduledAppointmentConfirmationEmailValues.class);
        when(azureEmailConfiguration.getSenderAddress()).thenReturn(senderAddress);
        var emailAsyncClient = EmailAsyncClientMocker.mockEmailAsyncClient(azureEmailClient);
        var emailMessage = mock(EmailMessage.class);
        when(appointmentConfirmationEmailCreator.createAppointmentEmailMessage(eq(values), eq(senderAddress)))
                .thenReturn(emailMessage);

        var mono = appointmentConfirmationEmailService.sendScheduledAppointmentConfirmationEmail(values);

        StepVerifier.create(mono).expectNextCount(1).verifyComplete();
        verify(azureEmailClient).getEmailClient();
        verify(emailAsyncClient).beginSend(emailMessage);
        verify(appointmentConfirmationEmailCreator).createAppointmentEmailMessage(eq(values), eq(senderAddress));
    }
}
