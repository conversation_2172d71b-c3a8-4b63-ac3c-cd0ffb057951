package com.alleskoenner.backend.emails.internal.ak_internal.business_notification;

import java.util.List;
import com.alleskoenner.backend.emails.BusinessNotificationEmailValues;
import com.alleskoenner.backend.emails.internal.AzureEmailClient;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.alleskoenner.backend.emails.internal.EmailAsyncClientMocker;
import com.azure.communication.email.models.EmailMessage;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import reactor.core.Disposable;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

class BusinessNotificationEmailServiceTest {

    @Mock
    private AzureEmailClient azureEmailClient;

    @Mock
    private AzureEmailConfiguration azureEmailConfiguration;

    @Mock
    private BusinessNotificationEmailCreator businessNotificationEmailCreator;

    @Mock
    private AzureEmailConfiguration.InternalBusinessNotification internalBusinessNotification;

    @InjectMocks
    private BusinessNotificationEmailService businessNotificationEmailService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(azureEmailConfiguration.getInternalBusinessNotification()).thenReturn(internalBusinessNotification);
    }

    @Test
    void shouldSendBusinessNotificationEmail() {
        String notificationText = "Business notification message";
        String senderAddress = "<EMAIL>";
        String[] recipientAddressesArray = {"<EMAIL>", "<EMAIL>"};
        List<String> recipientAddresses = List.of(recipientAddressesArray);

        when(internalBusinessNotification.isEnabled()).thenReturn(true);
        when(internalBusinessNotification.getRecipientAddresses()).thenReturn(recipientAddressesArray);
        when(azureEmailConfiguration.getSenderAddress()).thenReturn(senderAddress);

        var emailAsyncClient = EmailAsyncClientMocker.mockEmailAsyncClient(azureEmailClient);

        var emailMessage = mock(EmailMessage.class);
        when(businessNotificationEmailCreator.createEmailMessage(any(BusinessNotificationEmailValues.class), eq(senderAddress), eq(recipientAddresses)))
                .thenReturn(emailMessage);

        Mono<Void> mono = businessNotificationEmailService.createBusinessNotificationEmailMono(notificationText);

        StepVerifier.create(mono).verifyComplete();

        verify(azureEmailClient).getEmailClient();
        verify(emailAsyncClient).beginSend(emailMessage);
        verify(businessNotificationEmailCreator).createEmailMessage(any(BusinessNotificationEmailValues.class), eq(senderAddress), eq(recipientAddresses));

        Disposable result = businessNotificationEmailService.sendBusinessNotificationEmail(notificationText);
        assertThat(result).isNotNull();
    }

    @Test
    void shouldNotSendEmailWhenDisabled() {
        when(internalBusinessNotification.isEnabled()).thenReturn(false);

        Mono<Void> mono = businessNotificationEmailService.createBusinessNotificationEmailMono("test");

        StepVerifier.create(mono).verifyComplete();

        verify(azureEmailClient, never()).getEmailClient();
        verify(businessNotificationEmailCreator, never()).createEmailMessage(any(), any(), any());

        Disposable result = businessNotificationEmailService.sendBusinessNotificationEmail("test");
        assertThat(result).isNotNull();
    }

    @Test
    void shouldNotSendEmailWhenNoRecipients() {
        when(internalBusinessNotification.isEnabled()).thenReturn(true);
        when(internalBusinessNotification.getRecipientAddresses()).thenReturn(new String[0]);

        Mono<Void> mono = businessNotificationEmailService.createBusinessNotificationEmailMono("test");

        StepVerifier.create(mono).verifyComplete();

        verify(azureEmailClient, never()).getEmailClient();
        verify(businessNotificationEmailCreator, never()).createEmailMessage(any(), any(), any());

        Disposable result = businessNotificationEmailService.sendBusinessNotificationEmail("test");
        assertThat(result).isNotNull();
    }
}