package com.alleskoenner.backend.emails.internal.order_confirmation;

import com.alleskoenner.backend.emails.ConfirmationEmailValues;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.azure.communication.email.models.EmailMessage;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import java.util.Objects;

class ConfirmationEmailCreatorTest {

    @Mock AzureEmailConfiguration azureEmailConfiguration;

    @InjectMocks
    OrderConfirmationEmailCreator confirmationEmailCreator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(azureEmailConfiguration.getTermsAndConditionsFileName()).thenReturn("AGB.pdf");
    }

    @Test
    void shouldCreateCorrectEmailMessage() {
        String orderNumber = "12345";
        String recipientAddress = "<EMAIL>";
        String senderAddress = "<EMAIL>";
        ConfirmationEmailValues confirmationEmailValues = new ConfirmationEmailValues(
                orderNumber,
                "display name",
                "Adresse 1",
                "55555",
                "Bielefeld-Stadt",
                "<EMAIL>",
                "Beschreibung der Task... "
        );


        EmailMessage emailMessage = confirmationEmailCreator.createEmailMessage(confirmationEmailValues, senderAddress);

        assertThat(emailMessage.getSenderAddress()).isEqualTo(senderAddress);
        assertThat(emailMessage.getToRecipients())
                .hasSize(1)
                .allMatch(email -> Objects.equals(email.getAddress(), recipientAddress));
        assertThat(emailMessage.getSubject()).isEqualTo("Wir haben Ihren Auftrag erhalten - Vorgangsnummer: " + orderNumber);
        assertThat(emailMessage.getBodyPlainText())
                .contains("Vielen Dank für Ihren Auftrag mit der Vorgangsnummer: " + orderNumber)
                .contains(orderNumber);
        assertThat(emailMessage.getBodyHtml())
                .contains("display name")
                .contains("Adresse 1")
                .contains("55555 Bielefeld-Stadt")
                .contains("<EMAIL>")
                .contains("Beschreibung der Task... ");
        assertThat(emailMessage.getAttachments())
                .hasSize(1)
                .allMatch(attachment -> Objects.equals(attachment.getName(), "Allgemeine_Geschäftsbedingungen.pdf"))
                .allMatch(attachment -> attachment.getContentType().equals("application/pdf"));
    }

}