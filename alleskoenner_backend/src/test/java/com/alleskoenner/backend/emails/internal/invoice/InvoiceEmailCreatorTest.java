package com.alleskoenner.backend.emails.internal.invoice;

import java.util.Objects;

import com.alleskoenner.backend.emails.InvoiceEmailValues;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

class InvoiceEmailCreatorTest {

    @InjectMocks
    InvoiceEmailCreator invoiceEmailCreator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void shouldCreateCorrectEmailMessage() {
        var invoiceNumber = "12345";
        var recipientAddress = "<EMAIL>";
        var senderAddress = "<EMAIL>";
        var pdfAttachment = "test pdf content".getBytes();

        var invoiceEmailValues = new InvoiceEmailValues(
                "Adresse 1",
                "Bielefeld-Stadt",
                "<PERSON>",
                "55555",
                invoiceN<PERSON>ber,
                pdfAttachment,
                recipientAddress,
                "Arbeitsstraße 1",
                "Arbeit-Stadt",
                "<PERSON> Mustermann",
                "44444"
        );

        var emailMessage = invoiceEmailCreator.createEmailMessage(invoiceEmailValues, senderAddress);

        assertThat(emailMessage.getSenderAddress()).isEqualTo(senderAddress);
        assertThat(emailMessage.getToRecipients())
                .hasSize(1)
                .allMatch(email -> Objects.equals(email.getAddress(), recipientAddress));
        assertThat(emailMessage.getSubject()).isEqualTo("Ihre Rechnung - Rechnungsnummer: " + invoiceNumber);
        assertThat(emailMessage.getBodyPlainText())
                .contains("Vielen Dank für Ihren Auftrag. Anbei finden Sie Ihre Rechnung mit der Rechnungsnummer: " + invoiceNumber);
        assertThat(emailMessage.getBodyHtml())
                .contains("Max Mustermann")
                .contains("Adresse 1")
                .contains("Bielefeld-Stadt")
                .contains("55555")
                .contains("Arbeitsstraße 1")
                .contains("Arbeit-Stadt")
                .contains("44444");

        assertThat(emailMessage.getAttachments())
                .hasSize(1)
                .allMatch(attachment ->
                        attachment.getName().equals("Rechnung_" + invoiceNumber + ".pdf") &&
                                attachment.getContentType().equals("application/pdf"));
    }
}
