package com.alleskoenner.backend.emails.internal.ak_internal.error_notification;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;

@SpringBootTest
@Profile( {"manual-testing", "test"})
@Disabled("This test is only for manually testing push notifications. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class ErrorNotificationEmailServiceManualTest {

    @Autowired
    ErrorNotificationEmailService errorNotificationEmailService;

    @Test
    void shouldSendAnErrorNotificationEmail() throws InterruptedException {
        var testException = new RuntimeException("Test exception");
        errorNotificationEmailService.sendErrorNotificationEmail(testException);

        Thread.sleep(1000); // Wait for the email to be sent
    }
}