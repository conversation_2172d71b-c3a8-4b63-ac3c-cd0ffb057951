package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download;

import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download.business_central.PdfDownloadBusinessCentralClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingException;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class HandymanOrdersPdfDownloadServiceTest {
    @Mock
    private HandymanCompanyIdFetcher companyIdFetcher;
    @Mock
    private PdfDownloadBusinessCentralClient pdfDownloadBusinessCentralClient;
    @InjectMocks
    private HandymanOrdersPdfDownloadService service;

    private final LoginEmail loginEmail = new LoginEmail("<EMAIL>");

    @Test
    void downloadPdf_shouldReturnFlux_whenValid() {
        when(companyIdFetcher.getHandymanCompanyIdByLoginEmail(loginEmail)).thenReturn(Mono.just("company-123"));
        Flux<DataBuffer> pdfFlux = Flux.empty();
        when(pdfDownloadBusinessCentralClient.downloadPdf(loginEmail, "company-123", "sys-id-123")).thenReturn(pdfFlux);

        var result = service.downloadPdf(loginEmail, "sys-id-123");

        StepVerifier.create(result)
                .consumeNextWith(flux -> assertThat(flux).isEqualTo(pdfFlux))
                .verifyComplete();
    }

    @Test
    void downloadPdf_shouldReturnError_whenSystemIdIsBlank() {
        var result = service.downloadPdf(loginEmail, " ");

        StepVerifier.create(result)
                .expectErrorSatisfies(err -> {
                    assertThat(err).isInstanceOf(UserFacingException.class);
                    assertThat(((UserFacingException) err).getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
                })
                .verify();
    }

    @Test
    void downloadPdf_shouldPropagateError_whenCompanyIdFetcherFails() {
        when(companyIdFetcher.getHandymanCompanyIdByLoginEmail(any())).thenReturn(Mono.error(new RuntimeException("fetcher failed")));

        var result = service.downloadPdf(loginEmail, "sys-id-123");

        StepVerifier.create(result)
                .expectErrorMatches(e -> e instanceof RuntimeException && e.getMessage().equals("fetcher failed"))
                .verify();
    }
}
