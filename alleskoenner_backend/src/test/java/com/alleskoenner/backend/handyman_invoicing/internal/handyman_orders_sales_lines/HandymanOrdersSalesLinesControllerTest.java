package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines;

import java.util.Collections;
import java.util.Map;
import com.alleskoenner.backend.TestSecurityConfig;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.OIDCUserMock;
import com.alleskoenner.backend.shared.UserFacingException;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

@WebFluxTest(HandymanOrdersSalesLinesController.class)
@Import(TestSecurityConfig.class)
@ActiveProfiles("test")
class HandymanOrdersSalesLinesControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @Autowired
    private OIDCUserMock oidcUserMock;

    @MockitoBean
    private HandymanOrdersSalesLinesService service;

    @Test
    void itShouldReturnOkWhenUpdateIsSuccessful() {
        when(service.updateSalesOrderLines(Mockito.any(LoginEmail.class), Mockito.any(SalesOrderLinesUpdateRequest.class)))
                .thenReturn(Mono.empty());

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .put()
                .uri("/handyman-orders-sales-lines")
                .bodyValue(Map.of(
                        "salesOrderId", "order123",
                        "salesOrderLines", Collections.emptyList()
                ))
                .exchange()

                .expectStatus().isOk();
    }

    @Test
    void itShouldReturnBadRequestWhenServiceThrowsUserFacingException() {
        var errorMessage = "Bad request";
        when(service.updateSalesOrderLines(Mockito.any(LoginEmail.class), Mockito.any(SalesOrderLinesUpdateRequest.class)))
                .thenReturn(Mono.error(new UserFacingException(HttpStatus.BAD_REQUEST, errorMessage)));

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .put()
                .uri("/handyman-orders-sales-lines")
                .bodyValue(Map.of("INVALID", "INVALID"))
                .exchange()

                .expectStatus().isBadRequest()
                .expectBody().jsonPath("$.message").isEqualTo(errorMessage);
    }

    @Test
    void itShouldReturnInternalServerErrorWhenServiceThrowsException() {
        when(service.updateSalesOrderLines(Mockito.any(LoginEmail.class), Mockito.any(SalesOrderLinesUpdateRequest.class)))
                .thenReturn(Mono.error(new RuntimeException("Some error that should not be visible for user.")));

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .put()
                .uri("/handyman-orders-sales-lines")
                .bodyValue(Map.of(
                        "salesOrderId", "order123",
                        "salesOrderLines", Collections.emptyList()
                ))
                .exchange()

                .expectStatus().isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR)
                .expectBody().jsonPath("$.message").isEqualTo("An error occurred while updating sales order lines.");
    }

    @Test
    void itShouldFailWhenTokenIsNotSet() {
        webTestClient
                .put()
                .uri("/handyman-orders-sales-lines")
                .exchange()
                .expectStatus().isUnauthorized();
    }
}