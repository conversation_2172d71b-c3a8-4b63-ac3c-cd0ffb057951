package com.alleskoenner.backend.handyman_invoicing.internal.business_central;

import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.shared.LoginEmail;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;

@SpringBootTest
@Profile( {"manual-testing", "test"})
@Disabled("This test is only for manually testing the appointment coordination process. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class HandymanCompanyIdFetcherManualTest {

    @Autowired
    HandymanCompanyIdFetcher handymanCompanyIdFetcher;

    @Test
    @DisplayName("Manually test BC client")
    void startAppointmentCoordination() {
        var list = handymanCompanyIdFetcher.getHandymanCompanyIdByLoginEmail(new LoginEmail("<EMAIL>")).block();
        System.out.println(list);
        // Should fetch only once from BC because of caching
        var list2 = handymanCompanyIdFetcher.getHandymanCompanyIdByLoginEmail(new LoginEmail("<EMAIL>")).block();
        System.out.println(list2);
    }
}