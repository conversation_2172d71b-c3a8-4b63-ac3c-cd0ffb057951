package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central;

import java.io.IOException;
import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class SalesOrderInformationClientTest {

    private static MockWebServer mockWebServer;

    private SalesOrderInformationClient salesOrderInformationClient;

    @Mock
    private SalesOrderInformationUrlBuilder salesOrderInformationUrlBuilder;

    @Mock
    private HandymanCompanyIdFetcher handymanCompanyIdFetcher;

    private final LoginEmail loginEmail = new LoginEmail("<EMAIL>");
    private final String companyId = "45972317-3811-f011-9346-002248e4ed6a";

    @BeforeAll
    static void startServer() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
    }

    @AfterAll
    static void shutdownServer() throws IOException {
        mockWebServer.shutdown();
    }

    @BeforeEach
    void setUp() {
        var webClient = WebClient.builder()
                .baseUrl(mockWebServer.url("/").toString())
                .build();

        salesOrderInformationClient = new SalesOrderInformationClient(
                webClient,
                salesOrderInformationUrlBuilder,
                handymanCompanyIdFetcher
        );

        when(salesOrderInformationUrlBuilder.buildSalesOrderInformationRequestUrl(anyString()))
                .thenReturn("v2.0/tenant/env/api/itv/handymanApp/v2.0/companies(" + companyId + ")/salesOrderInformationList");
    }

    @Test
    void fetchSalesOrderInformationList_shouldReturnItems_whenResponseIsSuccessful() {
        var successResponse = """
                {
                  "@odata.context": "https://api.businesscentral.dynamics.com/v2.0/tenant/env/$metadata#companies(45972317-3811-f011-9346-002248e4ed6a)/salesOrderInformationList",
                  "value": [
                    {
                      "@odata.etag": "etag1",
                      "orderNo": "ORD001",
                      "documentType": "Order",
                      "asSoonAsPossible": false,
                      "requestedDeliveryDatetime": "2023-01-01T10:00:00Z",
                      "sellToCustomerNo": "CUST001",
                      "amountIncludingVAT": 100.00,
                      "amountExcludingVAT": 93.46,
                      "sellToCustomerName": "Customer Name",
                      "sellToCustomerName2": "Customer Name 2",
                      "sellToAddress": "123 Main St",
                      "sellToCity": "Berlin",
                      "sellToPostCode": "10115",
                      "sellToPhoneNo": "123456789",
                      "sellToEmail": "<EMAIL>",
                      "status": "Open",
                      "systemId": "SYS001",
                      "systemCreatedAt": "2023-01-01T09:00:00Z",
                      "workDescription": "Work description",
                      "salesOrderLines": [
                        {
                          "@odata.etag": "etag-line1",
                          "documentType": "Order",
                          "documentNo": "ORD001",
                          "lineNo": 10000,
                          "systemId": "line-sys-id-1",
                          "type": "Resource",
                          "no": "R0020",
                          "description": "Standard Arbeitsstunde",
                          "quantity": 1,
                          "unitOfMeasure": "Hour",
                          "unitPrice": 81.9,
                          "amount": 81.9,
                          "vatProdPostingGroup": "VAT7",
                          "vatPercent": 7,
                          "vatAmount": 87.63
                        }
                      ]
                    }
                  ]
                }
                """;

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(successResponse));

        var result =
                salesOrderInformationClient.fetchSalesOrderInformationList(loginEmail, companyId);

        StepVerifier.create(result)
                .assertNext(items -> {
                    assertThat(items).hasSize(1);
                    var item = items.getFirst();
                    assertThat(item.orderNo()).isEqualTo("ORD001");
                    assertThat(item.documentType()).isEqualTo("Order");
                    assertThat(item.asSoonAsPossible()).isFalse();
                    assertThat(item.requestedDeliveryDatetime()).isEqualTo("2023-01-01T10:00:00Z");
                    assertThat(item.sellToCustomerNo()).isEqualTo("CUST001");
                    assertThat(item.amountIncludingVAT()).isEqualByComparingTo("100.00");
                    assertThat(item.amountExcludingVAT()).isEqualByComparingTo("93.46");
                    assertThat(item.sellToCustomerName()).isEqualTo("Customer Name");
                    assertThat(item.sellToCustomerName2()).isEqualTo("Customer Name 2");
                    assertThat(item.sellToAddress()).isEqualTo("123 Main St");
                    assertThat(item.sellToCity()).isEqualTo("Berlin");
                    assertThat(item.sellToPostCode()).isEqualTo("10115");
                    assertThat(item.sellToPhoneNo()).isEqualTo("123456789");
                    assertThat(item.sellToEmail()).isEqualTo("<EMAIL>");
                    assertThat(item.status()).isEqualTo("Open");
                    assertThat(item.systemId()).isEqualTo("SYS001");
                    assertThat(item.systemCreatedAt()).isEqualTo("2023-01-01T09:00:00Z");
                    assertThat(item.workDescription()).isEqualTo("Work description");

                    // Verify sales order lines
                    assertThat(item.salesOrderLines()).hasSize(1);
                    var line = item.salesOrderLines().getFirst();
                    assertThat(line.documentType()).isEqualTo("Order");
                    assertThat(line.documentNo()).isEqualTo("ORD001");
                    assertThat(line.lineNo()).isEqualTo(10000);
                    assertThat(line.type()).isEqualTo("Resource");
                    assertThat(line.no()).isEqualTo("R0020");
                    assertThat(line.description()).isEqualTo("Standard Arbeitsstunde");
                    assertThat(line.quantity()).isEqualByComparingTo("1");
                    assertThat(line.unitOfMeasure()).isEqualTo("Hour");
                    assertThat(line.unitPrice()).isEqualByComparingTo("81.9");
                    assertThat(line.amount()).isEqualByComparingTo("81.9");
                    assertThat(line.vatProdPostingGroup()).isEqualTo("VAT7");
                    assertThat(line.vatPercent()).isEqualByComparingTo("7");
                    assertThat(line.vatAmount()).isEqualByComparingTo("87.63");
                })
                .verifyComplete();
    }

    @Test
    void fetchSalesOrderInformationList_shouldReturnError_whenResponseIsError() {
        var errorResponse = """
                {
                  "error": {
                    "code": "BadRequest",
                    "message": "Invalid request"
                  }
                }
                """;

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(400)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(errorResponse));

        var result =
                salesOrderInformationClient.fetchSalesOrderInformationList(loginEmail, companyId);

        StepVerifier.create(result)
                .expectErrorMatches(error -> error instanceof BusinessCentralErrorResponseException &&
                        error.getMessage().contains("Error fetching Sales Order Information List for handyman with <NAME_EMAIL>") &&
                        error.getMessage().contains("Status: 400") &&
                        error.getMessage().contains("Body: " + errorResponse))
                .verify();
    }
}
