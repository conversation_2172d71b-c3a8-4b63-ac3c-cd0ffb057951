package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting;

import com.alleskoenner.backend.emails.InvoiceEmailServiceInterface;
import com.alleskoenner.backend.emails.InvoiceEmailValues;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download.HandymanOrdersPdfDownloadService;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.dtos.PostedSalesOrderInformationItemResponse;
import com.alleskoenner.backend.shared.LoginEmail;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class InvoiceEmailSenderTest {

    @Mock
    private HandymanOrdersPdfDownloadService pdfDownloadService;

    @Mock
    private InvoiceEmailServiceInterface invoiceEmailService;

    @InjectMocks
    private InvoiceEmailSender invoiceEmailSender;

    @Captor
    private ArgumentCaptor<InvoiceEmailValues> invoiceEmailValuesCaptor;

    private final LoginEmail loginEmail = new LoginEmail("<EMAIL>");
    private final String salesOrderSystemId = "system-id-123";

    @Test
    void shouldDownloadPdfAndSendEmail() {
        var postedSalesOrder = createMockPostedSalesOrder();
        var pdfData = "PDF content".getBytes();
        DataBuffer dataBuffer = new DefaultDataBufferFactory().wrap(pdfData);

        when(pdfDownloadService.downloadPdf(any(LoginEmail.class), anyString()))
                .thenReturn(Mono.just(Flux.just(dataBuffer)));

        when(invoiceEmailService.sendInvoiceEmail(any(InvoiceEmailValues.class)))
                .thenReturn(Mono.empty());

        invoiceEmailSender.sendInvoiceEmail(postedSalesOrder, loginEmail, salesOrderSystemId);

        verify(pdfDownloadService).downloadPdf(any(LoginEmail.class), anyString());
        verify(invoiceEmailService).sendInvoiceEmail(invoiceEmailValuesCaptor.capture());

        var capturedValues = invoiceEmailValuesCaptor.getValue();
        assertThat(capturedValues.shipToAddressLine1()).isEqualTo("123 Main St");
        assertThat(capturedValues.shipToCity()).isEqualTo("Berlin");
        assertThat(capturedValues.shipToName()).isEqualTo("Customer Name");
        assertThat(capturedValues.shipToPostalCode()).isEqualTo("10115");
        assertThat(capturedValues.billToAddressLine1()).isEqualTo("123 Main St 2");
        assertThat(capturedValues.billToCity()).isEqualTo("Berlin 2");
        assertThat(capturedValues.billToName()).isEqualTo("Customer Name 2");
        assertThat(capturedValues.billToPostalCode()).isEqualTo("10112");
        assertThat(capturedValues.invoiceNumber()).isEqualTo("ORD001");
        assertThat(capturedValues.pdfAttachment()).isEqualTo(pdfData);
        assertThat(capturedValues.recipientEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    void shouldDoNothingOnPdfDownloadError() {
        var mock = mock(PostedSalesOrderInformationItemResponse.class);
        when(mock.systemId()).thenReturn(salesOrderSystemId);

        when(pdfDownloadService.downloadPdf(any(LoginEmail.class), anyString()))
                .thenReturn(Mono.error(new RuntimeException("Email sending error")));

        assertDoesNotThrow(() -> invoiceEmailSender.sendInvoiceEmail(mock, loginEmail, salesOrderSystemId));
    }

    @Test
    void shouldDoNothingOnEmailSendingError() {
        var postedSalesOrder = createMockPostedSalesOrder();
        var pdfData = "PDF content".getBytes();
        DataBuffer dataBuffer = new DefaultDataBufferFactory().wrap(pdfData);

        when(pdfDownloadService.downloadPdf(any(LoginEmail.class), anyString()))
                .thenReturn(Mono.just(Flux.just(dataBuffer)));
        when(invoiceEmailService.sendInvoiceEmail(any(InvoiceEmailValues.class)))
                .thenReturn(Mono.error(new RuntimeException("Email sending error")));

        assertDoesNotThrow(() -> invoiceEmailSender.sendInvoiceEmail(postedSalesOrder, loginEmail, salesOrderSystemId));
    }

    private PostedSalesOrderInformationItemResponse createMockPostedSalesOrder() {
        var mock = mock(PostedSalesOrderInformationItemResponse.class);
        when(mock.systemId()).thenReturn(salesOrderSystemId);
        when(mock.shipToAddress()).thenReturn("123 Main St");
        when(mock.shipToCity()).thenReturn("Berlin");
        when(mock.shipToName()).thenReturn("Customer Name");
        when(mock.orderNo()).thenReturn("ORD001");
        when(mock.shipToPostCode()).thenReturn("10115");
        when(mock.sellToEmail()).thenReturn("<EMAIL>");
        when(mock.billToAddress()).thenReturn("123 Main St 2");
        when(mock.billToCity()).thenReturn("Berlin 2");
        when(mock.billToName()).thenReturn("Customer Name 2");
        when(mock.billToPostCode()).thenReturn("10112");
        return mock;
    }
}
