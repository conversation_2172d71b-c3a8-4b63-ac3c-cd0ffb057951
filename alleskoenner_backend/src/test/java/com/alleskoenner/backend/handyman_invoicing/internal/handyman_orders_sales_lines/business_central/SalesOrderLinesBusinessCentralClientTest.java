package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central;

import java.io.IOException;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class SalesOrderLinesBusinessCentralClientTest {

    private static MockWebServer mockWebServer;
    private SalesOrderLinesBusinessCentralClient businessCentralClient;

    @Mock
    private SalesOrderLinesUrlBuilder urlBuilder;

    private final LoginEmail loginEmail = new LoginEmail("<EMAIL>");
    private final String companyId = "company123";
    private final String salesOrderId = "order123";
    private final String salesOrderLineId = "line123";

    @BeforeAll
    static void startServer() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
    }

    @AfterAll
    static void shutdownServer() throws IOException {
        mockWebServer.shutdown();
    }

    @BeforeEach
    void setUp() {
        WebClient webClient = WebClient.builder().baseUrl(mockWebServer.url("/").toString()).build();
        businessCentralClient = new SalesOrderLinesBusinessCentralClient(webClient, urlBuilder);
    }

    @Test
    void getSalesOrderLines_shouldReturnItems_whenResponseIsSuccessful() {
        // language=JSON
        var responseJson = """
                {
                  "value": [
                    { "id": "example-id1" },
                    { "id": "example-id2" }
                  ]
                }
                """;

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(responseJson));

        var result = businessCentralClient.getSalesOrderLines(loginEmail, companyId, salesOrderId);

        StepVerifier.create(result)
                .assertNext(lines -> {
                    assertThat(lines).hasSize(2);
                    assertThat(lines.get(0).id()).isEqualTo("example-id1");
                    assertThat(lines.get(1).id()).isEqualTo("example-id2");
                })
                .verifyComplete();
    }

    @Test
    void getSalesOrderLines_shouldReturnError_whenResponseIsError() {
        // language=JSON
        var errorBody = """
                {"error":"Bad Request"}
                """;

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(400)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(errorBody));

        var result = businessCentralClient.getSalesOrderLines(loginEmail, companyId, salesOrderId);

        StepVerifier.create(result)
                .expectErrorMatches(error ->
                        error instanceof BusinessCentralErrorResponseException &&
                                error.getMessage().contains("Error getting sales order lines for sales order " + salesOrderId) &&
                                error.getMessage().contains("Status: 400") &&
                                error.getMessage().contains("Body: " + errorBody))
                .verify();
    }

    @Test
    void deleteSalesOrderLine_shouldComplete_whenResponseIsSuccessful() {
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(204));
        when(urlBuilder.buildSalesOrderLineUrl(anyString(), anyString()))
                .thenReturn("salesorderlines/" + salesOrderLineId);

        var result = businessCentralClient.deleteSalesOrderLine(loginEmail, companyId, salesOrderLineId);

        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void deleteSalesOrderLine_shouldReturnError_whenResponseIsError() {
        when(urlBuilder.buildSalesOrderLineUrl(anyString(), anyString()))
                .thenReturn("salesorderlines/" + salesOrderLineId);
        // language=JSON
        var errorBody = """
                {"error":"Bad Request"}
                """;

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(400)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(errorBody));

        var result = businessCentralClient.deleteSalesOrderLine(loginEmail, companyId, salesOrderLineId);

        StepVerifier.create(result)
                .expectErrorMatches(error ->
                        error instanceof BusinessCentralErrorResponseException &&
                                error.getMessage().contains("Error deleting sales order line with id " + salesOrderLineId) &&
                                error.getMessage().contains("Status: 400") &&
                                error.getMessage().contains("Body: " + errorBody))
                .verify();
    }
}