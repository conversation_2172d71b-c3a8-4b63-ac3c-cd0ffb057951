package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download;

import com.alleskoenner.backend.TestSecurityConfig;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.OIDCUserMock;
import com.alleskoenner.backend.shared.UserFacingErrorMessage;
import com.alleskoenner.backend.shared.UserFacingException;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.context.annotation.Import;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@WebFluxTest(HandymanOrdersPdfDownloadController.class)
@Import( {TestSecurityConfig.class, OIDCUserMock.class})
@ActiveProfiles("test")
class HandymanOrdersPdfDownloadControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @Autowired
    private OIDCUserMock oidcUserMock;

    @MockitoBean
    private HandymanOrdersPdfDownloadService handymanOrdersPdfDownloadService;

    @Test
    void itShouldReturnPdfFileWhenDownloadIsSuccessful() {
        Flux<DataBuffer> pdfFlux = Flux.empty();
        when(handymanOrdersPdfDownloadService.downloadPdf(Mockito.any(LoginEmail.class), Mockito.eq("sys-id-123")))
                .thenReturn(Mono.just(pdfFlux));

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .get()
                .uri("/handyman-orders/sys-id-123/pdf")
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentType(MediaType.APPLICATION_PDF)
                .expectHeader().valueEquals("Content-Disposition", "attachment; filename=invoice-sys-id-123.pdf");
    }

    @Test
    void itShouldReturnBadRequestWhenSalesInvoiceSystemIdIsInvalid() {
        when(handymanOrdersPdfDownloadService.downloadPdf(Mockito.any(LoginEmail.class), Mockito.eq(" ")))
                .thenReturn(Mono.error(new UserFacingException(HttpStatus.BAD_REQUEST, "Sales invoice system ID is required")));

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .get()
                .uri("/handyman-orders/ /pdf")
                .exchange()
                .expectStatus().isBadRequest()
                .expectBody(UserFacingErrorMessage.class)
                .value(msg -> assertThat(msg.getMessage()).isEqualTo("Sales invoice system ID is required"));
    }

    @Test
    void itShouldReturnInternalServerErrorOnUnexpectedException() {
        when(handymanOrdersPdfDownloadService.downloadPdf(Mockito.any(LoginEmail.class), Mockito.eq("sys-id-err")))
                .thenReturn(Mono.error(new RuntimeException("Unexpected error")));

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .get()
                .uri("/handyman-orders/sys-id-err/pdf")
                .exchange()
                .expectStatus().is5xxServerError()
                .expectBody(UserFacingErrorMessage.class)
                .value(msg -> assertThat(msg.getMessage()).isEqualTo("An error occurred while downloading the PDF."));
    }

    @Test
    void itShouldFailWhenTokenIsNotSet() {
        webTestClient
                .get()
                .uri("/handyman-orders/sys-id-123/pdf")
                .exchange()
                .expectStatus().isUnauthorized();
    }
}
