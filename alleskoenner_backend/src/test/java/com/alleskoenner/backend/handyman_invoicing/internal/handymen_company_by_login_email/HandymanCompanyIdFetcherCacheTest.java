package com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email;

import java.time.Duration;
import java.util.List;
import com.alleskoenner.backend.shared.LoginEmail;
import com.github.benmanes.caffeine.cache.AsyncCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class HandymanCompanyIdFetcherCacheTest {

    @Mock
    private HandymenBcClient handymanBcClient;

    private HandymanCompanyIdFetcherCache cache;

    @BeforeEach
    void setUp() {
        AsyncCache<String, List<Handyman>> asyncCache = Caffeine.newBuilder()
                .expireAfterWrite(Duration.ofMinutes(30))
                .maximumSize(100)
                .buildAsync();

        cache = new HandymanCompanyIdFetcherCache(handymanBcClient, asyncCache);
    }

    @Test
    void shouldUseCacheOnSecondCall() {
        var mockHandyman = mock(Handyman.class);
        var dummyList = List.of(mockHandyman, mockHandyman);
        when(handymanBcClient.fetchHandymen()).thenReturn(Mono.just(dummyList));

        final var loginEmail = new LoginEmail("<EMAIL>");
        var firstResult = cache.getAllHandymen(loginEmail).block();
        var secondResult = cache.getAllHandymen(loginEmail).block();

        assertThat(firstResult).isEqualTo(dummyList);
        assertThat(secondResult).isEqualTo(dummyList);

        verify(handymanBcClient, times(1)).fetchHandymen();
    }

    @Test
    void shouldNotUseCacheOnDifferentSecondCall() {
        var mockHandyman = mock(Handyman.class);
        var dummyList = List.of(mockHandyman, mockHandyman);
        when(handymanBcClient.fetchHandymen()).thenReturn(Mono.just(dummyList));

        cache.getAllHandymen(new LoginEmail("<EMAIL>")).block();
        cache.getAllHandymen(new LoginEmail("<EMAIL>")).block();

        verify(handymanBcClient, times(2)).fetchHandymen();
    }
}
