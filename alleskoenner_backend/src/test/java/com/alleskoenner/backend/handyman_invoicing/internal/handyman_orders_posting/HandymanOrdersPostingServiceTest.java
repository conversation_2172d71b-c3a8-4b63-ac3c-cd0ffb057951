package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting;

import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.PostedSalesOrdersClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.dtos.PostedSalesOrderInformationItemResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.business_central.SalesOrderPostingClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload.SignatureHashCalculator;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload.SignaturePayloadBuilder;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload.business_central.SignatureUploadClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatusCode;
import org.springframework.web.reactive.function.client.ClientResponse;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class HandymanOrdersPostingServiceTest {

    @Mock
    private SalesOrderPostingClient salesOrderPostingClient;
    @Mock
    private PostedSalesOrdersClient postedSalesOrdersClient;
    @Mock
    private InvoiceEmailSender invoiceEmailSender;
    @Mock
    private HandymanCompanyIdFetcher companyIdFetcher;
    @Mock
    private ErrorNotificationEmailServiceInterface errorNotificationEmailService;
    @Mock
    private SignatureHashCalculator signatureHashCalculator;
    @Mock
    private SignaturePayloadBuilder signaturePayloadBuilder;
    @Mock
    private SignatureUploadClient signatureUploadClient;

    @InjectMocks
    private HandymanOrdersPostingService handymanOrdersPostingService;

    private final LoginEmail loginEmail = new LoginEmail("<EMAIL>");
    private final String salesOrderSystemId = "system-id-123";
    private final String caseNumber = "ORD001";
    private final String companyId = "company-id-123";
    private final String customerSignature = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAAQABAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBkQgUobHB0fAjM+HxFQkSQlJicvEzNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKAP/2Q==";

    @BeforeEach
    void setUp() {
        when(companyIdFetcher.getHandymanCompanyIdByLoginEmail(loginEmail)).thenReturn(Mono.just(companyId));
    }

    @Test
    void shouldPostSalesOrderAndFetchPostedOrder() {
        var mockResponse = mock(PostedSalesOrderInformationItemResponse.class);
        when(salesOrderPostingClient.postSalesOrder(eq(loginEmail), eq(companyId), eq(salesOrderSystemId)))
                .thenReturn(Mono.empty());
        when(postedSalesOrdersClient.fetchPostedSalesOrder(eq(loginEmail), eq(companyId), eq(caseNumber)))
                .thenReturn(Mono.just(mockResponse));

        var result = handymanOrdersPostingService.postSalesOrderInvoice(
                loginEmail, salesOrderSystemId, caseNumber);

        StepVerifier.create(result)
                .expectNext(mockResponse)
                .verifyComplete();

        verify(invoiceEmailSender).sendInvoiceEmail(eq(mockResponse), eq(loginEmail), eq(salesOrderSystemId));
    }

    @Test
    void shouldHandlePostingError() {
        var mockedClientResponse = mock(ClientResponse.class);
        when(mockedClientResponse.statusCode()).thenReturn(HttpStatusCode.valueOf(500));
        var expectedException =
                new BusinessCentralErrorResponseException("Error posting sales order", mockedClientResponse, "Error body");

        var mockResponse = mock(PostedSalesOrderInformationItemResponse.class);
        when(salesOrderPostingClient.postSalesOrder(eq(loginEmail), eq(companyId), eq(salesOrderSystemId)))
                .thenReturn(Mono.error(expectedException));
        when(postedSalesOrdersClient.fetchPostedSalesOrder(eq(loginEmail), eq(companyId), eq(caseNumber)))
                .thenReturn(Mono.just(mockResponse));

        var result = handymanOrdersPostingService.postSalesOrderInvoice(loginEmail, salesOrderSystemId, caseNumber);

        StepVerifier.create(result)
                .expectErrorMatches(error -> error instanceof BusinessCentralErrorResponseException
                        && error.getMessage().contains("Error posting sales order"))
                .verify();
    }

    @Test
    void shouldPropagateError_whenFetchingPostedOrderFails() {
        var mockedClientResponse = mock(ClientResponse.class);
        when(mockedClientResponse.statusCode()).thenReturn(HttpStatusCode.valueOf(500));
        var expectedException =
                new BusinessCentralErrorResponseException("Error fetching posted order", mockedClientResponse, "Error body");

        when(salesOrderPostingClient.postSalesOrder(eq(loginEmail), eq(companyId), eq(salesOrderSystemId)))
                .thenReturn(Mono.empty());
        when(postedSalesOrdersClient.fetchPostedSalesOrder(eq(loginEmail), eq(companyId), eq(caseNumber)))
                .thenReturn(Mono.error(expectedException));

        var result = handymanOrdersPostingService.postSalesOrderInvoice(
                loginEmail, salesOrderSystemId, caseNumber);

        StepVerifier.create(result)
                .expectErrorMatches(error -> error instanceof BusinessCentralErrorResponseException
                        && error.getMessage().contains("Error fetching posted order"))
                .verify();
    }

    @Test
    void shouldHandleEmailSendingError() {
        var mockResponse = mock(PostedSalesOrderInformationItemResponse.class);
        when(salesOrderPostingClient.postSalesOrder(eq(loginEmail), eq(companyId), eq(salesOrderSystemId)))
                .thenReturn(Mono.empty());
        when(postedSalesOrdersClient.fetchPostedSalesOrder(eq(loginEmail), eq(companyId), eq(caseNumber)))
                .thenReturn(Mono.just(mockResponse));

        Mockito.doThrow(new RuntimeException("Email sending error"))
                .when(invoiceEmailSender).sendInvoiceEmail(eq(mockResponse), eq(loginEmail), eq(salesOrderSystemId));

        var result = handymanOrdersPostingService.postSalesOrderInvoice(
                loginEmail, salesOrderSystemId, caseNumber);

        StepVerifier.create(result)
                .expectNext(mockResponse)
                .verifyComplete();
    }

    @Test
    void uploadSignatureToSalesOrder_shouldCompleteSuccessfully_whenAllServicesWork() {
        var signatureHash = "b3b0c44298fc1c149afbf4c8996fb924";
        var payload = "{\"base64Payload\":\"test-payload\"}";

        when(signatureHashCalculator.calculateSha256Hash(customerSignature)).thenReturn(signatureHash);
        when(signaturePayloadBuilder.buildBase64Payload(caseNumber, customerSignature, signatureHash)).thenReturn(payload);
        when(signatureUploadClient.uploadSignature(loginEmail, companyId, payload)).thenReturn(Mono.empty());

        var result = handymanOrdersPostingService.uploadSignatureToSalesOrder(loginEmail, customerSignature, caseNumber);

        StepVerifier.create(result)
                .verifyComplete();

        verify(signatureHashCalculator).calculateSha256Hash(customerSignature);
        verify(signaturePayloadBuilder).buildBase64Payload(caseNumber, customerSignature, signatureHash);
        verify(signatureUploadClient).uploadSignature(loginEmail, companyId, payload);
    }

    @Test
    void uploadSignatureToSalesOrder_shouldReturnUserFacingException_whenHashCalculationFails() {
        when(signatureHashCalculator.calculateSha256Hash(customerSignature))
                .thenThrow(new RuntimeException("Hash calculation failed"));

        var result = handymanOrdersPostingService.uploadSignatureToSalesOrder(loginEmail, customerSignature, caseNumber);

        StepVerifier.create(result)
                .expectErrorSatisfies(error -> {
                    assert error instanceof UserFacingException;
                    assert error.getMessage().equals("Fehler beim Upload der Unterschrift. Bitte versuchen Sie es erneut.");
                })
                .verify();
    }

    @Test
    void uploadSignatureToSalesOrder_shouldReturnUserFacingException_whenPayloadBuildingFails() {
        var signatureHash = "b3b0c44298fc1c149afbf4c8996fb924";

        when(signatureHashCalculator.calculateSha256Hash(customerSignature)).thenReturn(signatureHash);
        when(signaturePayloadBuilder.buildBase64Payload(caseNumber, customerSignature, signatureHash))
                .thenThrow(new RuntimeException("Payload building failed"));

        var result = handymanOrdersPostingService.uploadSignatureToSalesOrder(loginEmail, customerSignature, caseNumber);

        StepVerifier.create(result)
                .expectErrorSatisfies(error -> {
                    assert error instanceof UserFacingException;
                    assert error.getMessage().equals("Fehler beim Upload der Unterschrift. Bitte versuchen Sie es erneut.");
                })
                .verify();
    }

    @Test
    void uploadSignatureToSalesOrder_shouldReturnUserFacingException_whenUploadFails() {
        var signatureHash = "b3b0c44298fc1c149afbf4c8996fb924";
        var payload = "{\"base64Payload\":\"test-payload\"}";

        when(signatureHashCalculator.calculateSha256Hash(customerSignature)).thenReturn(signatureHash);
        when(signaturePayloadBuilder.buildBase64Payload(caseNumber, customerSignature, signatureHash)).thenReturn(payload);
        when(signatureUploadClient.uploadSignature(loginEmail, companyId, payload))
                .thenReturn(Mono.error(new RuntimeException("Upload failed")));

        var result = handymanOrdersPostingService.uploadSignatureToSalesOrder(loginEmail, customerSignature, caseNumber);

        StepVerifier.create(result)
                .expectErrorSatisfies(error -> {
                    assert error instanceof UserFacingException;
                    assert error.getMessage().equals("Fehler beim Upload der Unterschrift. Bitte versuchen Sie es erneut.");
                })
                .verify();
    }
}
