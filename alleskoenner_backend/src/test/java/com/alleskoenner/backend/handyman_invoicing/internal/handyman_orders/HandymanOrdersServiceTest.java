package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.SalesOrderInformationClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos.SalesOrderInformationItemResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos.SalesOrderLineResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import static org.assertj.core.api.Assertions.assertThat;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatusCode;
import org.springframework.web.reactive.function.client.ClientResponse;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class HandymanOrdersServiceTest {

    @Mock
    private SalesOrderInformationClient salesOrderInformationClient;
    @Mock
    private HandymanCompanyIdFetcher handymanCompanyIdFetcher;

    @Mock
    private HandymanInvoicingConfig config;

    @InjectMocks
    private HandymanOrdersService handymanOrdersService;

    private final LoginEmail loginEmail = new LoginEmail("<EMAIL>");
    private final Duration acceptanceTimeoutWindow = Duration.ofMinutes(5);

    @BeforeEach
    void setUp() {
        when(handymanCompanyIdFetcher.getHandymanCompanyIdByLoginEmail(loginEmail)).thenReturn(Mono.just("some-company-id"));
    }

    @Test
    void getHandymanOrders_shouldReturnHandymanOrdersItems_whenClientReturnsItems() {
        var requestedDeliveryDatetime = "2023-01-02T10:00:00Z";
        var systemCreatedAt = "2023-01-01T09:00:00Z";
        var clientResponse = getSalesOrderInformationItemResponses(false, requestedDeliveryDatetime, systemCreatedAt);

        when(config.getDefaultAsapWindow()).thenReturn(acceptanceTimeoutWindow);
        when(salesOrderInformationClient.fetchSalesOrderInformationList(eq(loginEmail), any()))
                .thenReturn(Mono.just(clientResponse));

        var result = handymanOrdersService.getHandymanOrders(loginEmail);

        StepVerifier.create(result)
                .consumeNextWith(responses -> {
                    assertThat(responses).hasSize(1);
                    HandymanOrdersResponse response = responses.getFirst();
                    assertThat(response.caseNumber()).isEqualTo("ORD001");
                    assertThat(response.addressLine1()).isEqualTo("Ship To Address");
                    assertThat(response.city()).isEqualTo("Ship To City");
                    assertThat(response.postalCode()).isEqualTo("Ship To PostCode");
                    assertThat(response.description()).isEqualTo("Work description");
                    assertThat(response.isAsap()).isFalse();
                    assertThat(response.scheduledDateTime()).isEqualTo(ZonedDateTime.parse(requestedDeliveryDatetime));

                    ZonedDateTime expectedExpirationDateTime = ZonedDateTime.parse(systemCreatedAt).plus(acceptanceTimeoutWindow);
                    assertThat(response.expirationDateTime()).isEqualTo(expectedExpirationDateTime);
                })
                .verifyComplete();
    }

    @Test
    void getHandymanOrders_shouldReturnHandymanOrdersItems_whenClientReturnsItems_forAsap() {
        var clientResponse = getSalesOrderInformationItemResponses(true, "2023-01-02T10:00:00Z", "2023-01-01T09:00:00Z");

        when(config.getDefaultAsapWindow()).thenReturn(acceptanceTimeoutWindow);
        when(salesOrderInformationClient.fetchSalesOrderInformationList(eq(loginEmail), any()))
                .thenReturn(Mono.just(clientResponse));

        var result = handymanOrdersService.getHandymanOrders(loginEmail);

        StepVerifier.create(result)
                .consumeNextWith(responses -> {
                    assertThat(responses).hasSize(1);
                    HandymanOrdersResponse response = responses.getFirst();
                    assertThat(response.isAsap()).isTrue();
                    assertThat(response.scheduledDateTime()).isNull();
                })
                .verifyComplete();
    }

    @Test
    void getHandymanOrders_shouldPropagateError_whenClientThrowsException() {
        ClientResponse mockedClientResponse = mock(ClientResponse.class);
        when(mockedClientResponse.statusCode()).thenReturn(HttpStatusCode.valueOf(500));
        BusinessCentralErrorResponseException expectedException =
                new BusinessCentralErrorResponseException("Error message", mockedClientResponse, "Error body");

        when(salesOrderInformationClient.fetchSalesOrderInformationList(eq(loginEmail), any()))
                .thenReturn(Mono.error(expectedException));

        var result = handymanOrdersService.getHandymanOrders(loginEmail);

        StepVerifier.create(result)
                .expectErrorMatches(error -> error instanceof BusinessCentralErrorResponseException
                        && error.getMessage().contains("Error message"))
                .verify();
    }

    @Test
    void getHandymanOrders_shouldHandleEmptyList_whenClientReturnsEmptyList() {
        when(salesOrderInformationClient.fetchSalesOrderInformationList(eq(loginEmail), any()))
                .thenReturn(Mono.just(Collections.emptyList()));

        var result = handymanOrdersService.getHandymanOrders(loginEmail);

        StepVerifier.create(result)
                .consumeNextWith(responses -> assertThat(responses).isEmpty())
                .verifyComplete();
    }

    @NotNull
    private static List<SalesOrderInformationItemResponse> getSalesOrderInformationItemResponses(boolean asSoonAsPossible, String requestedDeliveryDatetime, String systemCreatedAt) {
        List<SalesOrderLineResponse> salesOrderLines = List.of(
                new SalesOrderLineResponse(
                        "etag-line1",
                        "Order",
                        "ORD001",
                        10000,
                        "line-sys-id-1",
                        "Resource",
                        "R0020",
                        "Standard Arbeitsstunde",
                        new BigDecimal("1"),
                        "Hour",
                        new BigDecimal("81.9"),
                        new BigDecimal("81.9"),
                        "VAT7",
                        new BigDecimal("7"),
                        new BigDecimal("87.63")
                )
        );

        return List.of(
                new SalesOrderInformationItemResponse(
                        "etag1",
                        "ORD001",
                        "Order",
                        asSoonAsPossible,
                        requestedDeliveryDatetime,
                        "CUST001",
                        new BigDecimal("100.00"),
                        new BigDecimal("93.46"),
                        "Customer Name",
                        "Customer Name 2",
                        "123 Main St",
                        "Berlin",
                        "10115",
                        "123456789",
                        "<EMAIL>",
                        "Ship To Name",
                        "Ship To Address",
                        "Ship To Address 2",
                        "Ship To City",
                        "Ship To PostCode",
                        "Ship To PhoneNo",
                        "Bill To Name",
                        "Bill To Address",
                        "Bill To Address 2",
                        "Bill To City",
                        "Bill To PostCode",
                        "Open",
                        "SYS001",
                        systemCreatedAt,
                        "Work description",
                        salesOrderLines
                )
        );
    }
}
