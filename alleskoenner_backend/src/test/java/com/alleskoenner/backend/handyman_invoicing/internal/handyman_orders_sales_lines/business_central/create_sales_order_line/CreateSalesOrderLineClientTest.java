package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.create_sales_order_line;

import java.io.IOException;
import java.math.BigDecimal;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.SalesOrderLineCreateRequest;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class CreateSalesOrderLineClientTest {
    private static MockWebServer mockWebServer;
    private CreateSalesOrderLineClient client;

    @Mock
    private CreateSalesOrderLineUrlBuilder urlBuilder;

    private final LoginEmail loginEmail = new LoginEmail("<EMAIL>");
    private final String companyId = "company123";
    private final String salesOrderId = "order123";

    @BeforeAll
    static void startServer() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
    }

    @AfterAll
    static void shutdownServer() throws IOException {
        mockWebServer.shutdown();
    }

    @BeforeEach
    void setUp() {
        WebClient webClient = WebClient.builder().baseUrl(mockWebServer.url("/").toString()).build();
        client = new CreateSalesOrderLineClient(webClient, urlBuilder);
        when(urlBuilder.buildSalesOrderLinesUrl(anyString(), anyString())).thenReturn("");
    }

    @Test
    void createSalesOrderLine_shouldReturnItem_whenResponseIsSuccessful() {
        var responseJson = """
                {
                  "@odata.etag": "etag1",
                  "documentType": "Order",
                  "documentNo": "ORD001",
                  "lineNo": 10000,
                  "systemId": "line123",
                  "type": "Resource",
                  "no": "R0020",
                  "description": "desc",
                  "quantity": 2,
                  "unitOfMeasure": "Hour",
                  "unitPrice": 10.0,
                  "amount": 20.0,
                  "vatProdPostingGroup": "VAT7",
                  "vatPercent": 7,
                  "vatAmount": 1.4
                }
                """;
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(responseJson));
        var request = new SalesOrderLineCreateRequest(
                "Resource",
                "R0020",
                new BigDecimal("2")
        );

        var result = client.createSalesOrderLine(companyId, salesOrderId, request);

        StepVerifier.create(result)
                .assertNext(item -> {
                    assertThat(item.odataETag()).isEqualTo("etag1");
                    assertThat(item.documentType()).isEqualTo("Order");
                    assertThat(item.systemId()).isEqualTo("line123");
                    assertThat(item.quantity()).isEqualByComparingTo(new BigDecimal("2"));
                })
                .verifyComplete();
    }

    @Test
    void createSalesOrderLine_shouldReturnError_whenResponseIsError() {
        var errorBody = """
                {"error":"Bad Request"}
                """;
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(400)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(errorBody));
        var request = new SalesOrderLineCreateRequest(
                "Resource",
                "R0020",
                new BigDecimal("2")
        );

        var result = client.createSalesOrderLine(companyId, salesOrderId, request);

        StepVerifier.create(result)
                .expectErrorMatches(error ->
                        error instanceof BusinessCentralErrorResponseException &&
                                error.getMessage().contains("Error creating sales order line " + request + " on sales order " + salesOrderId) &&
                                error.getMessage().contains("Status: 400") &&
                                error.getMessage().contains("Body: " + errorBody))
                .verify();
    }
}
