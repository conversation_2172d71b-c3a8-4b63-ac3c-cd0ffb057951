package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting;

import com.alleskoenner.backend.TestSecurityConfig;
import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.dtos.PostedSalesOrderInformationItemResponse;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.OIDCUserMock;
import com.alleskoenner.backend.shared.UserFacingException;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

@WebFluxTest(HandymanOrdersPostingController.class)
@Import(TestSecurityConfig.class)
@ActiveProfiles("test")
class HandymanOrdersPostingControllerTest {

    private static final String CUSTOMER_SIGNATURE = "data:image/jpeg;base64,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";

    @Autowired
    private WebTestClient webTestClient;

    @Autowired
    private OIDCUserMock oidcUserMock;

    @MockitoBean
    private HandymanOrdersPostingService handymanOrdersPostingService;


    @Test
    void itShouldPostSalesOrderInvoice() {
        var caseNumber = "ORD001";
        var salesOrderSystemId = "system-id-123";
        var request = new HandymanOrdersPostingRequest(salesOrderSystemId, CUSTOMER_SIGNATURE);

        var mockResponse = Mockito.mock(PostedSalesOrderInformationItemResponse.class);

        when(handymanOrdersPostingService.uploadSignatureToSalesOrder(any(LoginEmail.class), eq(CUSTOMER_SIGNATURE), eq(caseNumber)))
                .thenReturn(Mono.empty());
        when(handymanOrdersPostingService.postSalesOrderInvoice(any(LoginEmail.class), eq(salesOrderSystemId), eq(caseNumber)))
                .thenReturn(Mono.just(mockResponse));
        when(mockResponse.systemCreatedAt()).thenReturn("2025-05-09T12:00:00Z");
        when(mockResponse.postedSalesOrderLines()).thenReturn(null);
        when(mockResponse.asSoonAsPossible()).thenReturn(true);


        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .post()
                .uri("/handyman-orders/{caseNumber}/post", caseNumber)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk();
    }

    @Test
    void itShouldFailWhenTokenIsNotSet() {
        var caseNumber = "ORD001";
        var request = new HandymanOrdersPostingRequest("system-id-123", CUSTOMER_SIGNATURE);

        webTestClient
                .post()
                .uri("/handyman-orders/{caseNumber}/post", caseNumber)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isUnauthorized();
    }

    @Test
    void itShouldFailWhenSalesOrderSystemIdIsEmpty() {
        var caseNumber = "ORD001";
        var request = new HandymanOrdersPostingRequest("", CUSTOMER_SIGNATURE);

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .post()
                .uri("/handyman-orders/{caseNumber}/post", caseNumber)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isBadRequest();
    }

    @Test
    void itShouldFailWhenCustomerSignatureIsEmpty() {
        var caseNumber = "ORD001";
        var request = new HandymanOrdersPostingRequest("system-id-123", "");

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .post()
                .uri("/handyman-orders/{caseNumber}/post", caseNumber)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isBadRequest();
    }

    @Test
    void itShouldReturnForbiddenWhenFeatureFlagIsDisabled() {
        HandymanInvoicingConfig config = new HandymanInvoicingConfig();
        config.setSalesOrderPostingEnabled(false);
        HandymanOrdersPostingController controller = new HandymanOrdersPostingController(handymanOrdersPostingService, config);

        var caseNumber = "ORD002";
        var request = new HandymanOrdersPostingRequest("system-id-456", CUSTOMER_SIGNATURE);

        WebTestClient.bindToController(controller)
                .build()
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .post()
                .uri("/handyman-orders/{caseNumber}/post", caseNumber)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isForbidden();
    }

    @Test
    void itShouldReturnInternalServerError_whenSignatureUploadFails() {
        var caseNumber = "ORD003";
        var salesOrderSystemId = "system-id-789";
        var request = new HandymanOrdersPostingRequest(salesOrderSystemId, CUSTOMER_SIGNATURE);

        when(handymanOrdersPostingService.uploadSignatureToSalesOrder(any(LoginEmail.class), eq(CUSTOMER_SIGNATURE), eq(caseNumber)))
                .thenReturn(Mono.error(new UserFacingException(HttpStatus.INTERNAL_SERVER_ERROR, "Fehler beim Upload der Unterschrift. Bitte versuchen Sie es erneut.")));
        when(handymanOrdersPostingService.postSalesOrderInvoice(any(LoginEmail.class), eq(salesOrderSystemId), eq(caseNumber)))
                .thenReturn(Mono.empty());

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .post()
                .uri("/handyman-orders/{caseNumber}/post", caseNumber)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().is5xxServerError()
                .expectBody()
                .jsonPath("$.message").isEqualTo("Fehler beim Upload der Unterschrift. Bitte versuchen Sie es erneut.");
    }
}
