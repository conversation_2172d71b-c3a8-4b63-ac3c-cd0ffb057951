package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.SalesOrderLinesBusinessCentralClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.create_sales_order_line.CreateSalesOrderLineService;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.BcSalesOrderLineResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.SalesOrderLineCreateRequest;
import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import org.assertj.core.api.SoftAssertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatusCode;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class HandymanOrdersSalesLinesServiceTest {

    @Mock
    private HandymanCompanyIdFetcher companyIdFetcher;
    @Mock
    private SalesOrderLinesBusinessCentralClient client;
    @Mock
    private CreateSalesOrderLineService createSalesOrderLineService;

    @InjectMocks
    private HandymanOrdersSalesLinesService service;

    private final LoginEmail loginEmail = new LoginEmail("<EMAIL>");
    private final String companyId = "company123";
    private final String salesOrderId = "order123";

    @Test
    void updateSalesOrderLines_shouldError_whenSalesOrderIdIsNull() {
        var request = new SalesOrderLinesUpdateRequest(null, Collections.emptyList());

        StepVerifier.create(service.updateSalesOrderLines(loginEmail, request))
                .expectErrorMatches(error ->
                        error instanceof ResponseStatusException &&
                                ((ResponseStatusException) error).getStatusCode().is4xxClientError() &&
                                ((ResponseStatusException) error).getReason().equals("Sales order ID is required"))
                .verify();
    }

    @Test
    void updateSalesOrderLines_shouldError_whenSalesOrderLinesIsNull() {
        var request = new SalesOrderLinesUpdateRequest(salesOrderId, null);
        StepVerifier.create(service.updateSalesOrderLines(loginEmail, request))
                .expectErrorMatches(error ->
                        error instanceof ResponseStatusException &&
                                ((ResponseStatusException) error).getStatusCode().is4xxClientError() &&
                                ((ResponseStatusException) error).getReason().equals("Sales order lines list is required"))
                .verify();
    }

    @Test
    void updateSalesOrderLines_shouldHandleBusinessError_whenClientThrowsException() {
        when(companyIdFetcher.getHandymanCompanyIdByLoginEmail(loginEmail))
                .thenReturn(Mono.just(companyId));
        var mockResponse = mock(ClientResponse.class);
        when(mockResponse.statusCode()).thenReturn(HttpStatusCode.valueOf(502));
        BusinessCentralErrorResponseException bcException =
                new BusinessCentralErrorResponseException("bcError", mockResponse, "errorBody");
        when(client.getSalesOrderLines(eq(loginEmail), eq(companyId), eq(salesOrderId)))
                .thenReturn(Mono.error(bcException));

        var request = new SalesOrderLinesUpdateRequest(salesOrderId, Collections.emptyList());

        var assertions = new SoftAssertions();
        StepVerifier.create(service.updateSalesOrderLines(loginEmail, request))
                .expectErrorSatisfies(error -> {
                    assertions.assertThat(error).isInstanceOf(BusinessCentralErrorResponseException.class);
                    BusinessCentralErrorResponseException bcError = (BusinessCentralErrorResponseException) error;
                    assertions.assertThat(bcError.getMessage()).isEqualTo("bcError\nBusiness Central responded with an error:\nStatus: 502\nBody: errorBody\n");
                })
                .verify();
        assertions.assertAll();
    }

    @Test
    void updateSalesOrderLines_shouldDeleteAndCreate_whenValidRequest() {
        when(companyIdFetcher.getHandymanCompanyIdByLoginEmail(loginEmail))
                .thenReturn(Mono.just(companyId));
        List<BcSalesOrderLineResponse> existing = List.of(
                new BcSalesOrderLineResponse("old1"),
                new BcSalesOrderLineResponse("old2")
        );
        when(client.getSalesOrderLines(eq(loginEmail), eq(companyId), eq(salesOrderId)))
                .thenReturn(Mono.just(existing));

        when(client.deleteSalesOrderLine(eq(loginEmail), eq(companyId), eq("old1")))
                .thenReturn(Mono.empty());
        when(client.deleteSalesOrderLine(eq(loginEmail), eq(companyId), eq("old2")))
                .thenReturn(Mono.empty());

        SalesOrderLineRequest lineRequest = new SalesOrderLineRequest(
                "type",
                "objNo",
                new BigDecimal("1")
        );
        when(createSalesOrderLineService.createSalesOrderLine(eq(companyId), eq(salesOrderId), any(SalesOrderLineCreateRequest.class)))
                .thenReturn(Mono.empty());

        var request = new SalesOrderLinesUpdateRequest(salesOrderId, List.of(lineRequest));

        StepVerifier.create(service.updateSalesOrderLines(loginEmail, request))
                .verifyComplete();
    }
}