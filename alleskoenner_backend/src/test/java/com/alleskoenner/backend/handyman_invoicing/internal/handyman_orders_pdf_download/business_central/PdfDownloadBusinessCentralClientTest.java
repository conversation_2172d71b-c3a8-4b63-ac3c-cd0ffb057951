package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download.business_central;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import static org.assertj.core.api.Assertions.assertThat;
import org.assertj.core.api.SoftAssertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

class PdfDownloadBusinessCentralClientTest {
    static MockWebServer mockWebServer;
    PdfDownloadBusinessCentralClient client;
    PdfDownloadUrlBuilder urlBuilder;
    final LoginEmail loginEmail = new LoginEmail("<EMAIL>");
    final String companyId = "company-123";
    final String salesInvoiceSystemId = "sys-id-123";

    @BeforeAll
    static void startServer() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
    }

    @AfterAll
    static void shutdownServer() throws IOException {
        mockWebServer.shutdown();
    }

    @BeforeEach
    void setUp() {
        WebClient webClient = WebClient.builder().baseUrl(mockWebServer.url("/").toString()).build();
        urlBuilder = mock(PdfDownloadUrlBuilder.class);
        when(urlBuilder.buildPdfDownloadUrl(companyId, salesInvoiceSystemId)).thenReturn("/pdf-url");
        client = new PdfDownloadBusinessCentralClient(webClient, urlBuilder);
    }

    @Test
    void downloadPdf_shouldReturnPdfFlux_whenResponseIsPdf() {
        byte[] pdfBytes = "PDFDATA".getBytes(StandardCharsets.UTF_8);
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", MediaType.APPLICATION_PDF_VALUE)
                .setBody(new okio.Buffer().write(pdfBytes)));

        Flux<DataBuffer> result = client.downloadPdf(loginEmail, companyId, salesInvoiceSystemId);

        StepVerifier.create(result)
                .consumeNextWith(dataBuffer -> {
                    byte[] actual = new byte[dataBuffer.readableByteCount()];
                    dataBuffer.read(actual);
                    assertThat(actual).isEqualTo(pdfBytes);
                })
                .verifyComplete();
    }

    @Test
    void downloadPdf_shouldReturnError_whenResponseIsError() {
        String errorBody = "{\"error\":\"Not found\"}";
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(404)
                .setHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .setBody(errorBody));

        Flux<DataBuffer> result = client.downloadPdf(loginEmail, companyId, "sys-id-err");

        var assertions = new SoftAssertions();
        StepVerifier.create(result)
                .expectErrorSatisfies(error -> {
                    assertions.assertThat(error).isInstanceOf(BusinessCentralErrorResponseException.class);
                    assertions.assertThat(error.getMessage())
                            .contains("Error downloading PDF for sales invoice sys-id-err for handyman with <NAME_EMAIL>: {\"error\":\"Not found\"}");
                })
                .verify();
        assertions.assertAll();
    }
}
