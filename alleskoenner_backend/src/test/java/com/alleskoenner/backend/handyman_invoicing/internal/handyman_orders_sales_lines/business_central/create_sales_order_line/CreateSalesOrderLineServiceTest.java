package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.create_sales_order_line;

import java.math.BigDecimal;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos.SalesOrderLineResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.SalesOrderLineCreateRequest;
import com.alleskoenner.backend.shared.LoginEmail;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

class CreateSalesOrderLineServiceTest {
    @Test
    void createSalesOrderLine_delegatesToClient() {
        CreateSalesOrderLineClient client = Mockito.mock(CreateSalesOrderLineClient.class);
        CreateSalesOrderLineService service = new CreateSalesOrderLineService(client);
        LoginEmail loginEmail = new LoginEmail("<EMAIL>");
        String companyId = "company123";
        String salesOrderId = "order123";
        SalesOrderLineCreateRequest request = new SalesOrderLineCreateRequest(
                "Resource", "R0020", new BigDecimal("2")
        );
        SalesOrderLineResponse response = new SalesOrderLineResponse(
                "etag1", "Order", "ORD001", 10000, "line123", "Resource", "R0020", "desc",
                new BigDecimal("2"), "Hour", new BigDecimal("10.0"), new BigDecimal("20.0"), "VAT7", new BigDecimal("7"), new BigDecimal("1.4")
        );
        when(client.createSalesOrderLine(any(), any(), any())).thenReturn(Mono.just(response));

        StepVerifier.create(service.createSalesOrderLine(companyId, salesOrderId, request))
                .expectNext(response)
                .verifyComplete();
    }
}
