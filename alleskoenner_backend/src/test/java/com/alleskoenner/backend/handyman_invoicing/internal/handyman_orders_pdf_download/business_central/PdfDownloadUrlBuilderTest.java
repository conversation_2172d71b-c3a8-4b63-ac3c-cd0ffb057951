package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download.business_central;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class PdfDownloadUrlBuilderTest {
    @Test
    void buildPdfDownloadUrl_shouldReturnCorrectUrl() {
        HandymanInvoicingConfig config = mock(HandymanInvoicingConfig.class, RETURNS_DEEP_STUBS);
        when(config.getBusinessCentral().getBcTenantId()).thenReturn("tenant-1");
        when(config.getBusinessCentral().getBcEnvironment()).thenReturn("sandbox");
        PdfDownloadUrlBuilder urlBuilder = new PdfDownloadUrlBuilder(config);

        String url = urlBuilder.buildPdfDownloadUrl("company-123", "sys-id-123");

        assertThat(url).isEqualTo("v2.0/tenant-1/sandbox/api/v2.0/companies(company-123)/salesInvoices(sys-id-123)/pdfDocument/pdfDocumentContent");
    }
}
