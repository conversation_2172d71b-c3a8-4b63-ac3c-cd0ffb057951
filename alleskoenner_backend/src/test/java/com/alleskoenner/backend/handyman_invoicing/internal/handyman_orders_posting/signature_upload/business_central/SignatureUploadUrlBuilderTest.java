package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload.business_central;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SignatureUploadUrlBuilderTest {

    @Mock
    private HandymanInvoicingConfig config;

    @Mock
    private HandymanInvoicingConfig.BusinessCentral businessCentral;

    @InjectMocks
    private SignatureUploadUrlBuilder signatureUploadUrlBuilder;

    @BeforeEach
    void setUp() {
        when(config.getBusinessCentral()).thenReturn(businessCentral);
    }

    @Test
    void buildSignatureUploadUrl_shouldReturnCorrectUrl_whenValidCompanyIdProvided() {
        var companyId = "test-company-id";
        var tenantId = "test-tenant-id";
        var environment = "test-environment";

        when(businessCentral.getBcTenantId()).thenReturn(tenantId);
        when(businessCentral.getBcEnvironment()).thenReturn(environment);

        var result = signatureUploadUrlBuilder.buildSignatureUploadUrl(companyId);

        var expectedUrl = "v2.0/test-tenant-id/test-environment/ODataV4/HandymanApi_UploadSignature?company=test-company-id";
        assertThat(result).isEqualTo(expectedUrl);
    }

    @Test
    void buildSignatureUploadUrl_shouldHandleSpecialCharacters_whenCompanyIdContainsSpecialCharacters() {
        var companyId = "company-with-special-chars-123";
        var tenantId = "tenant-123";
        var environment = "env-456";

        when(businessCentral.getBcTenantId()).thenReturn(tenantId);
        when(businessCentral.getBcEnvironment()).thenReturn(environment);

        var result = signatureUploadUrlBuilder.buildSignatureUploadUrl(companyId);

        var expectedUrl = "v2.0/tenant-123/env-456/ODataV4/HandymanApi_UploadSignature?company=company-with-special-chars-123";
        assertThat(result).isEqualTo(expectedUrl);
    }
}
