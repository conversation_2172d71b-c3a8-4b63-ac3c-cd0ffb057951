package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class SalesOrderLinesUrlBuilderTest {

    private SalesOrderLinesUrlBuilder urlBuilder;

    @BeforeEach
    void setUp() {
        HandymanInvoicingConfig config = new HandymanInvoicingConfig();
        var bcConfig = new HandymanInvoicingConfig.BusinessCentral();
        bcConfig.setBcTenantId("tenant");
        bcConfig.setBcEnvironment("env");
        config.setBusinessCentral(bcConfig);
        urlBuilder = new SalesOrderLinesUrlBuilder(config);
    }

    @Test
    void buildSalesOrderLineUrl_shouldReturnCorrectUrl() {
        String url = urlBuilder.buildSalesOrderLineUrl("companyId", "lineId");
        assertThat(url)
                .isEqualTo("v2.0/tenant/env/api/v2.0/companies(companyId)/salesOrderLines(lineId)");
    }
}