package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.create_sales_order_line;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class CreateSalesOrderLineUrlBuilderTest {
    private CreateSalesOrderLineUrlBuilder urlBuilder;

    @BeforeEach
    void setUp() {
        HandymanInvoicingConfig config = new HandymanInvoicingConfig();
        var bcConfig = new HandymanInvoicingConfig.BusinessCentral();
        bcConfig.setBcTenantId("tenant");
        bcConfig.setBcEnvironment("env");
        config.setBusinessCentral(bcConfig);
        urlBuilder = new CreateSalesOrderLineUrlBuilder(config);
    }

    @Test
    void buildSalesOrderLinesUrl_shouldReturnCorrectUrl() {
        String url = urlBuilder.buildSalesOrderLinesUrl("companyId", "orderId");
        assertThat(url)
                .isEqualTo("v2.0/tenant/env/api/v2.0/companies(companyId)/salesOrders(orderId)/salesOrderLines");
    }
}
