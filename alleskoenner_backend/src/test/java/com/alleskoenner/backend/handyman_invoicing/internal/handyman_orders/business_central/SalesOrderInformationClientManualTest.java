package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central;

import com.alleskoenner.backend.shared.LoginEmail;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;

@SpringBootTest
@Profile( {"manual-testing", "test"})
@Disabled("This test is only for manually testing the appointment coordination process. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class SalesOrderInformationClientManualTest {

    @Autowired
    SalesOrderInformationClient salesOrderInformationClient;

    @Test
    @DisplayName("Manually test BC client")
    void startAppointmentCoordination() {
        var loginEmail = new LoginEmail("<EMAIL>");
        var companyId = "45972317-3811-f011-9346-002248e4ed6a";

        var list = salesOrderInformationClient.fetchSalesOrderInformationList(loginEmail, companyId).block();
        System.out.println(list);
        // Should fetch only once from BC because of caching
        var list2 = salesOrderInformationClient.fetchSalesOrderInformationList(loginEmail, companyId).block();
        System.out.println(list2);
    }
}