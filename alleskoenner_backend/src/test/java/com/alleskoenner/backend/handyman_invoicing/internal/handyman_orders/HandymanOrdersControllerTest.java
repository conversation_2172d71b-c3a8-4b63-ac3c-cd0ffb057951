package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;

import com.alleskoenner.backend.TestSecurityConfig;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.OIDCUserMock;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

@WebFluxTest(HandymanOrdersController.class)
@Import(TestSecurityConfig.class)
@ActiveProfiles("test")
class HandymanOrdersControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @Autowired
    private OIDCUserMock oidcUserMock;

    @MockitoBean
    private HandymanOrdersService handymanOrdersService;

    @Test
    void itShouldReturnEmptyListWhenNoSalesOrderInformation() {
        when(handymanOrdersService.getHandymanOrders(Mockito.any(LoginEmail.class)))
                .thenReturn(Mono.just(Collections.emptyList()));

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .get()
                .uri("/handyman-orders")
                .exchange()
                .expectStatus().isOk()
                .expectBody(List.class).isEqualTo(Collections.emptyList());
    }

    @Test
    void itShouldReturnListOfHandymanOrders() {
        var orderLines = List.of(
                new HandymanOrdersLineResponse(
                        new java.math.BigDecimal("81.9"),
                        "Standard Arbeitsstunde",
                        "R0020",
                        new java.math.BigDecimal("1"),
                        "Resource",
                        "Hour",
                        new java.math.BigDecimal("81.9"),
                        "line-sys-id-1",
                        new java.math.BigDecimal("87.63"),
                        new java.math.BigDecimal("7"))
        );

        var handymanOrders = new HandymanOrdersResponse(
                "123 Main St",
                new java.math.BigDecimal("294.6"),
                new java.math.BigDecimal("315.22"),
                "ORD001",
                "Berlin",
                "Work description",
                "Hans Wurst",
                "<EMAIL>",
                ZonedDateTime.parse("2023-01-01T09:00:00Z"),
                false,
                orderLines,
                "0510 1234567890",
                "10115",
                ZonedDateTime.parse("2023-01-01T12:00:00Z"),
                "some-system-id",
                "Bill To Name",
                "Bill To Address",
                "Bill To Address 2",
                "Bill To City",
                "Bill To PostCode");

        when(handymanOrdersService.getHandymanOrders(Mockito.any(LoginEmail.class)))
                .thenReturn(Mono.just(List.of(handymanOrders)));

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .get()
                .uri("/handyman-orders")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$[0].addressLine1").isEqualTo("123 Main St")
                .jsonPath("$[0].city").isEqualTo("Berlin")
                .jsonPath("$[0].description").isEqualTo("Work description")
                .jsonPath("$[0].expirationDateTime").isEqualTo("2023-01-01T09:00:00Z")
                .jsonPath("$[0].isAsap").isEqualTo(false)
                .jsonPath("$[0].caseNumber").isEqualTo("ORD001")
                .jsonPath("$[0].postalCode").isEqualTo("10115")
                .jsonPath("$[0].scheduledDateTime").isEqualTo("2023-01-01T12:00:00Z")
                .jsonPath("$[0].amountIncludingVAT").isEqualTo(315.22)
                .jsonPath("$[0].amountExcludingVAT").isEqualTo(294.6)
                .jsonPath("$[0].orderLines[0].type").isEqualTo("Resource")
                .jsonPath("$[0].orderLines[0].lineObjectNumber").isEqualTo("R0020")
                .jsonPath("$[0].orderLines[0].description").isEqualTo("Standard Arbeitsstunde")
                .jsonPath("$[0].orderLines[0].quantity").isEqualTo(1)
                .jsonPath("$[0].orderLines[0].unitOfMeasure").isEqualTo("Hour")
                .jsonPath("$[0].orderLines[0].unitPrice").isEqualTo(81.9)
                .jsonPath("$[0].orderLines[0].amount").isEqualTo(81.9)
                .jsonPath("$[0].orderLines[0].systemId").isEqualTo("line-sys-id-1")
                .jsonPath("$[0].orderLines[0].vatPercent").isEqualTo(7)
                .jsonPath("$[0].orderLines[0].vatAmount").isEqualTo(87.63);
    }

    @Test
    void itShouldFailWhenTokenIsNotSet() {
        when(handymanOrdersService.getHandymanOrders(Mockito.any(LoginEmail.class)))
                .thenReturn(Mono.just(Collections.emptyList()));

        webTestClient
                .get()
                .uri("/handyman-orders")
                .exchange()
                .expectStatus().isUnauthorized();
    }
}
