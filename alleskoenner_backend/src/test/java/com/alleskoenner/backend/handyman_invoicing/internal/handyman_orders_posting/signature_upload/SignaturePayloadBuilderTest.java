package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload;

import java.util.Base64;

import com.fasterxml.jackson.databind.ObjectMapper;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SignaturePayloadBuilderTest {

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private SignaturePayloadBuilder signaturePayloadBuilder;

    private ObjectMapper realObjectMapper;

    @BeforeEach
    void setUp() {
        realObjectMapper = new ObjectMapper();
        signaturePayloadBuilder = new SignaturePayloadBuilder(realObjectMapper);
    }

    @Test
    void buildBase64Payload_shouldReturnValidPayload_whenValidInputProvided() throws Exception {
        var caseNumber = "101001";
        var customerSignature = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAAQABAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBkQgUobHB0fAjM+HxFQkSQlJicvEzNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKAP/2Q==";
        var signatureHash = "b3b0c44298fc1c149afbf4c8996fb924";

        var result = signaturePayloadBuilder.buildBase64Payload(caseNumber, customerSignature, signatureHash);

        assertThat(result).isNotNull();

        var outerPayload = realObjectMapper.readTree(result);
        assertThat(outerPayload.has("base64Payload")).isTrue();

        var base64Payload = outerPayload.get("base64Payload").asText();
        var decodedPayload = new String(Base64.getDecoder().decode(base64Payload));
        var innerPayload = realObjectMapper.readTree(decodedPayload);

        assertThat(innerPayload.get("salesOrderNumber").asText()).isEqualTo(caseNumber);
        assertThat(innerPayload.get("signatureBase64")
                .asText()).isEqualTo("/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAAQABAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBkQgUobHB0fAjM+HxFQkSQlJicvEzNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKAP/2Q==");
        assertThat(innerPayload.get("signatureHash").asText()).isEqualTo(signatureHash);
    }

    @Test
    void buildBase64Payload_shouldThrowException_whenInvalidDataUrlProvided() {
        var caseNumber = "101001";
        var invalidCustomerSignature = "invalid-data-url";
        var signatureHash = "b3b0c44298fc1c149afbf4c8996fb924";

        assertThatThrownBy(() -> signaturePayloadBuilder.buildBase64Payload(caseNumber, invalidCustomerSignature, signatureHash))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Invalid data URL format");
    }

    @Test
    void buildBase64Payload_shouldThrowException_whenDataUrlMissingComma() {
        var caseNumber = "101001";
        var invalidCustomerSignature = "data:image/jpeg;base64";
        var signatureHash = "b3b0c44298fc1c149afbf4c8996fb924";

        assertThatThrownBy(() -> signaturePayloadBuilder.buildBase64Payload(caseNumber, invalidCustomerSignature, signatureHash))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Invalid data URL format - missing comma");
    }

    @Test
    void buildBase64Payload_shouldThrowRuntimeException_whenJsonProcessingFails() throws Exception {
        signaturePayloadBuilder = new SignaturePayloadBuilder(objectMapper);

        when(objectMapper.writeValueAsString(any())).thenThrow(new RuntimeException("JSON processing failed"));

        var caseNumber = "101001";
        var customerSignature = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAAQABAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBkQgUobHB0fAjM+HxFQkSQlJicvEzNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKAP/2Q==";
        var signatureHash = "b3b0c44298fc1c149afbf4c8996fb924";

        assertThatThrownBy(() -> signaturePayloadBuilder.buildBase64Payload(caseNumber, customerSignature, signatureHash))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("JSON processing failed");
    }
}
