package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting;

import com.alleskoenner.backend.shared.LoginEmail;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;

@SpringBootTest
@Profile( {"manual-testing", "test"})
@Disabled("This test is only for manually testing the appointment coordination process. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class HandymanOrdersPostingServiceManualTest {

    @Autowired
    HandymanOrdersPostingService handymanOrdersPostingService;

    @Test
    @DisplayName("Start appointment coordination process for manual testing")
    void uploadSignatureImage() {

        handymanOrdersPostingService.uploadSignatureToSalesOrder(
                new LoginEmail("<EMAIL>"),
                "data:image/jpeg;base64,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",
                "101001"
        ).block();

    }
}