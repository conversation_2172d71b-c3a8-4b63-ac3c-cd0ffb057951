package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SignatureHashCalculatorTest {

    @InjectMocks
    private SignatureHashCalculator signatureHashCalculator;

    @Test
    void shouldReturnCorrectHash_whenValidDataUrlProvided() {
        var dataUrl = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAAQABAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBkQgUobHB0fAjM+HxFQkSQlJicvEzNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKAP/2Q==";

        var result = signatureHashCalculator.calculateSha256Hash(dataUrl);

        assertThat(result).isNotNull();
        assertThat(result).hasSize(64);
        assertThat(result).matches("[a-f0-9]{64}");
    }

    @Test
    void shouldReturnSameHash_whenSameDataProvided() {
        var dataUrl = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAAQABAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBkQgUobHB0fAjM+HxFQkSQlJicvEzNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKAP/2Q==";

        var result1 = signatureHashCalculator.calculateSha256Hash(dataUrl);
        var result2 = signatureHashCalculator.calculateSha256Hash(dataUrl);

        assertThat(result1).isEqualTo(result2);
    }

    @Test
    void shouldReturnDifferentHash_whenDifferentDataProvided() {
        var dataUrl1 = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAAQABAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBkQgUobHB0fAjM+HxFQkSQlJicvEzNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKAP/2Q==";
        var dataUrl2 = "data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGAWWkNrAAAAABJRU5ErkJggg==";

        var result1 = signatureHashCalculator.calculateSha256Hash(dataUrl1);
        var result2 = signatureHashCalculator.calculateSha256Hash(dataUrl2);

        assertThat(result1).isNotEqualTo(result2);
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "image/jpeg;base64,data",
            "invalid-data-url",
            "http://example.com/image.jpg"
    })
    void shouldThrowException_whenInvalidDataUrlFormat(String invalidDataUrl) {
        assertThatThrownBy(() -> signatureHashCalculator.calculateSha256Hash(invalidDataUrl))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Invalid data URL format");
    }

    @Test
    void shouldThrowException_whenDataUrlIsNull() {
        assertThatThrownBy(() -> signatureHashCalculator.calculateSha256Hash(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("DataUrl cannot be null");
    }

    @Test
    void shouldThrowException_whenDataUrlIsEmpty() {
        assertThatThrownBy(() -> signatureHashCalculator.calculateSha256Hash(""))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("DataUrl cannot be empty");
    }

    @Test
    void shouldThrowException_whenDataUrlIsWhitespace() {
        assertThatThrownBy(() -> signatureHashCalculator.calculateSha256Hash("   "))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("DataUrl cannot be empty");
    }

    @Test
    void shouldThrowException_whenDataUrlMissingComma() {
        var invalidDataUrl = "data:image/jpeg;base64";

        assertThatThrownBy(() -> signatureHashCalculator.calculateSha256Hash(invalidDataUrl))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Invalid data URL format - missing comma separator");
    }

    @Test
    void shouldThrowException_whenDataUrlEndsWithComma() {
        var invalidDataUrl = "data:image/jpeg;base64,";

        assertThatThrownBy(() -> signatureHashCalculator.calculateSha256Hash(invalidDataUrl))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Invalid data URL format - empty data part");
    }

    @Test
    void shouldThrowException_whenBase64DataIsEmpty() {
        var invalidDataUrl = "data:image/jpeg;base64,   ";

        assertThatThrownBy(() -> signatureHashCalculator.calculateSha256Hash(invalidDataUrl))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Invalid data URL - empty base64 data");
    }

    @Test
    void shouldThrowRuntimeException_whenInvalidBase64Data() {
        var invalidDataUrl = "data:image/jpeg;base64,invalid-base64-data!@#$";

        assertThatThrownBy(() -> signatureHashCalculator.calculateSha256Hash(invalidDataUrl))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Base64 decoding of signature image failed.");
    }

    @Test
    void shouldWorkWithDifferentImageTypes() {
        var pngDataUrl = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGAWWkNrAAAAABJRU5ErkJggg==";
        var jpegDataUrl = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcgSlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwKDAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAAQABAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBkQgUobHB0fAjM+HxFQkSQlJicvEzNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9/KKKKAP/2Q==";

        var pngResult = signatureHashCalculator.calculateSha256Hash(pngDataUrl);
        var jpegResult = signatureHashCalculator.calculateSha256Hash(jpegDataUrl);

        assertThat(pngResult).isNotNull().hasSize(64).matches("[a-f0-9]{64}");
        assertThat(jpegResult).isNotNull().hasSize(64).matches("[a-f0-9]{64}");
        assertThat(pngResult).isNotEqualTo(jpegResult);
    }

    @Test
    void shouldWorkWithMinimalValidDataUrl() {
        var minimalDataUrl = "data:,AA";

        var result = signatureHashCalculator.calculateSha256Hash(minimalDataUrl);

        assertThat(result).isNotNull().hasSize(64).matches("[a-f0-9]{64}");
    }

    @Test
    void shouldWorkWithComplexMimeType() {
        var complexDataUrl = "data:image/png;charset=utf-8;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGAWWkNrAAAAABJRU5ErkJggg==";

        var result = signatureHashCalculator.calculateSha256Hash(complexDataUrl);

        assertThat(result).isNotNull().hasSize(64).matches("[a-f0-9]{64}");
    }

    @Test
    void shouldReturnConsistentHashForKnownInput() {
        var dataUrl = "data:text/plain;base64,aGVsbG8="; // "hello" in Base64

        var result = signatureHashCalculator.calculateSha256Hash(dataUrl);

        var expectedHash = "2cf24dba5fb0a30e26e83b2ac5b9e29e1b161e5c1fa7425e73043362938b9824"; // expected hash of "hello"
        assertThat(result).isEqualTo(expectedHash);
    }
}