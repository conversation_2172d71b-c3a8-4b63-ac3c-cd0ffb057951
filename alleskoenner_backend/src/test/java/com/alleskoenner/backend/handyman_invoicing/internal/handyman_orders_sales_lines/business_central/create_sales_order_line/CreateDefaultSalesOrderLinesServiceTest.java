package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.create_sales_order_line;

import java.math.BigDecimal;

import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos.SalesOrderLineResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.SalesOrderLineCreateRequest;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@SpringBootTest
@ActiveProfiles("test")
class CreateDefaultSalesOrderLinesServiceTest {

    @MockitoBean
    CreateSalesOrderLineClient createSalesOrderLineClient;
    @Autowired
    CreateDefaultSalesOrderLinesService service;

    @Test
    void createDefaultSalesOrderLines_shouldCreateThreeDefaultLines() {
        String companyId = "company123";
        String salesOrderId = "order123";
        SalesOrderLineResponse mockResponse = new SalesOrderLineResponse(
                "etag1", "Order", "ORD001", 10000, "line123", "Resource", "R0020", "desc",
                new BigDecimal("1"), "Hour", new BigDecimal("10.0"), new BigDecimal("10.0"),
                "VAT7", new BigDecimal("7"), new BigDecimal("0.7")
        );
        when(createSalesOrderLineClient.createSalesOrderLine(eq(companyId), eq(salesOrderId), any(SalesOrderLineCreateRequest.class)))
                .thenReturn(Mono.just(mockResponse));


        Mono<Void> result = service.createDefaultSalesOrderLines(companyId, salesOrderId);


        StepVerifier.create(result).verifyComplete();

        verify(createSalesOrderLineClient, times(3)).createSalesOrderLine(eq(companyId), eq(salesOrderId), any(SalesOrderLineCreateRequest.class));
        ArgumentCaptor<SalesOrderLineCreateRequest> requestCaptor = ArgumentCaptor.forClass(SalesOrderLineCreateRequest.class);
        verify(createSalesOrderLineClient, times(3)).createSalesOrderLine(eq(companyId), eq(salesOrderId), requestCaptor.capture());

        var capturedRequests = requestCaptor.getAllValues();
        assertThat(capturedRequests).hasSize(3);
        var travelCharge = capturedRequests.getFirst();
        assertThat(travelCharge.lineType()).isEqualTo("Item");
        assertThat(travelCharge.lineObjectNumber()).isEqualTo("1000");
        assertThat(travelCharge.quantity()).isEqualByComparingTo(new BigDecimal("1"));
        var defaultWorkingHour = capturedRequests.get(1);
        assertThat(defaultWorkingHour.lineType()).isEqualTo("Resource");
        assertThat(defaultWorkingHour.lineObjectNumber()).isEqualTo("R0020");
        assertThat(defaultWorkingHour.quantity()).isEqualByComparingTo(new BigDecimal("1"));
        var smallPartsCharge = capturedRequests.get(2);
        assertThat(smallPartsCharge.lineType()).isEqualTo("Item");
        assertThat(smallPartsCharge.lineObjectNumber()).isEqualTo("1003");
        assertThat(smallPartsCharge.quantity()).isEqualByComparingTo(new BigDecimal("1"));
    }

    @Test
    void createDefaultSalesOrderLines_shouldHandleErrorFromClient() {
        String companyId = "company123";
        String salesOrderId = "order123";
        RuntimeException mockError = new RuntimeException("Test error");
        when(createSalesOrderLineClient.createSalesOrderLine(eq(companyId), eq(salesOrderId), any(SalesOrderLineCreateRequest.class)))
                .thenReturn(Mono.error(mockError));

        Mono<Void> result = service.createDefaultSalesOrderLines(companyId, salesOrderId);

        StepVerifier.create(result)
                .expectErrorSatisfies(error -> {
                    assertThat(error).isEqualTo(mockError);
                })
                .verify();
    }
}
