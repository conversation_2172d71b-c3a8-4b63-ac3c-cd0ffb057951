package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload.business_central;

import java.io.IOException;

import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class SignatureUploadClientTest {

    @Mock
    private SignatureUploadUrlBuilder signatureUploadUrlBuilder;

    private MockWebServer mockWebServer;
    private SignatureUploadClient signatureUploadClient;
    private LoginEmail loginEmail;

    @BeforeEach
    void setUp() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();

        var webClient = WebClient.builder()
                .baseUrl(mockWebServer.url("/").toString())
                .build();

        signatureUploadClient = new SignatureUploadClient(webClient, signatureUploadUrlBuilder);
        loginEmail = new LoginEmail("<EMAIL>");
    }

    @AfterEach
    void tearDown() throws IOException {
        mockWebServer.shutdown();
    }

    @Test
    void uploadSignature_shouldCompleteSuccessfully_whenResponseIsSuccessful() throws InterruptedException {
        var companyId = "test-company-id";
        var payload = "{\"base64Payload\":\"test-payload\"}";
        var url = "test-url";

        when(signatureUploadUrlBuilder.buildSignatureUploadUrl(companyId)).thenReturn(url);

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE));

        var result = signatureUploadClient.uploadSignature(loginEmail, companyId, payload);

        StepVerifier.create(result)
                .verifyComplete();

        var recordedRequest = mockWebServer.takeRequest();
        assertThat(recordedRequest.getMethod()).isEqualTo("POST");
        assertThat(recordedRequest.getPath()).isEqualTo("/" + url);
        assertThat(recordedRequest.getHeader("Content-Type")).isEqualTo(MediaType.APPLICATION_JSON_VALUE);
        assertThat(recordedRequest.getHeader("If-Match")).isEqualTo("*");
        assertThat(recordedRequest.getBody().readUtf8()).isEqualTo(payload);
    }

    @Test
    void uploadSignature_shouldReturnError_whenResponseIsError() {
        var companyId = "test-company-id";
        var payload = "{\"base64Payload\":\"test-payload\"}";
        var url = "test-url";
        var errorBody = "{\"error\":\"Upload failed\"}";

        when(signatureUploadUrlBuilder.buildSignatureUploadUrl(companyId)).thenReturn(url);

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(400)
                .setHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .setBody(errorBody));

        var result = signatureUploadClient.uploadSignature(loginEmail, companyId, payload);

        StepVerifier.create(result)
                .expectErrorSatisfies(error -> {
                    assertThat(error).isInstanceOf(BusinessCentralErrorResponseException.class);
                    assertThat(error.getMessage()).contains("Error uploading signature for handyman with <NAME_EMAIL>");
                })
                .verify();
    }

    @Test
    void uploadSignature_shouldReturnError_whenResponseIs500() {
        var companyId = "test-company-id";
        var payload = "{\"base64Payload\":\"test-payload\"}";
        var url = "test-url";
        var errorBody = "{\"error\":\"Internal server error\"}";

        when(signatureUploadUrlBuilder.buildSignatureUploadUrl(companyId)).thenReturn(url);

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(500)
                .setHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .setBody(errorBody));

        var result = signatureUploadClient.uploadSignature(loginEmail, companyId, payload);

        StepVerifier.create(result)
                .expectErrorSatisfies(error -> {
                    assertThat(error).isInstanceOf(BusinessCentralErrorResponseException.class);
                    assertThat(error.getMessage()).contains("Error uploading signature for handyman with <NAME_EMAIL>");
                })
                .verify();
    }
}
