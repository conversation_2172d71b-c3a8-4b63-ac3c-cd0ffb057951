package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation;

import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

@SpringBootTest
@Profile( {"manual-testing", "test"})
@Disabled("This test is only for manually testing the appointment coordination process. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class AppointmentRequestEscalationJobServiceManualTest {

    @MockitoSpyBean AppointmentCoordinationConfig config;
    @Autowired AppointmentRequestEscalationService service;

    @Test
    void startAppointmentRequestEscalation() throws InterruptedException {

        var escalationJobConfigMock = mock(AppointmentCoordinationConfig.EscalationJob.class);
        when(config.getEscalationJob()).thenReturn(escalationJobConfigMock);
        when(escalationJobConfigMock.isEnabled()).thenReturn(true);
        when(escalationJobConfigMock.getInitialDelayInSeconds()).thenReturn(0);
        when(escalationJobConfigMock.getFixedDelayInSeconds()).thenReturn(15);

        service.checkForAppointmentRequestToEscalate();

        Thread.sleep(10000); // wait for 10 seconds to let the async task finish
    }

}