package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_rejection;

import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.ClientResponse;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class AppointmentRequestRejectionClientTest {


    @InjectMocks
    private AppointmentRequestRejectionClient client;

    @Test
    void testMapResponseBodyToError_ResourceNotFound() {
        ClientResponse clientResponse = mock(ClientResponse.class);
        when(clientResponse.statusCode()).thenReturn(HttpStatus.BAD_REQUEST);

        when(clientResponse.bodyToMono(String.class))
                .thenReturn(Mono.just("BadRequest_ResourceNotFound: some details"));

        Mono<Void> result = client.mapResponseBodyToError(clientResponse, "requestedHandymanId123");

        StepVerifier.create(result)
                .expectErrorMatches(ex ->
                        ex instanceof BusinessCentralErrorResponseException
                                && ((BusinessCentralErrorResponseException) ex).response.statusCode() == HttpStatus.BAD_REQUEST
                                && ex.getMessage().contains("Error while getting requested handyman. ID: requestedHandymanId123.")
                )
                .verify();
    }

}
