package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.alleskoenner.backend.shared.LoginEmail;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;

@SpringBootTest
@Profile( {"manual-testing", "test"})
@Disabled("This test is only for manually testing the appointment request confirmation. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class AppointmentRequestConfirmationServiceManualTest {

    @Autowired
    AppointmentRequestConfirmationService appointmentRequestConfirmationService;

    @Test
    @DisplayName("Confirm appointment for manual testing")
    void startAppointmentCoordination() {
        appointmentRequestConfirmationService
                .confirmAndArchive(new LoginEmail("<EMAIL>"), "ab315d7e-b7df-ef11-9344-0022485ce5e0")
                .block();

    }
}
