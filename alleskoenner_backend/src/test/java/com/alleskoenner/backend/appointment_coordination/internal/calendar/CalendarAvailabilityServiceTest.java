package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import com.alleskoenner.backend.appointment_coordination.internal.MissingAppointmentRequestedDateTime;
import static com.alleskoenner.backend.appointment_coordination.internal.TestDataUtils.getDefaultHandymenWithPostalCodes;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_coordination.NoHandymanAvailableByCalendar;
import com.alleskoenner.backend.appointment_coordination.internal.handymen_by_postal_code.HandymanWithPostalCode;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class CalendarAvailabilityServiceTest {

    private static final ZonedDateTime ANY_ZONED_DATE_TIME = ZonedDateTime.parse("2025-02-11T14:30:00+01:00");

    @Mock
    private AvailabilityRequester availabilityRequester;
    @Mock
    private AvailabilityEvaluator availabilityEvaluator;

    @InjectMocks
    private CalendarAvailabilityService calendarAvailabilityService;

    @Test
    void getAvailableHandymenByCalendar_shouldReturnAvailableHandymen() {
        final var handymenWithPostalCodes = getDefaultHandymenWithPostalCodes("<EMAIL>", "<EMAIL>");
        var isAsSoonAsPossible = false;
        when(availabilityEvaluator.getAvailableHandymenEmailsFromResponse(any()))
                .thenReturn(List.of("<EMAIL>"));

        var resultMono = calendarAvailabilityService.getAvailableHandymenByCalendar(handymenWithPostalCodes, Optional.of(ANY_ZONED_DATE_TIME), isAsSoonAsPossible);

        StepVerifier.create(resultMono)
                .assertNext(result -> {
                    verify(availabilityRequester).getScheduleInformationList(any(), any(), eq(isAsSoonAsPossible));
                    verify(availabilityEvaluator).getAvailableHandymenEmailsFromResponse(any());
                })
                .verifyComplete();
    }

    @Test
    void getAvailableHandymenByCalendar_shouldResultInMonoError() {
        final var handymenWithPostalCodes = getDefaultHandymenWithPostalCodes("<EMAIL>", "<EMAIL>");
        var isAsSoonAsPossible = false;

        var resultMono = calendarAvailabilityService.getAvailableHandymenByCalendar(handymenWithPostalCodes, Optional.of(ANY_ZONED_DATE_TIME), isAsSoonAsPossible);

        when(availabilityRequester.getScheduleInformationList(any(), any(), eq(isAsSoonAsPossible)))
                .thenThrow(new RuntimeException("test exception"));

        StepVerifier.create(resultMono)
                .expectError()
                .verify();
    }

    @Test
    void getAvailableHandymenByCalendar_shouldResultInMonoErrorWhenNoHandymenAreAvailable() {
        final List<HandymanWithPostalCode> handymenWithPostalCodes = Collections.emptyList();

        var resultMono = calendarAvailabilityService.getAvailableHandymenByCalendar(handymenWithPostalCodes, Optional.of(ANY_ZONED_DATE_TIME), false);

        StepVerifier.create(resultMono)
                .consumeErrorWith(error -> {
                    assertThat(error)
                            .isInstanceOf(NoHandymanAvailableByCalendar.class)
                            .hasMessage("No handyman is currently available according to their calendar. The requested time was: 2025-02-11T14:30+01:00.");
                })
                .verify();
    }

    @Test
    void getAvailableHandymenByCalendar_shouldSetCorrectErrorTextWhenWhenNoHandymanAreAvailableAnRequestIsAsap() {
        final List<HandymanWithPostalCode> handymenWithPostalCodes = Collections.emptyList();

        var resultMono = calendarAvailabilityService.getAvailableHandymenByCalendar(handymenWithPostalCodes, Optional.of(ANY_ZONED_DATE_TIME), true);

        StepVerifier.create(resultMono)
                .consumeErrorWith(error -> {
                    assertThat(error)
                            .isInstanceOf(NoHandymanAvailableByCalendar.class)
                            .hasMessageContaining("No handyman is currently available according to their calendar. Time of the ASAP request: ");
                })
                .verify();
    }

    @Test
    void getAvailableHandymenByCalendar_shouldHandleWhenRequestedDateTimeIsEmpty() {

        final var handymenWithPostalCodes = getDefaultHandymenWithPostalCodes("<EMAIL>", "<EMAIL>");
        Optional<ZonedDateTime> requestedDateTime = Optional.empty();
        when(availabilityRequester.getScheduleInformationList(any(), eq(requestedDateTime), anyBoolean()))
                .thenThrow(new MissingAppointmentRequestedDateTime());

        var resultMono = calendarAvailabilityService.getAvailableHandymenByCalendar(handymenWithPostalCodes, requestedDateTime, false);

        StepVerifier.create(resultMono)
                .consumeErrorWith(error -> {
                    assertThat(error)
                            .isInstanceOf(MissingAppointmentRequestedDateTime.class)
                            .hasMessage("Requested DateTime is null, but should actually have been set when the appointment request is not Asap");
                })
                .verify();
    }
}
