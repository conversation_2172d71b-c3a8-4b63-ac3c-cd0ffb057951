package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.alleskoenner.backend.appointment_coordination.internal.calendar.CalendarBookingService;
import com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests.AppointmentRequestBcResponse;
import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import com.alleskoenner.backend.handyman_invoicing.CreateDefaultSalesOrderLinesServiceInterface;
import com.alleskoenner.backend.shared.LoginEmail;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
public class AppointmentRequestConfirmationServiceTest {

    @Mock
    private AppointmentRequestConfirmationClient confirmationClient;
    @Mock
    private AppointmentRequestArchivingClient archivingClient;
    @Mock
    private CalendarBookingService calendarBookingService;
    @Mock
    private AppointmentRequestGettingClient gettingClient;
    @Mock
    private SyncToHandymanCompanyClient syncToHandymanCompanyClient;
    @Mock
    private CreateDefaultSalesOrderLinesServiceInterface createDefaultSalesOrderLinesService;
    @Mock
    private ErrorNotificationEmailServiceInterface errorNotificationEmailService;
    @Mock
    private AppointmentConfirmationEmailSender emailSender;


    @InjectMocks
    private AppointmentRequestConfirmationService service;

    @Test
    public void confirmAndArchive_successfulFlow() {
        var loginEmailHandyman1 = new LoginEmail("<EMAIL>");
        var appointmentRequestId = "request1";
        var odataEtag = "odataEtag";
        var companyId = "company1";
        var handymanSalesOrderId = "salesOrder1";

        when(gettingClient.getAppointmentRequest(appointmentRequestId))
                .thenReturn(Mono.just(new AppointmentRequestBcResponse(
                        odataEtag,
                        null,
                        appointmentRequestId,
                        null,
                        null,
                        AppointmentRequestStatus.PENDING.getValue(),
                        null,
                        null,
                        null,
                        null,
                        false,
                        null
                )));
        when(confirmationClient.confirmAppointmentRequest(loginEmailHandyman1, appointmentRequestId, odataEtag))
                .thenReturn(Mono.empty());
        when(archivingClient.archiveAppointmentRequest(appointmentRequestId))
                .thenReturn(Mono.empty());

        var responseValueMock = mock(SyncToHandymanCompanyBcResponseValue.class);
        when(responseValueMock.companyId()).thenReturn(companyId);
        when(responseValueMock.handymanSalesOrderId()).thenReturn(handymanSalesOrderId);
        when(syncToHandymanCompanyClient.syncSalesOrderAndCustomerToHandymanCompany(appointmentRequestId))
                .thenReturn(Mono.just(responseValueMock));
        when(createDefaultSalesOrderLinesService.createDefaultSalesOrderLines(companyId, handymanSalesOrderId))
                .thenReturn(Mono.empty());
        when(calendarBookingService.findEarliestMatchingTimeSlotAndBookAppointment(eq(loginEmailHandyman1), any()))
                .thenReturn(Mono.empty());

        var result = service.confirmAndArchive(loginEmailHandyman1, appointmentRequestId);

        StepVerifier.create(result).verifyComplete();
        verify(confirmationClient).confirmAppointmentRequest(loginEmailHandyman1, appointmentRequestId, odataEtag);
        verify(createDefaultSalesOrderLinesService).createDefaultSalesOrderLines(companyId, handymanSalesOrderId);
        verify(archivingClient).archiveAppointmentRequest(appointmentRequestId);
        verify(emailSender).sendAppointmentConfirmationEmail(responseValueMock);
    }


    @Test
    public void confirmAndArchive_archivingFails_shouldCompleteWithoutError() {
        var loginEmailHandyman1 = new LoginEmail("<EMAIL>");
        var odataEtag = "odataEtag";
        var appointmentRequestId = "request1";
        var companyId = "company1";
        var handymanSalesOrderId = "salesOrder1";

        when(gettingClient.getAppointmentRequest(appointmentRequestId))
                .thenReturn(Mono.just(new AppointmentRequestBcResponse(
                        odataEtag,
                        null,
                        appointmentRequestId,
                        null,
                        null,
                        AppointmentRequestStatus.PENDING.getValue(),
                        null,
                        null,
                        null,
                        null,
                        false,
                        null)));

        when(confirmationClient.confirmAppointmentRequest(loginEmailHandyman1, appointmentRequestId, odataEtag))
                .thenReturn(Mono.empty());
        when(archivingClient.archiveAppointmentRequest(appointmentRequestId))
                .thenReturn(Mono.error(new RuntimeException("Fehler beim Archivieren")));

        var responseValueMock = mock(SyncToHandymanCompanyBcResponseValue.class);
        when(responseValueMock.companyId()).thenReturn(companyId);
        when(responseValueMock.handymanSalesOrderId()).thenReturn(handymanSalesOrderId);
        when(syncToHandymanCompanyClient.syncSalesOrderAndCustomerToHandymanCompany(appointmentRequestId))
                .thenReturn(Mono.just(responseValueMock));
        when(createDefaultSalesOrderLinesService.createDefaultSalesOrderLines(companyId, handymanSalesOrderId))
                .thenReturn(Mono.empty());
        when(calendarBookingService.findEarliestMatchingTimeSlotAndBookAppointment(eq(loginEmailHandyman1), any()))
                .thenReturn(Mono.empty());

        var result = service.confirmAndArchive(loginEmailHandyman1, appointmentRequestId);

        StepVerifier.create(result).verifyComplete();

        verify(confirmationClient).confirmAppointmentRequest(loginEmailHandyman1, appointmentRequestId, odataEtag);
        verify(createDefaultSalesOrderLinesService).createDefaultSalesOrderLines(companyId, handymanSalesOrderId);
        verify(archivingClient).archiveAppointmentRequest(appointmentRequestId);
        verify(emailSender).sendAppointmentConfirmationEmail(responseValueMock);
    }

}
