package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_rejection;

import com.alleskoenner.backend.TestSecurityConfig;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_request_rejection.dtos.AppointmentRequestRejectionRequest;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.OIDCUserMock;
import com.alleskoenner.backend.shared.UserFacingException;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.web.reactive.function.client.ClientResponse;
import reactor.core.publisher.Mono;

@WebFluxTest(AppointmentRequestRejectionController.class)
@Import(TestSecurityConfig.class)
@ActiveProfiles("test")
class AppointmentRequestRejectionControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @MockitoBean
    private AppointmentRequestRejectionService rejectionService;

    @Mock
    ClientResponse clientResponse;

    @Autowired
    private OIDCUserMock oidcUserMock;

    @Test
    void itShouldReturn200WhenSuccessful() {

        when(rejectionService.reject(any(), any(), any()))
                .thenReturn(Mono.empty());

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .put()
                .uri("/appointment-request/appointmentRequestID123/reject")
                .body(Mono.just(new AppointmentRequestRejectionRequest("reason")), AppointmentRequestRejectionRequest.class)
                .exchange()
                .expectStatus().isOk();
    }

    @Test
    void itShouldReturnUserFacingException() {
        when(rejectionService.reject(any(), any(), any()))
                .thenReturn(Mono.error(new UserFacingException(HttpStatus.GONE, "A Test Error!")));

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .put()
                .uri("/appointment-request/appointmentRequestID123/reject")
                .body(Mono.just(new AppointmentRequestRejectionRequest("reason")), AppointmentRequestRejectionRequest.class)
                .exchange()
                .expectStatus().isEqualTo(HttpStatus.GONE)
                .expectBody().jsonPath("$.message").isEqualTo("A Test Error!");
    }

    @Test
    void itShouldReturn500WhenBusinessCentralErrorResponseException() {
        when(clientResponse.statusCode()).thenReturn(HttpStatus.BAD_REQUEST);

        when(clientResponse.bodyToMono(String.class))
                .thenReturn(Mono.just("BadRequest_ResourceNotFound: some details"));
        var businessCentalClientResponse = mock(ClientResponse.class);
        when(businessCentalClientResponse.statusCode()).thenReturn(HttpStatus.BAD_REQUEST);
        var businessCentralErrorResponseException = new BusinessCentralErrorResponseException("", businessCentalClientResponse, "BadRequest_ResourceNotFound: some details");
        when(rejectionService.reject(any(), any(), any()))
                .thenReturn(Mono.error(businessCentralErrorResponseException));

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .put()
                .uri("/appointment-request/appointmentRequestID123/reject")
                .body(Mono.just(new AppointmentRequestRejectionRequest("reason")), AppointmentRequestRejectionRequest.class)
                .exchange()
                .expectStatus().is5xxServerError()
                .expectBody().jsonPath("$.message").isEqualTo("An error occurred while rejecting the appointment request.");
    }
}
