package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;

import com.alleskoenner.backend.TestSecurityConfig;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.OIDCUserMock;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

@WebFluxTest(PendingAppointmentRequestController.class)
@Import(TestSecurityConfig.class)
@ActiveProfiles("test")
class PendingAppointmentRequestControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @Autowired
    private OIDCUserMock oidcUserMock;

    @MockitoBean
    private PendingAppointmentRequestService appointmentRequestService;

    @Test
    void itShouldReturnEmptyListWhenNoPendingAppointmentRequests() {
        when(appointmentRequestService.getPendingAppointmentRequestsForHandyman(Mockito.any(LoginEmail.class))).thenReturn(Mono.just(Collections.emptyList()));

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .get()
                .uri("/appointment-request/pending")
                .exchange()
                .expectStatus().isOk()
                .expectBody(List.class).isEqualTo(Collections.emptyList());
    }

    @Test
    void itShouldReturnListOfPendingAppointmentRequests() {
        AppointmentRequest appointmentRequestBcDto = new ScheduledAppointmentRequest(
                "shipToAddressLine1",
                "shipToAddressLine2",
                "shipToPostcode",
                "shipToCity",
                "appointmentRequestId",
                "taskDescription",
                ZonedDateTime.now().plusMinutes(5),
                ZonedDateTime.now()
        );
        when(appointmentRequestService.getPendingAppointmentRequestsForHandyman(Mockito.any(LoginEmail.class)))
                .thenReturn(Mono.just(List.of(appointmentRequestBcDto)));

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .get()
                .uri("/appointment-request/pending")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$[0].appointmentRequestId").isEqualTo("appointmentRequestId")
                .jsonPath("$[0].isAsap").isEqualTo(false)
                .jsonPath("$[0].description").isEqualTo("taskDescription")
                .jsonPath("$[0].addressLine1").isEqualTo("shipToAddressLine1")
                .jsonPath("$[0].addressLine2").isEqualTo("shipToAddressLine2")
                .jsonPath("$[0].postcode").isEqualTo("shipToPostcode")
                .jsonPath("$[0].city").isEqualTo("shipToCity");
    }

    @Test
    void itShouldGetHandymanIdFromHeader() {
        when(appointmentRequestService.getPendingAppointmentRequestsForHandyman(Mockito.any(LoginEmail.class))).thenReturn(Mono.just(Collections.emptyList()));

        LoginEmail handyman1LoginEMail = new LoginEmail("<EMAIL>");
        webTestClient
                .mutateWith(oidcUserMock.handyman(handyman1LoginEMail.email()))
                .get()
                .uri("/appointment-request/pending")
                .exchange()
                .expectStatus().isOk();

        verify(appointmentRequestService).getPendingAppointmentRequestsForHandyman(handyman1LoginEMail);
    }

    @Test
    void itShouldFailWhenTokenIsNotSet() {
        when(appointmentRequestService.getPendingAppointmentRequestsForHandyman(Mockito.any(LoginEmail.class))).thenReturn(Mono.just(Collections.emptyList()));

        webTestClient
                .get()
                .uri("/appointment-request/pending")
                .exchange()
                .expectStatus().isUnauthorized();
    }
}
