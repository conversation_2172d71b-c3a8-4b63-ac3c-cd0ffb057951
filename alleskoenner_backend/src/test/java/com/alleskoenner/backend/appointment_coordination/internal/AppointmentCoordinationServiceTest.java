package com.alleskoenner.backend.appointment_coordination.internal;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import com.alleskoenner.backend.appointment_coordination.AppointmentCoordinationDetails;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_coordination.AppointmentCoordinationService;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_coordination.NoHandymanAvailableByCalendar;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_coordination.NoHandymanAvailableByPostalCode;
import com.alleskoenner.backend.appointment_coordination.internal.calendar.CalendarAvailabilityService;
import com.alleskoenner.backend.appointment_coordination.internal.handymen_by_postal_code.HandymanWithPostalCode;
import com.alleskoenner.backend.appointment_coordination.internal.handymen_by_postal_code.HandymenByPostalCodeBcClient;
import com.alleskoenner.backend.appointment_coordination.internal.requested_handymen.AssignRequestedHandymenBcClient;
import com.alleskoenner.backend.emails.BusinessNotificationEmailServiceInterface;
import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import com.alleskoenner.backend.push_notification.internal.AppointmentRequestNotificationSender;
import static org.assertj.core.api.Assertions.assertThat;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class AppointmentCoordinationServiceTest {

    @Mock HandymenByPostalCodeBcClient businessCentralClient;
    @Mock CalendarAvailabilityService calendarAvailabilityService;
    @Mock AppointmentRequestNotificationSender appointmentRequestNotificationSender;
    @Mock AssignRequestedHandymenBcClient assignRequestedHandymenBcClient;
    @Mock ErrorNotificationEmailServiceInterface errorNotificationEmailService;
    @Mock BusinessNotificationEmailServiceInterface businessNotificationEmailService;
    @InjectMocks AppointmentCoordinationService appointmentCoordinationService;

    @Test
    void verifyStepsOfAppointmentCoordinationProcess() {
        var zonedDateTime = ZonedDateTime.now();
        var appointmentRequestDetails = new AppointmentCoordinationDetails(
                "appointment-request-id",
                "Dies und Das Straße 1",
                "DiesundDasCity",
                "10589",
                "Dies und Das",
                Optional.of(zonedDateTime),
                false);
        when(businessCentralClient.fetchHandymenByPostalCode("10589"))
                .thenReturn(Mono.just(List.of(
                        getHandyman("<EMAIL>"),
                        getHandyman("<EMAIL>")
                )));
        final var deviceTokens = List.of("ADECGF");
        when(calendarAvailabilityService.getAvailableHandymenByCalendar(any(), any(), eq(appointmentRequestDetails.isAsSoonAsPossible())))
                .thenReturn(Mono.just(List.of(new HandymanWithPostalCode(null, null, null, null, deviceTokens, null, null, null, null, true, null))));
        when(assignRequestedHandymenBcClient.assignRequestedHandymenToAppointmentRequest(any(), any()))
                .thenReturn(Mono.just("Whatever"));
        when(appointmentRequestNotificationSender.sendAppointmentRequestNotification(
                any(),
                eq(appointmentRequestDetails.requestedDateTime().get()),
                eq(appointmentRequestDetails.isAsSoonAsPossible())
        )).thenReturn(Mono.empty());

        StepVerifier.create(appointmentCoordinationService.startAppointmentCoordination(appointmentRequestDetails))
                .expectComplete()
                .verify();


        verify(businessCentralClient).fetchHandymenByPostalCode("10589");

        ArgumentCaptor<List<HandymanWithPostalCode>> listArgumentCaptor = ArgumentCaptor.forClass(List.class);
        ArgumentCaptor<Optional<ZonedDateTime>> zonedDateTimeArgumentCaptor = ArgumentCaptor.forClass(Optional.class);
        verify(calendarAvailabilityService).getAvailableHandymenByCalendar(listArgumentCaptor.capture(), zonedDateTimeArgumentCaptor.capture(), eq(appointmentRequestDetails.isAsSoonAsPossible()));
        assertThat(listArgumentCaptor.getValue().stream().map(HandymanWithPostalCode::outlookEMailAddress)
                .toList()).containsExactly("<EMAIL>", "<EMAIL>");
        assertThat(zonedDateTimeArgumentCaptor.getValue().get()).isEqualTo(zonedDateTime);

        verify(appointmentRequestNotificationSender).sendAppointmentRequestNotification(deviceTokens, appointmentRequestDetails.requestedDateTime()
                .get(), appointmentRequestDetails.isAsSoonAsPossible());
    }

    @NotNull
    private static HandymanWithPostalCode getHandyman(String outlookEMailAddress) {
        return new HandymanWithPostalCode(
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                outlookEMailAddress,
                null,
                true,
                null

        );
    }

    @Test
    void shouldHandleErrorWhenNoHandymanIsAvailableByPostalCode() {
        var appointmentRequestDetails = new AppointmentCoordinationDetails(
                "appointment-request-id",
                "Dies und Das Straße 1",
                "DiesundDasCity",
                "10589",
                "Dies und Das",
                Optional.of(ZonedDateTime.parse("2025-02-11T14:30:00+01:00")),
                false);
        when(businessCentralClient.fetchHandymenByPostalCode("10589"))
                .thenReturn(Mono.error(new NoHandymanAvailableByPostalCode("No handyman found for postal code: 10589.")));
        when(businessNotificationEmailService.sendBusinessNotificationEmail(anyString()))
                .thenReturn(null);

        StepVerifier.create(appointmentCoordinationService.startAppointmentCoordination(appointmentRequestDetails))
                .expectError(NoHandymanAvailableByPostalCode.class)
                .verify();

        ArgumentCaptor<String> notificationMessageArgumentCaptor = ArgumentCaptor.forClass(String.class);
        verify(businessNotificationEmailService).sendBusinessNotificationEmail(notificationMessageArgumentCaptor.capture());
        assertThat(notificationMessageArgumentCaptor.getValue()).isEqualTo("No handyman found for postal code: 10589. Further details: AppointmentCoordinationDetails[appointmentRequestId=appointment-request-id, addressLine1=Dies und Das Straße 1, city=DiesundDasCity, postalCode=10589, taskDescription=Dies und Das, requestedDateTime=Optional[2025-02-11T14:30+01:00], isAsSoonAsPossible=false]");
        verify(errorNotificationEmailService, never()).sendErrorNotificationEmail(any(Throwable.class));
    }

    @Test
    void shouldHandleErrorWhenNoHandymanIsAvailableByCalendar() {
        var appointmentRequestDetails = new AppointmentCoordinationDetails(
                "appointment-request-id",
                "Dies und Das Straße 1",
                "DiesundDasCity",
                "10589",
                "Dies und Das",
                Optional.of(ZonedDateTime.parse("2025-02-11T14:30:00+01:00")),
                false);
        when(businessCentralClient.fetchHandymenByPostalCode("10589"))
                .thenReturn(Mono.just(List.of(
                        getHandyman("<EMAIL>"),
                        getHandyman("<EMAIL>")
                )));
        when(businessNotificationEmailService.sendBusinessNotificationEmail(anyString()))
                .thenReturn(null);
        var mockError = new NoHandymanAvailableByCalendar("No handyman is currently available according to their calendar. The requested time was: " + appointmentRequestDetails.requestedDateTime()
                .get() + ".");
        when(calendarAvailabilityService.getAvailableHandymenByCalendar(any(), any(), eq(appointmentRequestDetails.isAsSoonAsPossible())))
                .thenReturn(Mono.error(mockError));


        StepVerifier.create(appointmentCoordinationService.startAppointmentCoordination(appointmentRequestDetails))
                .expectError(NoHandymanAvailableByCalendar.class)
                .verify();

        ArgumentCaptor<String> notificationMessageArgumentCaptor = ArgumentCaptor.forClass(String.class);
        verify(businessNotificationEmailService).sendBusinessNotificationEmail(notificationMessageArgumentCaptor.capture());
        assertThat(notificationMessageArgumentCaptor.getValue()).isEqualTo("No handyman is currently available according to their calendar. The requested time was: 2025-02-11T14:30+01:00. Further details: AppointmentCoordinationDetails[appointmentRequestId=appointment-request-id, addressLine1=Dies und Das Straße 1, city=DiesundDasCity, postalCode=10589, taskDescription=Dies und Das, requestedDateTime=Optional[2025-02-11T14:30+01:00], isAsSoonAsPossible=false]");
        verify(errorNotificationEmailService, never()).sendErrorNotificationEmail(any(Throwable.class));
    }

    @Test
    void shouldHandleGenericException() {
        var appointmentRequestDetails = new AppointmentCoordinationDetails(
                "appointment-request-id",
                "Dies und Das Straße 1",
                "DiesundDasCity",
                "10589",
                "Dies und Das",
                Optional.of(ZonedDateTime.parse("2025-02-11T14:30:00+01:00")),
                false);
        RuntimeException exception = new RuntimeException("Test exception");
        when(businessCentralClient.fetchHandymenByPostalCode("10589"))
                .thenReturn(Mono.error(exception));
        when(errorNotificationEmailService.sendErrorNotificationEmail(any(Throwable.class)))
                .thenReturn(null);

        StepVerifier.create(appointmentCoordinationService.startAppointmentCoordination(appointmentRequestDetails))
                .expectError(RuntimeException.class)
                .verify();


        ArgumentCaptor<String> notificationMessagesArgumentCaptor = ArgumentCaptor.forClass(String.class);
        verify(errorNotificationEmailService).sendErrorNotificationEmail(eq(exception), notificationMessagesArgumentCaptor.capture());
        assertThat(notificationMessagesArgumentCaptor.getValue()).isEqualTo(" Further details: AppointmentCoordinationDetails[appointmentRequestId=appointment-request-id, addressLine1=Dies und Das Straße 1, city=DiesundDasCity, postalCode=10589, taskDescription=Dies und Das, requestedDateTime=Optional[2025-02-11T14:30+01:00], isAsSoonAsPossible=false]");
        verify(businessNotificationEmailService, never()).sendBusinessNotificationEmail(anyString());
    }
}
