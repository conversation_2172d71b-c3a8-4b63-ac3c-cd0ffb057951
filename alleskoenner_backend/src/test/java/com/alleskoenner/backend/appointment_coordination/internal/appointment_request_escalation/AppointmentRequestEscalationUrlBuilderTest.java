package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation;

import com.alleskoenner.backend.appointment_coordination.internal.business_central.AppointmentCoordinationBcConfig;
import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AppointmentRequestEscalationUrlBuilderTest {

    @Mock
    private AppointmentCoordinationBcConfig config;

    @InjectMocks
    private AppointmentRequestEscalationUrlBuilder urlBuilder;

    @Test
    void buildGettingAppointmentRequestsToEscalateUrl_shouldReturnCorrectUrl() {
        var environment = "environment1";
        var companyId = "company1";

        when(config.getBcEnvironment()).thenReturn(environment);
        when(config.getBcCompanyId()).thenReturn(companyId);

        var result = urlBuilder.buildGettingAppointmentRequestsToEscalateUrl();

        var expectedUrl = "v2.0/environment1/api/ITV/appointments/v2.0/companies(company1)/appointmentRequests?$filter=status eq 'Timed Out' Or status eq 'Rejected'";
        assertEquals(expectedUrl, result);
    }

    @Test
    void buildSettingAppointmentRequestToStatusEscalateUrl_shouldReturnCorrectUrl() {
        var environment = "environment1";
        var companyId = "company1";
        var appointmentRequestId = "request1";

        when(config.getBcEnvironment()).thenReturn(environment);
        when(config.getBcCompanyId()).thenReturn(companyId);

        var result = urlBuilder.buildSettingAppointmentRequestToStatusEscalateUrl(appointmentRequestId);

        var expectedUrl = "v2.0/environment1/api/ITV/appointments/v2.0/companies(company1)/appointmentRequests(request1)";
        assertEquals(expectedUrl, result);
    }
}