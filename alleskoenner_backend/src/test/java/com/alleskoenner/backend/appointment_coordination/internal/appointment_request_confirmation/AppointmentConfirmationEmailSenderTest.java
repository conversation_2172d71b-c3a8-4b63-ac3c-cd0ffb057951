package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import java.time.ZonedDateTime;
import com.alleskoenner.backend.emails.AppointmentConfirmationEmailServiceInterface;
import com.alleskoenner.backend.emails.AsapAppointmentConfirmationEmailValues;
import com.alleskoenner.backend.emails.ScheduledAppointmentConfirmationEmailValues;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class AppointmentConfirmationEmailSenderTest {

    @Mock
    private AppointmentConfirmationEmailServiceInterface appointmentConfirmationEmailServiceInterface;

    @InjectMocks
    private AppointmentConfirmationEmailSender appointmentConfirmationEmailSender;

    @Captor
    private ArgumentCaptor<AsapAppointmentConfirmationEmailValues> asapCaptor;
    @Captor
    private ArgumentCaptor<ScheduledAppointmentConfirmationEmailValues> scheduledCaptor;

    @Test
    void shouldSendAsapAppointmentConfirmationEmail() {
        var response = mock(SyncToHandymanCompanyBcResponseValue.class);
        when(response.asSoonAsPossible()).thenReturn(true);
        when(response.salesOrderNo()).thenReturn("SO123");
        when(response.shipToName()).thenReturn("Max Mustermann");
        when(response.shipToAddress()).thenReturn("Musterstraße 1");
        when(response.shipToCity()).thenReturn("Berlin");
        when(response.shipToPostCode()).thenReturn("10115");
        when(response.customerEMail()).thenReturn("<EMAIL>");
        when(response.workDescription()).thenReturn("Reparatur");
        when(appointmentConfirmationEmailServiceInterface.sendAsapAppointmentConfirmationEmail(any(AsapAppointmentConfirmationEmailValues.class)))
                .thenReturn(Mono.empty());

        appointmentConfirmationEmailSender.sendAppointmentConfirmationEmail(response);
        verify(appointmentConfirmationEmailServiceInterface).sendAsapAppointmentConfirmationEmail(asapCaptor.capture());
        var captured = asapCaptor.getValue();
        assertThat(captured.orderNumber()).isEqualTo("SO123");
        assertThat(captured.email()).isEqualTo("<EMAIL>");
        assertThat(captured.taskDescription()).isEqualTo("Reparatur");
    }

    @Test
    void shouldSendScheduledAppointmentConfirmationEmail() {
        var response = mock(SyncToHandymanCompanyBcResponseValue.class);
        when(response.asSoonAsPossible()).thenReturn(false);
        when(response.salesOrderNo()).thenReturn("SO124");
        when(response.shipToName()).thenReturn("Erika Musterfrau");
        when(response.shipToAddress()).thenReturn("Beispielweg 2");
        when(response.shipToCity()).thenReturn("Hamburg");
        when(response.shipToPostCode()).thenReturn("20095");
        when(response.customerEMail()).thenReturn("<EMAIL>");
        when(response.workDescription()).thenReturn("Installation");
        when(response.requestedDeliveryDatetime()).thenReturn(ZonedDateTime.parse("2024-06-01T10:30:00+02:00[Europe/Berlin]"));
        when(appointmentConfirmationEmailServiceInterface.sendScheduledAppointmentConfirmationEmail(any(ScheduledAppointmentConfirmationEmailValues.class)))
                .thenReturn(Mono.empty());

        appointmentConfirmationEmailSender.sendAppointmentConfirmationEmail(response);
        verify(appointmentConfirmationEmailServiceInterface).sendScheduledAppointmentConfirmationEmail(scheduledCaptor.capture());
        var captured = scheduledCaptor.getValue();
        assertThat(captured.orderNumber()).isEqualTo("SO124");
        assertThat(captured.email()).isEqualTo("<EMAIL>");
        assertThat(captured.taskDescription()).isEqualTo("Installation");
        assertThat(captured.weekday()).isEqualTo("Samstag");
        assertThat(captured.date()).isEqualTo("01.06.2024");
        assertThat(captured.time()).isEqualTo("10:30");
    }

    @Test
    void shouldNotThrowOnEmailSendError() {
        var response = mock(SyncToHandymanCompanyBcResponseValue.class);
        when(response.asSoonAsPossible()).thenReturn(true);
        when(appointmentConfirmationEmailServiceInterface.sendAsapAppointmentConfirmationEmail(any(AsapAppointmentConfirmationEmailValues.class)))
                .thenReturn(Mono.error(new RuntimeException("Email error")));
        assertDoesNotThrow(() -> appointmentConfirmationEmailSender.sendAppointmentConfirmationEmail(response));
    }
}
