package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import java.time.ZonedDateTime;
import java.util.stream.Stream;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Named;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class CalendarBookingServiceTest {
    private final CalendarBookingService service = new CalendarBookingService(null, null, null, null);

    @ParameterizedTest(name = "{index}: input={0}, interval={1}, expected={2}")
    @MethodSource("roundUpToNextIntervalCases")
    void testRoundUpToNextInterval(String input, int interval, String expected) {
        ZonedDateTime inputTime = ZonedDateTime.parse(input);
        ZonedDateTime expectedTime = ZonedDateTime.parse(expected);
        ZonedDateTime result = service.roundUpToNextInterval(inputTime, interval);
        assertThat(result).isEqualTo(expectedTime);
    }

    static Stream<Arguments> roundUpToNextIntervalCases() {
        return Stream.of(
                Arguments.of(Named.of("on interval 00", "2023-10-01T12:00:00+02:00"), 15, Named.of("same", "2023-10-01T12:00:00+02:00")),
                Arguments.of(Named.of("just after interval", "2023-10-01T12:00:01+02:00"), 15, Named.of("next interval", "2023-10-01T12:15:00+02:00")),
                Arguments.of(Named.of("before next interval", "2023-10-01T12:14:59+02:00"), 15, Named.of("next interval", "2023-10-01T12:15:00+02:00")),
                Arguments.of(Named.of("on interval 45", "2023-10-01T12:45:00+02:00"), 15, Named.of("same", "2023-10-01T12:45:00+02:00")),
                Arguments.of(Named.of("cross hour", "2023-10-01T12:50:00+02:00"), 15, Named.of("same", "2023-10-01T13:00:00+02:00")),
                Arguments.of(Named.of("30min interval", "2023-10-01T12:00:01+02:00"), 30, Named.of("next interval", "2023-10-01T12:30:00+02:00"))
        );
    }
}
