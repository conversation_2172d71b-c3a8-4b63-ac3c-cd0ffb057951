package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import java.time.ZonedDateTime;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.List;

import com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.dtos.HandymanAppointmentQueryResponse;
import com.alleskoenner.backend.shared.LoginEmail;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class PendingAppointmentRequestServiceTest {

    public static final int LESS_THAN_TIMEOUT_WINDOW = 1;
    private static final int TIMEOUT_WINDOW = 5;

    @NotNull
    private static ZonedDateTime getCreationTime() {
        return ZonedDateTime.now().minusMinutes(LESS_THAN_TIMEOUT_WINDOW);
    }

    @Mock
    PendingAppointmentRequestClient pendingAppointmentRequestClient;
    @Mock
    ExpirationDateTimeCalculator expirationDateTimeCalculator;

    @InjectMocks
    PendingAppointmentRequestService service;

    @Test
    void itShouldMapBusinessCentralResponseToScheduledAppointmentRequests() {
        when(expirationDateTimeCalculator.calculateExpiredDateTime(any()))
                .thenReturn(getCreationTime().plusMinutes(TIMEOUT_WINDOW));
        String requestedDeliveryDatetime = "1970-01-01T01:01:01+01:00[Europe/Paris]";
        var queryResponse = getHandymanAppointmentQueryResponse(requestedDeliveryDatetime,
                false
        );
        when(pendingAppointmentRequestClient.fetchPendingAppointmentRequests(any(LoginEmail.class)))
                .thenReturn(Mono.just(List.of(queryResponse)));


        var pendingAppointmentRequestsForHandyman = service.getPendingAppointmentRequestsForHandyman(new LoginEmail("<EMAIL>"));

        StepVerifier.create(pendingAppointmentRequestsForHandyman)
                .assertNext(appointmentRequests -> {

                    var appointmentRequest = appointmentRequests.getFirst();
                    assertThat(appointmentRequest).isInstanceOf(ScheduledAppointmentRequest.class);
                    assertThat(appointmentRequest.addressLine1).isEqualTo("shipToAddressLine1");
                    assertThat(appointmentRequest.addressLine2).isEqualTo("addressLine2");
                    assertThat(appointmentRequest.postcode).isEqualTo("postcode");
                    assertThat(appointmentRequest.city).isEqualTo("shipToCity");
                    assertThat(appointmentRequest.appointmentRequestId).isEqualTo("system-uuid");
                    assertThat(appointmentRequest.description).isEqualTo("taskDescription");
                    assertThat(appointmentRequest.expirationDateTime).isCloseTo(getCreationTime().plusMinutes(TIMEOUT_WINDOW), within(LESS_THAN_TIMEOUT_WINDOW, ChronoUnit.SECONDS));
                    assertThat(((ScheduledAppointmentRequest) appointmentRequest).scheduledDateTime)
                            .isEqualTo(ZonedDateTime.parse(requestedDeliveryDatetime));
                    assertThat(appointmentRequest.isAsap).isFalse();

                })
                .verifyComplete();

    }

    @Test
    void itShouldMapBusinessCentralResponseToAsapAppointmentRequests() {
        when(expirationDateTimeCalculator.calculateExpiredDateTime(any()))
                .thenReturn(getCreationTime().plusMinutes(TIMEOUT_WINDOW));
        String requestedDeliveryDatetime = "1970-01-01T01:01:01+01:00[Europe/Paris]";
        var appointmentRequestBcDto = getHandymanAppointmentQueryResponse(requestedDeliveryDatetime, true);
        when(pendingAppointmentRequestClient.fetchPendingAppointmentRequests(any(LoginEmail.class)))
                .thenReturn(Mono.just(List.of(appointmentRequestBcDto)));


        var pendingAppointmentRequestsForHandyman = service.getPendingAppointmentRequestsForHandyman(new LoginEmail("<EMAIL>"));

        StepVerifier.create(pendingAppointmentRequestsForHandyman)
                .assertNext(appointmentRequests -> {

                    var appointmentRequest = appointmentRequests.getFirst();
                    assertThat(appointmentRequest).isInstanceOf(AsapAppointmentRequest.class);
                    assertThat(appointmentRequest.addressLine1).isEqualTo("shipToAddressLine1");
                    assertThat(appointmentRequest.addressLine2).isEqualTo("addressLine2");
                    assertThat(appointmentRequest.postcode).isEqualTo("postcode");
                    assertThat(appointmentRequest.city).isEqualTo("shipToCity");
                    assertThat(appointmentRequest.appointmentRequestId).isEqualTo("system-uuid");
                    assertThat(appointmentRequest.description).isEqualTo("taskDescription");
                    assertThat(appointmentRequest.expirationDateTime).isCloseTo(getCreationTime().plusMinutes(TIMEOUT_WINDOW), within(LESS_THAN_TIMEOUT_WINDOW, ChronoUnit.SECONDS));
                    assertThat(appointmentRequest.isAsap).isTrue();

                })
                .verifyComplete();

    }

    @NotNull
    private static HandymanAppointmentQueryResponse getHandymanAppointmentQueryResponse(String requestedDeliveryDatetime, boolean asSoonAsPossible) {
        return new HandymanAppointmentQueryResponse(
                getCreationTime().toString(),
                "",
                "",
                "",
                asSoonAsPossible,
                "",
                "",
                "",
                requestedDeliveryDatetime,
                "",
                "",
                "system-uuid",
                "taskDescription",
                "",
                "",
                "",
                "shipToAddressLine1",
                "addressLine2",
                "shipToCity",
                "postcode"
        );
    }

    @Test
    void itShouldHandleParsingErrorOfScheduledDateTime() {
        when(expirationDateTimeCalculator.calculateExpiredDateTime(any()))
                .thenReturn(getCreationTime().plusMinutes(TIMEOUT_WINDOW));
        String requestedDeliveryDatetime = "not-parsable-date-time]";
        var appointmentRequestBcDto = getHandymanAppointmentQueryResponse(requestedDeliveryDatetime, false);
        when(pendingAppointmentRequestClient.fetchPendingAppointmentRequests(any(LoginEmail.class)))
                .thenReturn(Mono.just(List.of(appointmentRequestBcDto)));

        var pendingAppointmentRequestsForHandyman = service.getPendingAppointmentRequestsForHandyman(new LoginEmail("<EMAIL>"));

        StepVerifier.create(pendingAppointmentRequestsForHandyman)
                .expectError(DateTimeParseException.class)
                .verify();
    }

}
