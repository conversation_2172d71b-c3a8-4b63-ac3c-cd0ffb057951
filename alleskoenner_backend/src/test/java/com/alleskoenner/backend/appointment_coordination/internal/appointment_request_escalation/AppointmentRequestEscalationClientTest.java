package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation;

import java.io.IOException;

import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class AppointmentRequestEscalationClientTest {

    private static MockWebServer mockWebServer;

    private AppointmentRequestEscalationClient client;

    @Mock
    private AppointmentRequestEscalationUrlBuilder urlBuilder;

    @BeforeAll
    static void startServer() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
    }

    @AfterAll
    static void shutdownServer() throws IOException {
        mockWebServer.shutdown();
    }

    @BeforeEach
    void setUp() {
        var webClient = WebClient.builder()
                .baseUrl(mockWebServer.url("/").toString())
                .build();

        client = new AppointmentRequestEscalationClient(
                webClient,
                urlBuilder
        );
    }

    @Test
    void getAppointmentRequestsToEscalate_shouldReturnListOfAppointmentRequests() {
        when(urlBuilder.buildGettingAppointmentRequestsToEscalateUrl())
                .thenReturn("v2.0/env/api/ITV/handyman/v2.0/companies(45972317-3811-f011-9346-002248e4ed6a)/handymanAppointmentQuery");

        var successResponse = """
                {
                  "@odata.context": "https://api.businesscentral.dynamics.com/v2.0/env/$metadata#companies(45972317-3811-f011-9346-002248e4ed6a)/handymanAppointmentQuery",
                  "value": [
                    {
                      "no": "APP001",
                      "status": "Timed Out",
                      "systemId": "SYS001"
                    }
                  ]
                }
                """;

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(successResponse));

        var result = client.getAppointmentRequestsToEscalate();

        StepVerifier.create(result)
                .assertNext(items -> {
                    assertThat(items).hasSize(1);
                    var item = items.getFirst();
                    assertThat(item.no()).isEqualTo("APP001");
                    assertThat(item.status()).isEqualTo("Timed Out");
                    assertThat(item.systemId()).isEqualTo("SYS001");
                })
                .verifyComplete();
    }

    @Test
    void getAppointmentRequestsToEscalate_shouldReturnErrorWhenStatusIsNot2xx() {
        when(urlBuilder.buildGettingAppointmentRequestsToEscalateUrl())
                .thenReturn("v2.0/env/api/ITV/handyman/v2.0/companies(45972317-3811-f011-9346-002248e4ed6a)/handymanAppointmentQuery");

        var errorResponse = """
                {
                  "error": {
                    "code": "BadRequest",
                    "message": "Invalid request"
                  }
                }
                """;

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(400)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(errorResponse));

        var result = client.getAppointmentRequestsToEscalate();

        StepVerifier.create(result)
                .expectErrorMatches(error -> error instanceof BusinessCentralErrorResponseException &&
                        error.getMessage().contains("Some error occurred while fetching appointment requests") &&
                        error.getMessage().contains("Status: 400") &&
                        error.getMessage().contains("Body: " + errorResponse))
                .verify();
    }

    @Test
    void setAppointmentRequestStatusToEscalate_shouldCompleteSuccessfully() {
        var appointmentRequestId = "request1";

        when(urlBuilder.buildSettingAppointmentRequestToStatusEscalateUrl(appointmentRequestId))
                .thenReturn("v2.0/env/api/ITV/appointments/v2.0/companies(45972317-3811-f011-9346-002248e4ed6a)/appointmentRequests(request1)");

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody("{}"));

        var result = client.setAppointmentRequestStatusToEscalate(appointmentRequestId);

        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void setAppointmentRequestStatusToEscalate_shouldReturnErrorWhenStatusIsNot2xx() {
        var appointmentRequestId = "request1";

        when(urlBuilder.buildSettingAppointmentRequestToStatusEscalateUrl(appointmentRequestId))
                .thenReturn("v2.0/env/api/ITV/appointments/v2.0/companies(45972317-3811-f011-9346-002248e4ed6a)/appointmentRequests(request1)");
        var errorResponse = """
                {
                  "error": {
                    "code": "BadRequest",
                    "message": "Invalid request"
                  }
                }
                """;

        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(400)
                .setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .setBody(errorResponse));

        var result = client.setAppointmentRequestStatusToEscalate(appointmentRequestId);

        StepVerifier.create(result)
                .expectErrorMatches(error -> error instanceof BusinessCentralErrorResponseException &&
                        error.getMessage().contains("Some error occurred while setting appointment request with id") &&
                        error.getMessage().contains("Status: 400") &&
                        error.getMessage().contains("Body: " + errorResponse))
                .verify();
    }
}
