package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.alleskoenner.backend.TestSecurityConfig;
import com.alleskoenner.backend.shared.OIDCUserMock;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

@WebFluxTest(AppointmentRequestConfirmationController.class)
@Import(TestSecurityConfig.class)
@ActiveProfiles("test")
class AppointmentRequestConfirmationControllerTest {

    @Autowired
    private WebTestClient webTestClient;
    @MockitoBean
    private AppointmentRequestConfirmationService confirmationService;
    @Autowired
    private OIDCUserMock oidcUserMock;

    @Test
    void itShouldReturn200WhenSuccessful() {

        when(confirmationService.confirmAndArchive(any(), any()))
                .thenReturn(Mono.empty());

        webTestClient
                .mutateWith(oidcUserMock.handyman("<EMAIL>"))
                .put()
                .uri("/appointment-request/2389-4232-342/confirm")
                .exchange()
                .expectStatus().isOk();
    }

}
