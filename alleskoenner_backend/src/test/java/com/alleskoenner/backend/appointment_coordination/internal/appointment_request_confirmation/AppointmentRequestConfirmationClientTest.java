package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.alleskoenner.backend.shared.LoginEmail;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class AppointmentRequestConfirmationClientTest {

    @Mock
    ClientResponse clientResponse;
    @InjectMocks
    private AppointmentRequestConfirmationClient client;

    @Test
    void testMapResponseBodyToError_ResourceNotFound() {
        when(clientResponse.statusCode()).thenReturn(HttpStatus.BAD_REQUEST);
        when(clientResponse.bodyToMono(String.class))
                .thenReturn(Mono.just("BadRequest_ResourceNotFound: some details"));

        Mono<Void> result = client.mapResponseBodyToError(clientResponse, new LoginEmail("<EMAIL>"), "appointmentRequestId");

        StepVerifier.create(result)
                .expectErrorMatches(ex ->
                        ex instanceof ResponseStatusException
                                && ((ResponseStatusException) ex).getStatusCode() == HttpStatus.GONE
                                && ex.getMessage().contains("Appointment request does not exist anymore.")
                )
                .verify();
    }

}
