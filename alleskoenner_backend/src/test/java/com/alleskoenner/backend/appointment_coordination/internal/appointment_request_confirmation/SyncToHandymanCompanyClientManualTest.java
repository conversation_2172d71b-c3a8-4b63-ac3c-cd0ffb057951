package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;

@SpringBootTest
@Profile({"manual-testing", "test"})
@Disabled("This test is only for manually triggering. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class SyncToHandymanCompanyClientManualTest {

    @Autowired
    SyncToHandymanCompanyClient syncToHandymanCompanyClient;

    @Test
    @DisplayName("Start appointment coordination process for manual testing")
    void startAppointmentCoordination() {
        syncToHandymanCompanyClient.syncSalesOrderAndCustomerToHandymanCompany("aefeaa34-96ef-ef11-9345-0022480c7e82")
                .block();
    }
}
