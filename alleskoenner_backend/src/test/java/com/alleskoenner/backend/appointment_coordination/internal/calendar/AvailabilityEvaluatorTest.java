package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.List;
import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import com.microsoft.graph.models.ScheduleInformation;
import static org.assertj.core.api.Assertions.assertThat;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class AvailabilityEvaluatorTest {
    private AvailabilityEvaluator availabilityEvaluator;

    @BeforeEach
    void setUp() {
        var config = mock(AppointmentCoordinationConfig.class);
        var calendarConfig = mock(AppointmentCoordinationConfig.Calendar.class);
        when(config.getCalendar()).thenReturn(calendarConfig);
        when(calendarConfig.getTravelTimeToCustomerInMinutes()).thenReturn(30);
        when(calendarConfig.getFixedDurationOfAppointmentInMinutes()).thenReturn(60);
        when(calendarConfig.getAvailabilityViewIntervalInMinutes()).thenReturn(30);
        availabilityEvaluator = new AvailabilityEvaluator(config);
    }

    @NotNull
    private static ScheduleInformation createScheduleInformation(String mail, String number) {
        var scheduleInformation = new ScheduleInformation();
        scheduleInformation.setScheduleId(mail);
        scheduleInformation.setAvailabilityView(number);
        return scheduleInformation;
    }

    @Test
    void shouldReturnEmailsOfAvailableHandymen() {
        final var availableHandyman = createScheduleInformation("<EMAIL>", "0000");
        final var busyHandyman = createScheduleInformation("<EMAIL>", "1122");
        final var unknownHandyman = createScheduleInformation("<EMAIL>", null);

        var schedules = List.of(availableHandyman, busyHandyman, unknownHandyman);

        var availableHandymen = availabilityEvaluator.getAvailableHandymenEmailsFromResponse(schedules);

        assertThat(availableHandymen).containsExactly("<EMAIL>");
    }

    @Test
    void shouldReturnEmailsOfHandymenWithAtLeast90ConsecutiveMinutesOfFreeTime() {
        final var availableHandyman1 = createScheduleInformation("<EMAIL>", "1000");
        final var availableHandyman2 = createScheduleInformation("<EMAIL>", "120001");
        final var availableHandyman3 = createScheduleInformation("<EMAIL>", "0001");
        final var busyHandyman1 = createScheduleInformation("<EMAIL>", "00100200");
        final var busyHandyman2 = createScheduleInformation("<EMAIL>", "0020");

        var schedules = List.of(availableHandyman1, availableHandyman2, availableHandyman3, busyHandyman1, busyHandyman2);

        var availableHandymen = availabilityEvaluator.getAvailableHandymenEmailsFromResponse(schedules);

        assertThat(availableHandymen).containsExactly("<EMAIL>", "<EMAIL>", "<EMAIL>");
    }

    @Test
    void shouldReturnEmptyListWhenNoHandymenAreAvailable() {
        final var busyHandyman = createScheduleInformation("<EMAIL>", "1111");
        final var anotherBusyHandyman = createScheduleInformation("<EMAIL>", "2222");

        var schedules = List.of(busyHandyman, anotherBusyHandyman);

        var availableHandymen = availabilityEvaluator.getAvailableHandymenEmailsFromResponse(schedules);

        assertThat(availableHandymen).isEmpty();
    }

    @Test
    void shouldReturnEmptyListWhenSchedulesListIsEmpty() {
        List<ScheduleInformation> schedules = List.of();

        var availableHandymen = availabilityEvaluator.getAvailableHandymenEmailsFromResponse(schedules);

        assertThat(availableHandymen).isEmpty();
    }

    // Test handymen as ScheduleInformation constants
    private static class TestHandymen {
        static final ScheduleInformation BUSY_ALL_DAY;
        static final ScheduleInformation BUSY_WITH_SHORT_GAPS;
        static final ScheduleInformation FREE_ALL_DAY;
        static final ScheduleInformation FREE_AT_START;
        static final ScheduleInformation FREE_IN_MIDDLE;
        static final ScheduleInformation FREE_AT_END;

        static {
            BUSY_ALL_DAY = new ScheduleInformation();
            BUSY_ALL_DAY.setScheduleId("<EMAIL>");
            BUSY_ALL_DAY.setAvailabilityView("111111111");

            BUSY_WITH_SHORT_GAPS = new ScheduleInformation();
            BUSY_WITH_SHORT_GAPS.setScheduleId("<EMAIL>");
            BUSY_WITH_SHORT_GAPS.setAvailabilityView("110111011");

            FREE_ALL_DAY = new ScheduleInformation();
            FREE_ALL_DAY.setScheduleId("<EMAIL>");
            FREE_ALL_DAY.setAvailabilityView("000000000");

            FREE_AT_START = new ScheduleInformation();
            FREE_AT_START.setScheduleId("<EMAIL>");
            FREE_AT_START.setAvailabilityView("000111111");

            FREE_IN_MIDDLE = new ScheduleInformation();
            FREE_IN_MIDDLE.setScheduleId("<EMAIL>");
            FREE_IN_MIDDLE.setAvailabilityView("111000111");

            FREE_AT_END = new ScheduleInformation();
            FREE_AT_END.setScheduleId("<EMAIL>");
            FREE_AT_END.setAvailabilityView("111111000");
        }
    }

    @Test
    void getAvailableHandymenEmailsFromResponse_returnsEmptyListForEmptyInput() {
        List<ScheduleInformation> input = List.of();
        var result = availabilityEvaluator.getAvailableHandymenEmailsFromResponse(input);
        assertThat(result).isEmpty();
    }

    @Test
    void getAvailableHandymenEmailsFromResponse_returnsOnlyHandymenWithSufficientFreeSlot() {
        var input = List.of(
                TestHandymen.FREE_AT_START,
                TestHandymen.BUSY_ALL_DAY,
                TestHandymen.BUSY_WITH_SHORT_GAPS
        );
        var result = availabilityEvaluator.getAvailableHandymenEmailsFromResponse(input);
        assertThat(result).containsExactly(TestHandymen.FREE_AT_START.getScheduleId());
    }

    @Test
    void getAvailableHandymenEmailsFromResponse_returnsAllHandymenIfAllHaveFreeSlot() {
        var input = List.of(
                TestHandymen.FREE_AT_START,
                TestHandymen.FREE_AT_END,
                TestHandymen.FREE_ALL_DAY
        );
        var result = availabilityEvaluator.getAvailableHandymenEmailsFromResponse(input);
        assertThat(result).containsExactlyInAnyOrder(
                TestHandymen.FREE_AT_START.getScheduleId(),
                TestHandymen.FREE_AT_END.getScheduleId(),
                TestHandymen.FREE_ALL_DAY.getScheduleId()
        );
    }

    @Test
    void getAvailableHandymenEmailsFromResponse_returnsEmptyListIfNoHandymanHasFreeSlot() {
        var input = List.of(
                TestHandymen.BUSY_ALL_DAY,
                TestHandymen.BUSY_WITH_SHORT_GAPS
        );
        var result = availabilityEvaluator.getAvailableHandymenEmailsFromResponse(input);
        assertThat(result).isEmpty();
    }

    @Test
    void getFirstAvailableTimeSlot_findsSlotAtBeginning() {
        var windowStart = ZonedDateTime.parse("2025-02-11T08:00:00+01:00");
        var duration = Duration.ofMinutes(90); // needs 3 slots
        var result = availabilityEvaluator.getFirstAvailableTimeSlot(TestHandymen.FREE_AT_START, windowStart, duration);
        assertThat(result).isEqualTo(windowStart); // 08:00
    }

    @Test
    void getFirstAvailableTimeSlot_findsSlotInMiddle() {
        var windowStart = ZonedDateTime.parse("2025-02-11T08:00:00+01:00");
        var duration = Duration.ofMinutes(90); // needs 3 slots
        var result = availabilityEvaluator.getFirstAvailableTimeSlot(TestHandymen.FREE_IN_MIDDLE, windowStart, duration);
        assertThat(result).isEqualTo(windowStart.plusMinutes(3 * 30)); // 09:30
    }

    @Test
    void getFirstAvailableTimeSlot_findsSlotAtEnd() {
        var windowStart = ZonedDateTime.parse("2025-02-11T08:00:00+01:00");
        var duration = Duration.ofMinutes(90); // needs 3 slots
        var result = availabilityEvaluator.getFirstAvailableTimeSlot(TestHandymen.FREE_AT_END, windowStart, duration);
        assertThat(result).isEqualTo(windowStart.plusMinutes(6 * 30)); // 11:00
    }

    @Test
    void getFirstAvailableTimeSlot_returnsNullIfNotEnoughConsecutiveFreeSlots() {
        var windowStart = ZonedDateTime.parse("2025-02-11T08:00:00+01:00");
        var duration = Duration.ofMinutes(90); // needs 3 slots
        var result = availabilityEvaluator.getFirstAvailableTimeSlot(TestHandymen.BUSY_WITH_SHORT_GAPS, windowStart, duration);
        assertThat(result).isNull();
    }

    @Test
    void getFirstAvailableTimeSlot_returnsNullIfNoZerosAtAll() {
        var windowStart = ZonedDateTime.parse("2025-02-11T08:00:00+01:00");
        var duration = Duration.ofMinutes(90); // needs 3 slots
        var result = availabilityEvaluator.getFirstAvailableTimeSlot(TestHandymen.BUSY_ALL_DAY, windowStart, duration);
        assertThat(result).isNull();
    }

    @Test
    void getFirstAvailableTimeSlot_returnsNullIfAvailabilityViewIsNull() {
        var info = new ScheduleInformation();
        info.setAvailabilityView(null);
        var windowStart = ZonedDateTime.parse("2025-02-11T08:00:00+01:00");
        var duration = Duration.ofMinutes(90);
        var result = availabilityEvaluator.getFirstAvailableTimeSlot(info, windowStart, duration);
        assertThat(result).isNull();
    }
}
