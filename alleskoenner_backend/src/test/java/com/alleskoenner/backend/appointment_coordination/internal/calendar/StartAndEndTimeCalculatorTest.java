package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import java.time.Duration;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Optional;
import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.within;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class StartAndEndTimeCalculatorTest {

    @Mock private AppointmentCoordinationConfig config;
    @Mock private AppointmentCoordinationConfig.Calendar calendar;
    @InjectMocks
    private StartAndEndTimeCalculator calculator;

    @BeforeEach
    void setUp() {
        when(config.getCalendar()).thenReturn(calendar);
    }

    @Test
    void shouldCalculateStartAndEndTimeForScheduledAppointment() {
        var requestedDateTime = ZonedDateTime.parse("2025-02-18T10:00:00Z");
        when(calendar.getFixedDurationOfAppointmentInMinutes()).thenReturn(60);
        when(calendar.getTravelTimeToCustomerInMinutes()).thenReturn(30);

        var result = calculator.getStartAndEndDateTime(Optional.of(requestedDateTime), false);

        assertThat(result.startDateTimeIncludingTravelTime()).isEqualTo(ZonedDateTime.parse("2025-02-18T09:30:00Z"));
        assertThat(result.endDateTimeWithFixedAppointmentDuration1()).isEqualTo(ZonedDateTime.parse("2025-02-18T11:00:00Z"));
    }

    @Test
    void shouldCalculateStartAndEndTimeForAsapAppointment() {
        var ignoredTime = ZonedDateTime.of(1970, 1, 1, 1, 1, 1, 1, ZoneId.of("Europe/Berlin"));
        when(calendar.getFixedDurationOfAppointmentInMinutes()).thenReturn(60);
        when(calendar.getDefaultAsapWindow()).thenReturn(Duration.ofHours(3));

        var result = calculator.getStartAndEndDateTime(Optional.of(ignoredTime), true);

        assertThat(Duration.between(result.startDateTimeIncludingTravelTime(), result.endDateTimeWithFixedAppointmentDuration1())
                .toMinutes()).isCloseTo(240, within(1L));
    }
}
