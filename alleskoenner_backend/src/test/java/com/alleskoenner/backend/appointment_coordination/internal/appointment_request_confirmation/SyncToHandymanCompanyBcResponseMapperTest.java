package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import org.assertj.core.api.SoftAssertions;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import org.junit.jupiter.api.Test;

class SyncToHandymanCompanyBcResponseMapperTest {

    // language=JSON
    private static final String JSON = """
            {
              "@odata.context": "https://api.businesscentral.dynamics.com/v2.0/HandymanSandbox/api/ITV/handymanApp/v2.0/$metadata#companies(45972317-3811-f011-9346-002248e4ed6a)/customerOrderCreation/$entity",
              "@odata.etag": "W/\\"JzIwOzE1NTEzNjI4NDYzNzUxMjU3NTI3MTswMDsn\\"",
              "id": 0,
              "customerID": "ce44addc-ea0e-f011-9347-6045bd179ab7",
              "customerName": "Einar Testar",
              "customerNo": "C00020",
              "salesOrderID": "ab376eaa-d421-f011-9af8-6045bd179dd3",
              "salesOrderNo": "101030",
              "customerAddress": "Herschelstraße, 10589 Berlin, Deutschland",
              "customerCity": "Berlin",
              "customerAddress2": "",
              "customerEMail": "<EMAIL>",
              "customerPhoneNo": "",
              "customerPostCode": "10589",
              "customerPostingGroup": "EU",
              "genBusPostingGroup": "EU",
              "workDescription": "Ich bin Einar, der gern alles testet. Jetzt brauch ich einen, der alles repariert",
              "postingDate": "2025-04-25",
              "documentDate": "2025-04-25",
              "type": "Person",
              "isConditionsChecked": false,
              "asSoonAsPossible": false,
              "requestedDeliveryDatetime": "2035-04-05T07:00:00Z"
            }
            """;

    @Test
    void testMapFunctionality() {
        SyncToHandymanCompanyBcResponseValue responseValue = SyncToHandymanCompanyBcResponseMapper.map(JSON);
        assertNotNull(responseValue);

        SoftAssertions softly = new SoftAssertions();

        softly.assertThat(responseValue.odataContext())
                .isEqualTo("https://api.businesscentral.dynamics.com/v2.0/HandymanSandbox/api/ITV/handymanApp/v2.0/$metadata#companies(45972317-3811-f011-9346-002248e4ed6a)/customerOrderCreation/$entity");
        softly.assertThat(responseValue.odataEtag()).isEqualTo("W/\"JzIwOzE1NTEzNjI4NDYzNzUxMjU3NTI3MTswMDsn\"");
        softly.assertThat(responseValue.customerID()).isEqualTo("ce44addc-ea0e-f011-9347-6045bd179ab7");
        softly.assertThat(responseValue.customerName()).isEqualTo("Einar Testar");
        softly.assertThat(responseValue.customerNo()).isEqualTo("C00020");
        softly.assertThat(responseValue.salesOrderID()).isEqualTo("ab376eaa-d421-f011-9af8-6045bd179dd3");
        softly.assertThat(responseValue.salesOrderNo()).isEqualTo("101030");
        softly.assertThat(responseValue.customerAddress()).isEqualTo("Herschelstraße, 10589 Berlin, Deutschland");
        softly.assertThat(responseValue.customerCity()).isEqualTo("Berlin");
        softly.assertThat(responseValue.customerAddress2()).isEqualTo("");
        softly.assertThat(responseValue.customerEMail()).isEqualTo("<EMAIL>");
        softly.assertThat(responseValue.customerPhoneNo()).isEqualTo("");
        softly.assertThat(responseValue.customerPostCode()).isEqualTo("10589");
        softly.assertThat(responseValue.customerPostingGroup()).isEqualTo("EU");
        softly.assertThat(responseValue.genBusPostingGroup()).isEqualTo("EU");
        softly.assertThat(responseValue.workDescription()).isEqualTo("Ich bin Einar, der gern alles testet. Jetzt brauch ich einen, der alles repariert");
        softly.assertThat(responseValue.postingDate()).hasToString("2025-04-25");
        softly.assertThat(responseValue.documentDate()).hasToString("2025-04-25");
        softly.assertThat(responseValue.type()).isEqualTo("Person");
        softly.assertThat(responseValue.isConditionsChecked()).isFalse();
        softly.assertThat(responseValue.asSoonAsPossible()).isFalse();
        softly.assertThat(responseValue.requestedDeliveryDatetime()).hasToString("2035-04-05T07:00Z");

        softly.assertAll();
    }

}
