package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AvailabilityRequesterTest {

    private static final ZonedDateTime ANY_ZONED_DATE_TIME = ZonedDateTime.parse("2025-02-11T14:30:00+01:00");
    private static final int AVAILABILITY_VIEW_INTERVAL_IN_MINUTES = 30;
    private static final String TIME_ZONE = "+01:00";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    @Mock AppointmentCoordinationConfig config;
    @Mock AppointmentCoordinationConfig.Calendar calendar;

    @InjectMocks
    private AvailabilityRequester availabilityRequester;

    @Test
    void shouldCreateValidSchedulePostRequestBody() {
        when(config.getCalendar()).thenReturn(calendar);
        when(calendar.getAvailabilityViewIntervalInMinutes()).thenReturn(AVAILABILITY_VIEW_INTERVAL_IN_MINUTES);

        var handymenEmailAddresses = List.of("<EMAIL>", "<EMAIL>");

        var requestBody = availabilityRequester.getGetSchedulePostRequestBody(handymenEmailAddresses,
                ANY_ZONED_DATE_TIME.minusMinutes(30),
                ANY_ZONED_DATE_TIME.plusMinutes(60));

        assertThat(requestBody).isNotNull();
        assertThat(requestBody.getSchedules()).containsExactlyElementsOf(handymenEmailAddresses);

        var expectedStartTime = ANY_ZONED_DATE_TIME.minusMinutes(30);
        var expectedEndTime = ANY_ZONED_DATE_TIME.plusMinutes(60);

        assertThat(requestBody.getStartTime()).isNotNull();
        assertThat(requestBody.getStartTime().getDateTime()).isEqualTo(DATE_TIME_FORMATTER.format(expectedStartTime));
        assertThat(requestBody.getStartTime().getTimeZone()).isEqualTo(TIME_ZONE);

        assertThat(requestBody.getEndTime()).isNotNull();
        assertThat(requestBody.getEndTime().getDateTime()).isEqualTo(DATE_TIME_FORMATTER.format(expectedEndTime));
        assertThat(requestBody.getEndTime().getTimeZone()).isEqualTo(TIME_ZONE);

        assertThat(requestBody.getAvailabilityViewInterval()).isEqualTo(AVAILABILITY_VIEW_INTERVAL_IN_MINUTES);
    }

}
