package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;

@SpringBootTest
@Profile( {"manual-testing", "test"})
@Disabled("This test is only for manually testing push notifications. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class AppointmentConfirmationEmailSenderManualTest {

    // If you find this not working, please check the application.yml in the test resources for the correct email configuration

    @Autowired
    AppointmentConfirmationEmailSender appointmentConfirmationEmailSender;

    @Test
    void shouldSendAScheduledAppointmentConfirmationEmail() throws InterruptedException {
        appointmentConfirmationEmailSender.sendAppointmentConfirmationEmail(
                new SyncToHandymanCompanyBcResponseValue(
                        "https://example.com/odata/context",
                        "etag12345",
                        "CUST123",
                        "<PERSON> Mustermann",
                        "CUSTNO456",
                        "SO12345",
                        "SO67890",
                        "Hamster Str. 12",
                        "Berlin",
                        "Apartment 3B",
                        "<EMAIL>",
                        "+4915123456789",
                        "10115",
                        "POSTGRP1",
                        "Musterfirma GmbH",
                        "Hamster Str. 15",
                        "Suite 5",
                        "Berlin",
                        "10117",
                        "+4915123456790",
                        "Musterfirma GmbH",
                        "Rechnungsstr. 20",
                        "Etage 2",
                        "Berlin",
                        "10119",
                        "COMP123",
                        "HANDYMAN123",
                        "GENPOSTGRP2",
                        "Reparatur der Heizung",
                        LocalDate.now(),
                        LocalDate.now(),
                        "Standard",
                        true,
                        false,
                        ZonedDateTime.now()
                )
        );

        Thread.sleep(5000); // Wait for the email to be sent
    }

    @Test
    void shouldSendAnAsapAppointmentConfirmationEmail() throws InterruptedException {
        var asSoonAsPossible = true;
        appointmentConfirmationEmailSender.sendAppointmentConfirmationEmail(
                new SyncToHandymanCompanyBcResponseValue(
                        "https://example.com/odata/context",
                        "etag12345",
                        "CUST123",
                        "Max Mustermann",
                        "CUSTNO456",
                        "SO12345",
                        "SO67890",
                        "Hamster Str. 12",
                        "Berlin",
                        "Apartment 3B",
                        "<EMAIL>",
                        "+4915123456789",
                        "10115",
                        "POSTGRP1",
                        "Musterfirma GmbH",
                        "Hamster Str. 15",
                        "Suite 5",
                        "Berlin",
                        "10117",
                        "+4915123456790",
                        "Musterfirma GmbH",
                        "Rechnungsstr. 20",
                        "Etage 2",
                        "Berlin",
                        "10119",
                        "COMP123",
                        "HANDYMAN123",
                        "GENPOSTGRP2",
                        "Reparatur der Heizung",
                        LocalDate.now(),
                        LocalDate.now(),
                        "Standard",
                        true,
                        asSoonAsPossible,
                        ZonedDateTime.now()
                )
        );

        Thread.sleep(5000); // Wait for the email to be sent
    }
}