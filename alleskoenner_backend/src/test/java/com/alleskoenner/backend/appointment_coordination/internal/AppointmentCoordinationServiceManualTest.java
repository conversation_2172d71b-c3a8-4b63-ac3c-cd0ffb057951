package com.alleskoenner.backend.appointment_coordination.internal;

import java.time.ZonedDateTime;
import java.util.Optional;

import com.alleskoenner.backend.appointment_coordination.AppointmentCoordinationDetails;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_coordination.AppointmentCoordinationService;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;

@SpringBootTest
@Profile( {"manual-testing", "test"})
@Disabled("This test is only for manually testing the appointment coordination process. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class AppointmentCoordinationServiceManualTest {

    @Autowired
    AppointmentCoordinationService appointmentCoordinationService;

    @Test
    @DisplayName("Start appointment coordination process for manual testing")
    void startAppointmentCoordination() {
        var appointmentRequestDetails = new AppointmentCoordinationDetails(
                "replace-with-real-appointment-request-id",
                "Dies und Das Straße 1",
                "DiesundDasCity",
                "10243",
                "Manueller Test über Spring Backend",
                Optional.of(ZonedDateTime.now()),
                false);
        var list = appointmentCoordinationService.startAppointmentCoordination(appointmentRequestDetails).block();
        System.out.println(list);
    }
}
