package com.alleskoenner.backend.appointment_coordination.internal;

import java.util.List;
import com.alleskoenner.backend.appointment_coordination.internal.handymen_by_postal_code.HandymanWithPostalCode;
import org.jetbrains.annotations.NotNull;

public class TestDataUtils {
    @NotNull
    private static HandymanWithPostalCode getNewEmptyHandyman() {
        return new HandymanWithPostalCode(null, null, null, null, null, null, null, null, null, true, null);
    }

    @NotNull
    public static List<HandymanWithPostalCode> getDefaultHandymenWithPostalCodes(String email1, String email2) {
        final var handyman1 = getNewEmptyHandyman().withOutlookEMailAddress(email1);
        final var handyman2 = getNewEmptyHandyman().withOutlookEMailAddress(email2);
        return List.of(handyman1, handyman2);
    }
}
