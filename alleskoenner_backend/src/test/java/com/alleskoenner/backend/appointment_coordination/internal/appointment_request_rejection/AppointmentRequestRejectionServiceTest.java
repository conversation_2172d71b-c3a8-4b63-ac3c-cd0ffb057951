package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_rejection;

import com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation.AppointmentRequestStatus;
import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation.AppointmentRequestStatus.PENDING;
import com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.HandymanAppointmentQueryGettingClient;
import com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.dtos.HandymanAppointmentQueryResponse;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.ClientResponse;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;


@ExtendWith(MockitoExtension.class)
class AppointmentRequestRejectionServiceTest {
    @Mock
    AppointmentRequestRejectionClient rejectionClient;
    @Mock
    HandymanAppointmentQueryGettingClient gettingClient;
    @InjectMocks
    AppointmentRequestRejectionService rejectionService;

    @Test
    void successfulReject() {
        var email = "<EMAIL>";
        var appointmentRequestId = "appointmentRequestId123";
        var reason = "because";
        when(rejectionClient.rejectAppointmentRequest("handymanSystemId123", reason))
                .thenReturn(Mono.empty());
        when(gettingClient.getRequestedHandyman(new LoginEmail(email), appointmentRequestId))
                .thenReturn(Mono.just(new HandymanAppointmentQueryResponse(
                        appointmentRequestId,
                        email,
                        PENDING.getValue(),
                        "handymanSystemId123",
                        false,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null)));
        var reject = rejectionService.reject(new LoginEmail(email), appointmentRequestId, reason);
        StepVerifier.create(reject)
                .verifyComplete();
    }

    @Test
    void rejectErrorOnBcReject() {
        var clientResponse = mock(ClientResponse.class);
        when(clientResponse.statusCode()).thenReturn(HttpStatus.BAD_REQUEST);

        var email = "<EMAIL>";
        var appointmentRequestId = "appointmentRequestId123";
        var reason = "because";
        when(gettingClient.getRequestedHandyman(new LoginEmail(email), appointmentRequestId))
                .thenReturn(Mono.just(new HandymanAppointmentQueryResponse(
                        appointmentRequestId,
                        email,
                        PENDING.getValue(),
                        "handymanSystemId123",
                        false,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null)));
        var bcError = new BusinessCentralErrorResponseException("Some BC Error", clientResponse, "{\"error\": \"Error Body\"}");
        when(rejectionClient.rejectAppointmentRequest("handymanSystemId123", reason))
                .thenReturn(Mono.error(bcError));
        var reject = rejectionService.reject(new LoginEmail(email), appointmentRequestId, reason);
        StepVerifier.create(reject)
                .verifyError(BusinessCentralErrorResponseException.class);
    }

    @Test
    void rejectNotPendingRequestedHandyman() {
        var email = "<EMAIL>";
        var appointmentRequestId = "appointmentRequestId123";
        var reason = "because";
        when(gettingClient.getRequestedHandyman(new LoginEmail(email), appointmentRequestId))
                .thenReturn(Mono.just(new HandymanAppointmentQueryResponse(
                        appointmentRequestId,
                        email,
                        AppointmentRequestStatus.REJECTED.getValue(),
                        "handymanSystemId123",
                        false,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null)));
        var reject = rejectionService.reject(new LoginEmail(email), appointmentRequestId, reason);
        StepVerifier.create(reject)
                .verifyErrorMessage("Handyman request can't be rejected. Is in state: Rejected");
    }
}
