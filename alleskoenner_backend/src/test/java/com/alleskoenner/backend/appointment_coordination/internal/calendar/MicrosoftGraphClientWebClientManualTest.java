package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import com.alleskoenner.backend.appointment_coordination.internal.handymen_by_postal_code.HandymanWithPostalCode;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Optional;

import static com.alleskoenner.backend.appointment_coordination.internal.TestDataUtils.getDefaultHandymenWithPostalCodes;
import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@Profile({"manual-testing", "test"})
@Disabled("This test is only for manually testing getting available handymen via their outlook calendar. To run this test, set your env variable to spring_profiles_active=manual-testing,test in your Editor Configuration")
class MicrosoftGraphClientWebClientManualTest {

    @Autowired
    private CalendarAvailabilityService calendarAvailabilityService;

    @Test
    @DisplayName("Call Calendar view from outlook for manual testing")
    void testCalendarClient() {

        var handymanEmail1 = "<EMAIL>";
        var handymanEmail2 = "<EMAIL>";
        var handymenWithPostalCodes = getDefaultHandymenWithPostalCodes(handymanEmail1, handymanEmail2);
        boolean isAsSoonAsPossible = false;

        var requestedDateTime = ZonedDateTime.of(2025, 2, 15, 10, 0, 0, 0, ZoneId.of("Europe/Berlin"));

        var availableHandymen = calendarAvailabilityService.getAvailableHandymenByCalendar(handymenWithPostalCodes, Optional.of(requestedDateTime), isAsSoonAsPossible)
                .doOnError(throwable -> System.out.println("Error: " + throwable.getMessage()))
                .block();

        System.out.println(availableHandymen);
        assertThat(availableHandymen).isNotNull();
        assertThat(availableHandymen.stream().map(HandymanWithPostalCode::outlookEMailAddress).toList()).contains(handymanEmail2);

    }

}
