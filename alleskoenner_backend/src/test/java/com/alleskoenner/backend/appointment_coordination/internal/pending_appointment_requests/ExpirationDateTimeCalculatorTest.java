package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import java.time.Duration;
import java.time.LocalTime;
import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.dtos.HandymanAppointmentQueryResponse;
import static org.assertj.core.api.Assertions.assertThat;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ExpirationDateTimeCalculatorTest {

    @InjectMocks
    ExpirationDateTimeCalculator calculator;

    @Mock
    AppointmentCoordinationConfig config;
    @Mock
    AppointmentCoordinationConfig.QuietHours quietHours;

    @BeforeEach
    void setup() {
        when(config.getAppointmentRequestAcceptanceTimeoutWindow()).thenReturn(Duration.ofMinutes(15));
        when(config.getQuietHours()).thenReturn(quietHours);
        when(quietHours.getStart()).thenReturn(LocalTime.parse("22:01"));
        when(quietHours.getEnd()).thenReturn(LocalTime.parse("06:00"));
    }

    @Test
    void itShouldCalculateExpiredDateTime_2145() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-01T21:45:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);

        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(1); // next day
        assertThat(zonedDateTime.getHour()).isEqualTo(22);
        assertThat(zonedDateTime.getMinute()).isEqualTo(0);
    }

    @Test
    void itShouldCalculateExpiredDateTime_2146() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-01T21:46:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);
        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(2);
        assertThat(zonedDateTime.getHour()).isEqualTo(6);
        assertThat(zonedDateTime.getMinute()).isEqualTo(15);
    }

    @Test
    void itShouldCalculateExpiredDateTime_2200() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-01T22:00:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);

        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(2);
        assertThat(zonedDateTime.getHour()).isEqualTo(6);
        assertThat(zonedDateTime.getMinute()).isEqualTo(15);
    }

    @Test
    void itShouldCalculateExpiredDateTime_2344() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-01T23:44:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);

        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(2);
        assertThat(zonedDateTime.getHour()).isEqualTo(6);
        assertThat(zonedDateTime.getMinute()).isEqualTo(15);
    }

    @Test
    void itShouldCalculateExpiredDateTime_2345() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-01T23:45:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);

        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(2);
        assertThat(zonedDateTime.getHour()).isEqualTo(6);
        assertThat(zonedDateTime.getMinute()).isEqualTo(15);
    }

    @Test
    void itShouldCalculateExpiredDateTime_2346() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-01T23:46:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);

        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(2);
        assertThat(zonedDateTime.getHour()).isEqualTo(6);
        assertThat(zonedDateTime.getMinute()).isEqualTo(15);
    }

    @Test
    void itShouldCalculateExpiredDateTime_2359() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-01T23:59:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);

        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(2);
        assertThat(zonedDateTime.getHour()).isEqualTo(6);
        assertThat(zonedDateTime.getMinute()).isEqualTo(15);
    }

    @Test
    void itShouldCalculateExpiredDateTime_0000() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-02T00:00:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);

        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(3);
        assertThat(zonedDateTime.getHour()).isEqualTo(6);
        assertThat(zonedDateTime.getMinute()).isEqualTo(15);
    }

    @Test
    void itShouldCalculateExpiredDateTime_0001() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-02T00:01:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);

        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(2);
        assertThat(zonedDateTime.getHour()).isEqualTo(6);
        assertThat(zonedDateTime.getMinute()).isEqualTo(15);
    }

    @Test
    void itShouldCalculateExpiredDateTime_0559() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-01T05:59:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);

        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(1);
        assertThat(zonedDateTime.getHour()).isEqualTo(6);
        assertThat(zonedDateTime.getMinute()).isEqualTo(15);
    }

    @Test
    void itShouldCalculateExpiredDateTime_0600() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-01T06:00:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);

        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(1);
        assertThat(zonedDateTime.getHour()).isEqualTo(6);
        assertThat(zonedDateTime.getMinute()).isEqualTo(15);
    }

    @Test
    void itShouldCalculateExpiredDateTime_0601() {
        var appointmentRequest = mock(HandymanAppointmentQueryResponse.class);
        when(appointmentRequest.systemCreatedAt()).thenReturn("2020-01-01T06:01:00+02:00");

        var zonedDateTime = calculator.calculateExpiredDateTime(appointmentRequest);

        assertThat(zonedDateTime.getDayOfMonth()).isEqualTo(1);
        assertThat(zonedDateTime.getHour()).isEqualTo(6);
        assertThat(zonedDateTime.getMinute()).isEqualTo(16);
    }
}
