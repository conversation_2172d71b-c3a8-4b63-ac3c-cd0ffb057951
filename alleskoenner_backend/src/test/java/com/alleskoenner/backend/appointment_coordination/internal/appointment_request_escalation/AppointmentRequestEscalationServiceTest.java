package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation;

import java.util.List;

import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation.AppointmentRequestStatus.REJECTED;
import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation.AppointmentRequestStatus.TIMED_OUT;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation.dtos.AppointmentRequestEscalationResponse;
import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import com.alleskoenner.backend.emails.BusinessNotificationEmailServiceInterface;
import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class AppointmentRequestEscalationServiceTest {

    @Mock
    private AppointmentRequestEscalationClient client;

    @Mock
    private BusinessNotificationEmailServiceInterface businessNotificationEmailService;

    @Mock
    private ErrorNotificationEmailServiceInterface errorNotificationEmailService;

    @Mock
    private AppointmentCoordinationConfig config;

    @Mock
    private AppointmentCoordinationConfig.EscalationJob escalationJob;

    @InjectMocks
    private AppointmentRequestEscalationService service;

    @BeforeEach
    void setUp() {
        when(config.getEscalationJob()).thenReturn(escalationJob);
    }

    @Test
    void checkForAppointmentRequestToEscalate_shouldDoNothingWhenJobIsDisabled() {
        when(escalationJob.isEnabled()).thenReturn(false);

        service.checkForAppointmentRequestToEscalate();

        verify(client, never()).getAppointmentRequestsToEscalate();
    }

    @Test
    void checkForAppointmentRequestToEscalate_shouldProcessTimedOutRequests() {
        var appointmentRequest = createAppointmentRequest(TIMED_OUT.getValue());

        when(escalationJob.isEnabled()).thenReturn(true);
        when(client.getAppointmentRequestsToEscalate()).thenReturn(Mono.just(List.of(appointmentRequest)));
        when(client.setAppointmentRequestStatusToEscalate(appointmentRequest.systemId())).thenReturn(Mono.empty());

        service.checkForAppointmentRequestToEscalate();

        verify(client).getAppointmentRequestsToEscalate();
        verify(client).setAppointmentRequestStatusToEscalate(appointmentRequest.systemId());
        verify(businessNotificationEmailService).sendBusinessNotificationEmail(anyString());
    }

    @Test
    void checkForAppointmentRequestToEscalate_shouldProcessRejectedRequests() {
        var appointmentRequest = createAppointmentRequest(REJECTED.getValue());

        when(escalationJob.isEnabled()).thenReturn(true);
        when(client.getAppointmentRequestsToEscalate()).thenReturn(Mono.just(List.of(appointmentRequest)));
        when(client.setAppointmentRequestStatusToEscalate(appointmentRequest.systemId())).thenReturn(Mono.empty());

        service.checkForAppointmentRequestToEscalate();

        verify(client).getAppointmentRequestsToEscalate();
        verify(client).setAppointmentRequestStatusToEscalate(appointmentRequest.systemId());
        verify(businessNotificationEmailService).sendBusinessNotificationEmail(anyString());
    }

    @Test
    void checkForAppointmentRequestToEscalate_shouldSkipRequestsWithOtherStatuses() {
        var appointmentRequest = createAppointmentRequest("Pending");

        when(escalationJob.isEnabled()).thenReturn(true);
        when(client.getAppointmentRequestsToEscalate()).thenReturn(Mono.just(List.of(appointmentRequest)));

        service.checkForAppointmentRequestToEscalate();

        verify(client).getAppointmentRequestsToEscalate();
        verify(client, never()).setAppointmentRequestStatusToEscalate(any());
        verify(businessNotificationEmailService, never()).sendBusinessNotificationEmail(any());
    }

    @Test
    void checkForAppointmentRequestToEscalate_shouldHandleErrorsWhenSettingStatus() {
        var appointmentRequest = createAppointmentRequest(TIMED_OUT.getValue());
        var error = new RuntimeException("Test error");

        when(escalationJob.isEnabled()).thenReturn(true);
        when(client.getAppointmentRequestsToEscalate()).thenReturn(Mono.just(List.of(appointmentRequest)));
        when(client.setAppointmentRequestStatusToEscalate(appointmentRequest.systemId())).thenReturn(Mono.error(error));

        service.checkForAppointmentRequestToEscalate();

        verify(client).getAppointmentRequestsToEscalate();
        verify(client).setAppointmentRequestStatusToEscalate(appointmentRequest.systemId());
        verify(businessNotificationEmailService, never()).sendBusinessNotificationEmail(any());
        verify(errorNotificationEmailService).sendErrorNotificationEmail(eq(error), anyString(), anyString(), anyString());
    }

    @Test
    void checkForAppointmentRequestToEscalate_shouldProcessMultipleRequests() {
        var timedOutRequest = createAppointmentRequest(TIMED_OUT.getValue());
        var rejectedRequest = createAppointmentRequest(REJECTED.getValue());
        var otherRequest = createAppointmentRequest("Some Other Status");

        when(escalationJob.isEnabled()).thenReturn(true);
        when(client.getAppointmentRequestsToEscalate()).thenReturn(Mono.just(List.of(timedOutRequest, rejectedRequest, otherRequest)));
        when(client.setAppointmentRequestStatusToEscalate(timedOutRequest.systemId())).thenReturn(Mono.empty());
        when(client.setAppointmentRequestStatusToEscalate(rejectedRequest.systemId())).thenReturn(Mono.empty());

        service.checkForAppointmentRequestToEscalate();

        verify(client).getAppointmentRequestsToEscalate();
        verify(client).setAppointmentRequestStatusToEscalate(timedOutRequest.systemId());
        verify(client).setAppointmentRequestStatusToEscalate(rejectedRequest.systemId());
        verify(client, never()).setAppointmentRequestStatusToEscalate(otherRequest.systemId());
        verify(businessNotificationEmailService, times(2)).sendBusinessNotificationEmail(anyString());
    }

    private AppointmentRequestEscalationResponse createAppointmentRequest(String status) {
        var systemId = "appointmentSystemId-" + status.replace(" ", "-");
        return new AppointmentRequestEscalationResponse("appointmentNo", status, systemId
        );
    }
}
