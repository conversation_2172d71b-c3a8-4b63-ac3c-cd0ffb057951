package com.alleskoenner.backend.shared;

import java.time.Instant;
import java.util.Arrays;
import java.util.Map;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.test.web.reactive.server.SecurityMockServerConfigurers;
import static org.springframework.security.test.web.reactive.server.SecurityMockServerConfigurers.mockJwt;
import org.springframework.stereotype.Component;

@Component
public class OIDCUserMock {

    @Value("${security.client.customer-client-id}")
    private String customerClientId;

    @Value("${security.client.handyman-client-id}")
    private String handymanClientId;

    @NotNull
    public SecurityMockServerConfigurers.JwtMutator handyman(String loginMail) {
        return getOidcLoginMutator(loginMail, handymanClientId);
    }

    @NotNull
    public SecurityMockServerConfigurers.JwtMutator customer(String loginMail, String... roles) {
        return getOidcLoginMutator(loginMail, customerClientId, roles);
    }

    @NotNull
    private static SecurityMockServerConfigurers.JwtMutator getOidcLoginMutator(String loginMail, String clientId, String... roles) {
        return mockJwt()
                .jwt(new Jwt("mock-token",  // Token value
                        Instant.now(), // Issued at
                        Instant.now().plusSeconds(3600), // Expires in 1 hour
                        Map.of("alg", "RS256"), // Header
                        Map.of(
                                "email", loginMail,
                                "azp", clientId,
                                "sub", "mock-sub"
                        ))
                ).authorities(
                        Arrays.stream(roles)
                                .map(SimpleGrantedAuthority::new)
                                .toArray(SimpleGrantedAuthority[]::new)
                );
    }
}
