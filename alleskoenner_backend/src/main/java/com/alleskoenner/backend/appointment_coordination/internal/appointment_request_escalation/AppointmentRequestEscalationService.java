package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation;

import java.util.EnumSet;
import java.util.Set;

import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation.AppointmentRequestStatus.REJECTED;
import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation.AppointmentRequestStatus.TIMED_OUT;
import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation.AppointmentRequestStatus.TIMED_OUT_ESCAPED;
import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation.AppointmentRequestStatus.from;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation.dtos.AppointmentRequestEscalationResponse;
import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import com.alleskoenner.backend.emails.BusinessNotificationEmailServiceInterface;
import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppointmentRequestEscalationService {

    private static final Set<AppointmentRequestStatus> ESCALATABLE_STATUSES = EnumSet.of(TIMED_OUT, TIMED_OUT_ESCAPED, REJECTED);

    private final AppointmentRequestEscalationClient client;
    private final BusinessNotificationEmailServiceInterface businessNotificationEmailService;
    private final ErrorNotificationEmailServiceInterface errorNotificationEmailServiceInterface;
    private final AppointmentCoordinationConfig config;


    /*
     * We have two mechanisms in business central that define the "Timed Out" and "Rejected" status of an appointment request.
     * For the status "Timed Out" we have a job in BC that automatically sets the appointment request status to "Timed Out"
     * after a configured number of minutes after creation of the appointment request.
     * For the status "Rejected" we have a mechanism that automatically sets the appointment request status to "Rejected"
     * when all handymen reject the appointment request.
     * This is why this scheduled job looks only for these two statuses and escalates them by sending business notification emails.
     * Their statuses are set to "Escalated" in BC to not constantly trigger a notification email.
     */
    @Scheduled(
            initialDelayString = "${appointment-coordination.escalation-job.initialDelayInSeconds}",
            fixedDelayString = "${appointment-coordination.escalation-job.fixedDelayInSeconds}",
            timeUnit = java.util.concurrent.TimeUnit.SECONDS
    )
    public void checkForAppointmentRequestToEscalate() {
        if (!config.getEscalationJob().isEnabled()) {
            return;
        }

        client.getAppointmentRequestsToEscalate()
                .subscribe(appointmentRequests -> appointmentRequests.stream()
                        .filter(this::isEscalatable)
                        .forEach(this::escalate));
    }

    private boolean isEscalatable(AppointmentRequestEscalationResponse appointmentRequest) {
        var status = from(appointmentRequest.status());
        return status != null && ESCALATABLE_STATUSES.contains(status);
    }

    private void escalate(AppointmentRequestEscalationResponse appointmentRequest) {
        client.setAppointmentRequestStatusToEscalate(appointmentRequest.systemId())
                .doOnSuccess(unused -> handleSuccessfulEscalation(appointmentRequest))
                .doOnError(error -> handleEscalationError(appointmentRequest, error))
                .subscribe();
    }

    private void handleSuccessfulEscalation(AppointmentRequestEscalationResponse appointmentRequest) {
        log.info("Successfully set appointment request to escalated: {}", appointmentRequest.no());
        var message = buildEscalationMessage(appointmentRequest);
        businessNotificationEmailService.sendBusinessNotificationEmail(message);
    }

    private void handleEscalationError(AppointmentRequestEscalationResponse appointmentRequest, Throwable error) {
        log.error("Error escalating appointment request: {}", appointmentRequest.no(), error);
        errorNotificationEmailServiceInterface.sendErrorNotificationEmail(
                error,
                "Error escalating appointment request.",
                "Appointment Request Number: " + appointmentRequest.no(),
                "Further Details: " + appointmentRequest
        );
    }

    @NotNull
    private String buildEscalationMessage(AppointmentRequestEscalationResponse appointmentRequest) {
        var status = from(appointmentRequest.status());
        var statusMessage = status == TIMED_OUT ? "has timed out." : "has been rejected by all handymen.";

        return String.format("The appointment request with number %s %s Further Details: %s",
                appointmentRequest.no(),
                statusMessage,
                appointmentRequest
        );
    }
}
