package com.alleskoenner.backend.appointment_coordination.internal.handymen_by_postal_code;

import java.util.List;

import com.alleskoenner.backend.shared.LoginEmail;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.With;

@With
public record HandymanWithPostalCode(
        String handymanSystemId,
        LoginEmail loginEmail,
        String companyID,
        String companyName,
        @JsonProperty("deviceToken")
        @JsonDeserialize(using = CommaSeparatedStringToListDeserializer.class)
        List<String> deviceTokens,
        String environmentName,
        String name,
        String outlookEMailAddress,
        String postalCode,
        boolean active,
        String auxiliaryIndex1
) {
}
