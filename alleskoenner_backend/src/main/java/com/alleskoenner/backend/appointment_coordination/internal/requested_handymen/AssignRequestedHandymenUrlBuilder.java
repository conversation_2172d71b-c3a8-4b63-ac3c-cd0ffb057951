package com.alleskoenner.backend.appointment_coordination.internal.requested_handymen;

import com.alleskoenner.backend.appointment_coordination.internal.business_central.AppointmentCoordinationBcConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AssignRequestedHandymenUrlBuilder {

    private final AppointmentCoordinationBcConfig config;

    public AssignRequestedHandymenUrlBuilder(AppointmentCoordinationBcConfig config) {
        this.config = config;
    }

    String buildAssignRequestedHandymenQueryUrl(String appointmentRequestId) {
        return String.format("v2.0/%s/api/ITV/appointments/v2.0/companies(%s)/appointmentRequests(%s)/Microsoft.NAV.assignHandymen", config.getBcEnvironment(), config.getBcCompanyId(), appointmentRequestId);
    }
}
