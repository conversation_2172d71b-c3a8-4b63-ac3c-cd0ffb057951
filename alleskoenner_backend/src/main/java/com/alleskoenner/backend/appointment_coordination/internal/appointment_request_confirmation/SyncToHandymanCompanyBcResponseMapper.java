package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

@Component
public class SyncToHandymanCompanyBcResponseMapper {

    private static final ObjectMapper objectMapper = new ObjectMapper()
            .findAndRegisterModules()
            .configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    public static SyncToHandymanCompanyBcResponseValue map(String syncToHandymanCompanyBcResponseValueJson) {
        try {
            return objectMapper.readValue(syncToHandymanCompanyBcResponseValueJson, SyncToHandymanCompanyBcResponseValue.class);
        } catch (JsonProcessingException e) {
            throw new IllegalStateException("Could not parse json response from 'value' field", e);
        }
    }
}