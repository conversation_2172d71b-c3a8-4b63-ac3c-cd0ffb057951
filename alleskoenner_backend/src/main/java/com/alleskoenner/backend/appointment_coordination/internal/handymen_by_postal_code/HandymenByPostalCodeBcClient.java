package com.alleskoenner.backend.appointment_coordination.internal.handymen_by_postal_code;

import java.util.List;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_coordination.NoHandymanAvailableByPostalCode;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class HandymenByPostalCodeBcClient {

    private final WebClient appointmentCoordinationBusinessCentralWebClient;
    private final HandymenByPostalCodeUrlBuilder handymenByPostalCodeUrlBuilder;

    public Mono<List<HandymanWithPostalCode>> fetchHandymenByPostalCode(String postalCode) {

        return appointmentCoordinationBusinessCentralWebClient.get()
                .uri(handymenByPostalCodeUrlBuilder.buildHandymanByPostalCodeQueryUrl(postalCode))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(HandymenByPostalCodeResponse.class)
                                .map(HandymenByPostalCodeResponse::value)
                                .flatMap(handymenWithPostalCodes -> checkHandymanListNotEmpty(handymenWithPostalCodes, postalCode));
                    } else {
                        return mapResponseBodyToError(response);
                    }
                });
    }

    @NotNull
    private static Mono<List<HandymanWithPostalCode>> checkHandymanListNotEmpty(List<HandymanWithPostalCode> handymenWithPostalCodes, String postalCode) {
        if (handymenWithPostalCodes.isEmpty()) {
            return Mono.error(new NoHandymanAvailableByPostalCode("No handyman found for postal code: " + postalCode + "."));
        } else {
            return Mono.just(handymenWithPostalCodes);
        }
    }

    private static Mono<List<HandymanWithPostalCode>> mapResponseBodyToError(ClientResponse response) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> {
                    log.error("Request failed with status code {} and body {}", response.statusCode(), errorBody);
                    return Mono.error(new WebClientResponseException(
                            "Request failed with status code " + response.statusCode(),
                            response.statusCode().value(),
                            response.statusCode().toString(), null, errorBody.getBytes(), null
                    ));
                });
    }
}
