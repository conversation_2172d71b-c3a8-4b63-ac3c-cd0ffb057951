package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_rejection;

import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation.AppointmentRequestStatus.REJECTED;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class AppointmentRequestRejectionClient {

    private final WebClient appointmentCoordinationBusinessCentralWebClient;
    private final AppointmentRequestRejectionUrlBuilder urlBuilder;

    public Mono<Void> rejectAppointmentRequest(String requestedHandymanId, String reason) {

        record AppointmentRequestRejectionBody(String reason, String state) {
        }

        return appointmentCoordinationBusinessCentralWebClient.patch()
                .uri(urlBuilder.buildUrl(requestedHandymanId))
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.IF_MATCH, "*")
                .bodyValue(new AppointmentRequestRejectionBody(reason, REJECTED.getValue()))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return Mono.empty();
                    } else {
                        return mapResponseBodyToError(response, requestedHandymanId);
                    }
                })
                .doOnError(e -> log.error(e.getMessage(), e));
    }

    Mono<Void> mapResponseBodyToError(ClientResponse response, String requestedHandymanId) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(
                        new BusinessCentralErrorResponseException(
                                String.format(
                                        "Error while getting requested handyman. ID: %s.",
                                        requestedHandymanId),
                                response,
                                errorBody)));
    }
}
