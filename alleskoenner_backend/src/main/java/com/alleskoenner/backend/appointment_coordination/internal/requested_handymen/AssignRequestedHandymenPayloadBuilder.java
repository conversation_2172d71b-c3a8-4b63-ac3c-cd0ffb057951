package com.alleskoenner.backend.appointment_coordination.internal.requested_handymen;

import com.alleskoenner.backend.shared.LoginEmail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AssignRequestedHandymenPayloadBuilder {

    /**
     * Builds a payload for assigning multiple handymen to an appointment request.
     * <p>
     * The Business Central function to assign multiple handymen can only receive a text property and does not support JSON.
     * Therefore, a valid payload must include an escaped JSON array in the following format:
     * {@code { "handymenArrayText" : "[\"<EMAIL>\",\"<EMAIL>\"]" }}
     *
     * @param handymen List of handyman identifiers.
     * @return An instance of {@link AssignRequestedHandymenBcPayload} containing the formatted payload.
     */
    AssignRequestedHandymenBcPayload buildAssignRequestedHandymenPayload(List<LoginEmail> handymen) {
        final var handymenListAsString = handymen.stream()
                .map(handyman -> "\"" + handyman.email() + "\"")
                .collect(Collectors.joining(","));
        return new AssignRequestedHandymenBcPayload("[" + handymenListAsString + "]");
    }
}
