package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import static java.util.Objects.requireNonNull;
import java.util.Optional;

import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import com.microsoft.graph.models.DateTimeTimeZone;
import com.microsoft.graph.models.ScheduleInformation;
import com.microsoft.graph.users.item.calendar.getschedule.GetSchedulePostRequestBody;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class AvailabilityRequester {

    private final GraphClient graphClient;
    private final AppointmentCoordinationConfig config;
    private final StartAndEndTimeCalculator startAndEndTimeCalculator;

    /**
     * Returns the schedule information for all handymen at a specific time or in the default time window for ASAP requests.
     */
    public List<ScheduleInformation> getScheduleInformationList(@NotNull List<String> handymenEmailAddresses, Optional<ZonedDateTime> requestedDateTime, boolean isAsSoonAsPossible) {
        var startAndEndDateTime = startAndEndTimeCalculator.getStartAndEndDateTime(requestedDateTime, isAsSoonAsPossible);
        var schedulePostRequestBody = getGetSchedulePostRequestBody(handymenEmailAddresses, startAndEndDateTime.startDateTimeIncludingTravelTime(), startAndEndDateTime.endDateTimeWithFixedAppointmentDuration1());
        return makeScheduleRequest(schedulePostRequestBody);
    }

    /**
     * Returns the schedule information for a single handyman in a custom time window.
     */
    public ScheduleInformation getScheduleInformationForHandyman(String handymanEmailAddress, ZonedDateTime windowStart, ZonedDateTime windowEnd) {
        var schedulePostRequestBody = getGetSchedulePostRequestBody(List.of(handymanEmailAddress), windowStart, windowEnd);
        return makeScheduleRequest(schedulePostRequestBody).getFirst();
    }

    private List<ScheduleInformation> makeScheduleRequest(GetSchedulePostRequestBody schedulePostRequestBody) {
        final var staticEmailAddressForApiRequest = config.getCalendar().getStaticEmailAddressForApiRequest();
        final var UNEXPECTED_ERROR_MESSAGE = "An unexpected error occurred while fetching handyman schedules for %s".formatted(schedulePostRequestBody.getSchedules());
        final var getSchedulePostResponse = requireNonNull(graphClient
                .getClient()
                .users()
                .byUserId(staticEmailAddressForApiRequest)
                .calendar()
                .getSchedule()
                .post(schedulePostRequestBody), UNEXPECTED_ERROR_MESSAGE);
        return requireNonNull(getSchedulePostResponse.getValue(), UNEXPECTED_ERROR_MESSAGE);
    }

    GetSchedulePostRequestBody getGetSchedulePostRequestBody(
            @NotEmpty @NotNull List<String> handymanEmails,
            ZonedDateTime startDateTime,
            ZonedDateTime endDateTime) {

        var schedulePostRequestBody = new GetSchedulePostRequestBody();
        schedulePostRequestBody.setSchedules(handymanEmails);

        var dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

        var startTime = new DateTimeTimeZone();
        startTime.setDateTime(dateTimeFormatter.format(startDateTime));
        startTime.setTimeZone(startDateTime.getZone().getId());
        schedulePostRequestBody.setStartTime(startTime);

        var endTime = new DateTimeTimeZone();
        endTime.setDateTime(dateTimeFormatter.format(endDateTime));
        endTime.setTimeZone(endDateTime.getZone().getId());
        schedulePostRequestBody.setEndTime(endTime);

        schedulePostRequestBody.setAvailabilityViewInterval(config.getCalendar().getAvailabilityViewIntervalInMinutes());

        return schedulePostRequestBody;
    }

}
