package com.alleskoenner.backend.appointment_coordination.internal.config;

import java.time.Duration;
import java.time.LocalTime;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


@Slf4j
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "appointment-coordination")
public class AppointmentCoordinationConfig {

    private Duration appointmentRequestAcceptanceTimeoutWindow;
    private QuietHours quietHours;
    private Calendar calendar;
    private EscalationJob escalationJob;


    @Getter
    @Setter
    public static class QuietHours {
        private LocalTime start;
        private LocalTime end;
    }

    @Getter
    @Setter
    public static class Calendar {
        private int travelTimeToCustomerInMinutes;
        private int fixedDurationOfAppointmentInMinutes;
        private int availabilityViewIntervalInMinutes;
        private Duration defaultAsapWindow;

        private String staticEmailAddressForApiRequest;

        private MicrosoftGraphClient microsoftGraphClient;

        @Getter
        @Setter
        public static class MicrosoftGraphClient {
            private String baseurl;
            private String clientId;
            private String clientSecret;
            private String scope;
            private String tenant;
        }
    }

    @Getter
    @Setter
    public static class EscalationJob {
        private boolean enabled;
        private Integer initialDelayInSeconds;
        private Integer fixedDelayInSeconds;
    }

    @PostConstruct
    private void checkEscalationJobEnabled() {
        if (!this.getEscalationJob().isEnabled()) {
            log.warn("Appointment request escalation job is disabled.");
        }
    }
}
