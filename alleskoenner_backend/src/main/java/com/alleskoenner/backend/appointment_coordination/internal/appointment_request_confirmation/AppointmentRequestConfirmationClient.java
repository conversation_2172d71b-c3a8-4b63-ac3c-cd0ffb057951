package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation.AppointmentRequestStatus.CONFIRMED;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class AppointmentRequestConfirmationClient {

    private final WebClient appointmentCoordinationBusinessCentralWebClient;
    private final AppointmentRequestConfirmationUrlBuilder urlBuilder;

    public Mono<Void> confirmAppointmentRequest(LoginEmail loginEmail, String appointmentRequestId, String odataEtag) {

        record AppointmentRequestConfirmationBody(String confirmedHandyman, String status) {
        }

        return appointmentCoordinationBusinessCentralWebClient.patch()
                .uri(urlBuilder.buildUrl(appointmentRequestId))
                .contentType(MediaType.APPLICATION_JSON)

                .header(HttpHeaders.IF_MATCH, odataEtag)

                .bodyValue(new AppointmentRequestConfirmationBody(loginEmail.email(), CONFIRMED.getValue()))

                .exchangeToMono(response -> {

                    if (response.statusCode().is2xxSuccessful()) {
                        return Mono.empty();
                    } else {
                        return mapResponseBodyToError(response, loginEmail, appointmentRequestId);
                    }

                });
    }

    public Mono<Void> mapResponseBodyToError(ClientResponse response, LoginEmail loginEmail, String appointmentRequestId) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> {
                    if (errorBody.contains("BadRequest_ResourceNotFound")) {

                        log.error("Appointment request {} does not exist anymore for handyman {}. Status code: {}. Error: {}",
                                appointmentRequestId, loginEmail.email(), response.statusCode(), errorBody);

                        return Mono.error(new UserFacingException(HttpStatus.GONE, "Appointment request does not exist anymore."));
                    } else {

                        return Mono.error(new WebClientResponseException(
                                String.format("Setting appointment request %s to confirmed for handyman %s failed with status code %s and body %s",
                                        appointmentRequestId, loginEmail.email(), response.statusCode(), errorBody),
                                response.statusCode().value(),
                                response.statusCode().toString(), null, errorBody.getBytes(), null));
                    }
                });
    }
}
