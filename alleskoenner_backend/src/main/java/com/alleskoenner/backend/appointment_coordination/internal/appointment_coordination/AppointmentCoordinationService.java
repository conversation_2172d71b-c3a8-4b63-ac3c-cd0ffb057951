package com.alleskoenner.backend.appointment_coordination.internal.appointment_coordination;

import java.util.Collection;

import com.alleskoenner.backend.appointment_coordination.AppointmentCoordinationDetails;
import com.alleskoenner.backend.appointment_coordination.AppointmentCoordinationServiceInterface;
import com.alleskoenner.backend.appointment_coordination.internal.calendar.CalendarAvailabilityService;
import com.alleskoenner.backend.appointment_coordination.internal.handymen_by_postal_code.HandymanWithPostalCode;
import com.alleskoenner.backend.appointment_coordination.internal.handymen_by_postal_code.HandymenByPostalCodeBcClient;
import com.alleskoenner.backend.appointment_coordination.internal.requested_handymen.AssignRequestedHandymenBcClient;
import com.alleskoenner.backend.emails.BusinessNotificationEmailServiceInterface;
import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import com.alleskoenner.backend.push_notification.AppointmentRequestNotificationSenderInterface;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@AllArgsConstructor
public class AppointmentCoordinationService implements AppointmentCoordinationServiceInterface {

    private final HandymenByPostalCodeBcClient handymenByPostalCodeBcClient;
    private final AssignRequestedHandymenBcClient assignRequestedHandymenBcClient;
    private final CalendarAvailabilityService calendarAvailabilityService;
    private final AppointmentRequestNotificationSenderInterface notificationSender;
    private final ErrorNotificationEmailServiceInterface errorNotificationEmailService;
    private final BusinessNotificationEmailServiceInterface businessNotificationEmailService;

    @Override
    public Mono<Void> startAppointmentCoordination(AppointmentCoordinationDetails appointmentCoordinationDetails) {
        return handymenByPostalCodeBcClient.fetchHandymenByPostalCode(appointmentCoordinationDetails.postalCode())

                .doOnSuccess(handymenWithPostalCodes -> log.info("Following Handymen serve the postal code {}: {}", appointmentCoordinationDetails.postalCode(), handymenWithPostalCodes))

                .flatMap(handymen -> calendarAvailabilityService.getAvailableHandymenByCalendar(handymen, appointmentCoordinationDetails.requestedDateTime(), appointmentCoordinationDetails.isAsSoonAsPossible()))

                .doOnSuccess(availableHandymen -> {
                    final var availableHandymanEmails = availableHandymen.stream().map(HandymanWithPostalCode::outlookEMailAddress).toList();
                    final var requestedTime = appointmentCoordinationDetails.isAsSoonAsPossible() ? "ASAP" : appointmentCoordinationDetails.requestedDateTime();
                    log.info("Following handymen have time in their calendar for the requested time {}: {}", requestedTime, availableHandymanEmails);
                })

                .flatMap(availableHandymen -> {
                    var availableHandymanLoginEmails = availableHandymen.stream().map(HandymanWithPostalCode::loginEmail).toList();
                    return assignRequestedHandymenBcClient.assignRequestedHandymenToAppointmentRequest(appointmentCoordinationDetails.appointmentRequestId(), availableHandymanLoginEmails)
                            .thenReturn(availableHandymen);
                })

                .flatMap(availableHandymen -> {
                    var deviceTokens = availableHandymen.stream()
                            .map(HandymanWithPostalCode::deviceTokens)
                            .flatMap(Collection::stream).toList();
                    var requestedDateTime = appointmentCoordinationDetails.requestedDateTime().orElse(null);
                    return notificationSender.sendAppointmentRequestNotification(deviceTokens, requestedDateTime, appointmentCoordinationDetails.isAsSoonAsPossible());
                })

                .doOnError(e -> logAndSendNotificationEmails(e, appointmentCoordinationDetails));
    }

    private void logAndSendNotificationEmails(Throwable e, AppointmentCoordinationDetails appointmentCoordinationDetails) {
        if (e instanceof BusinessException) {
            var message = e.getMessage() + " Further details: " + appointmentCoordinationDetails;
            log.warn(message);
            businessNotificationEmailService.sendBusinessNotificationEmail(message);
        } else {
            var furtherDetailsMessage = " Further details: " + appointmentCoordinationDetails;
            log.error("Some error occurred during appointment coordination: " + e.getMessage() + furtherDetailsMessage, e);
            errorNotificationEmailService.sendErrorNotificationEmail(e, furtherDetailsMessage);
        }
    }

}
