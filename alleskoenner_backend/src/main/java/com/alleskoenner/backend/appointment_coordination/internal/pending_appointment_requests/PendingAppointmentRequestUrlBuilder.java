package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import com.alleskoenner.backend.appointment_coordination.internal.business_central.AppointmentCoordinationBcConfig;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PendingAppointmentRequestUrlBuilder {

    private final AppointmentCoordinationBcConfig config;

    public PendingAppointmentRequestUrlBuilder(AppointmentCoordinationBcConfig config) {
        this.config = config;
    }

    String buildUrl(LoginEmail loginEmail) {
        return String.format("v2.0/%s/api/ITV/handyman/v2.0/companies(%s)/handymanAppointmentQuery?$filter=loginEmail eq '%s' And handymenState eq 'pending'", config.getBcEnvironment(), config.getBcCompanyId(), loginEmail.email());
    }

}
