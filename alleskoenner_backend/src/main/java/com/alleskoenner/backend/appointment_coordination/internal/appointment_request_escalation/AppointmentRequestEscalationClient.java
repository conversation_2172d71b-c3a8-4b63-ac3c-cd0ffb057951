package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation;

import java.util.List;
import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation.AppointmentRequestStatus.ESCALATED;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation.dtos.AppointmentRequestEscalationContextResponse;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation.dtos.AppointmentRequestEscalationResponse;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class AppointmentRequestEscalationClient {

    private final WebClient appointmentCoordinationBusinessCentralWebClient;
    private final AppointmentRequestEscalationUrlBuilder urlBuilder;

    public Mono<List<AppointmentRequestEscalationResponse>> getAppointmentRequestsToEscalate() {

        return appointmentCoordinationBusinessCentralWebClient.get()
                .uri(urlBuilder.buildGettingAppointmentRequestsToEscalateUrl())
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(AppointmentRequestEscalationContextResponse.class)
                                .map(AppointmentRequestEscalationContextResponse::value);
                    } else {
                        return mapResponseBodyToError(response);
                    }
                });
    }

    Mono<List<AppointmentRequestEscalationResponse>> mapResponseBodyToError(ClientResponse response) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(
                        new BusinessCentralErrorResponseException("Some error occurred while fetching appointment requests", response, errorBody)
                ));
    }

    public Mono<Void> setAppointmentRequestStatusToEscalate(String appointmentRequestId) {

        record SettingStatusToEscalateBody(String status) {
        }

        return appointmentCoordinationBusinessCentralWebClient.patch()
                .uri(urlBuilder.buildSettingAppointmentRequestToStatusEscalateUrl(appointmentRequestId))
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.IF_MATCH, "*")
                .bodyValue(new SettingStatusToEscalateBody(ESCALATED.getValue()))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return Mono.empty();
                    } else {
                        return mapResponseBodyToError2(response, appointmentRequestId);
                    }
                });
    }

    Mono<Void> mapResponseBodyToError2(ClientResponse response, String appointmentRequestId) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(
                        new BusinessCentralErrorResponseException("Some error occurred while setting appointment request with id " + appointmentRequestId + " to status '" + ESCALATED.getValue() + "'", response, errorBody)
                ));
    }
}
