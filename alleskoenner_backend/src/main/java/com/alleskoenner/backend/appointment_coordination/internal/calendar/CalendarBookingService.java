package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation.SyncToHandymanCompanyBcResponseValue;
import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import java.time.Duration;
import java.time.ZonedDateTime;
import static java.time.temporal.ChronoUnit.HOURS;

@Service
@AllArgsConstructor
public class CalendarBookingService {
    private final AppointmentCreator appointmentCreator;
    private final AvailabilityRequester availabilityRequester;
    private final AvailabilityEvaluator availabilityEvaluator;
    private final AppointmentCoordinationConfig config;

    public Mono<Void> findEarliestMatchingTimeSlotAndBookAppointment(LoginEmail loginEmail, SyncToHandymanCompanyBcResponseValue syncResponse) {
        if (syncResponse.asSoonAsPossible()) {
            final var availabilityViewIntervalInMinutes = config.getCalendar().getAvailabilityViewIntervalInMinutes();
            final var windowStart = roundUpToNextInterval(ZonedDateTime.now(), availabilityViewIntervalInMinutes);
            final var windowEnd = windowStart.plus(config.getCalendar().getDefaultAsapWindow());
            final var scheduleInfo = availabilityRequester.getScheduleInformationForHandyman(loginEmail.email(), windowStart, windowEnd);

            final var travelTimeFactorForInboundAndOutboundTravel = 2L;
            final var totalDurationIncludingTravelTime = getAppointmentDuration()
                    .plusMinutes(config.getCalendar().getTravelTimeToCustomerInMinutes() * travelTimeFactorForInboundAndOutboundTravel);
            final var timeSlotStart = availabilityEvaluator.getFirstAvailableTimeSlot(scheduleInfo, windowStart, totalDurationIncludingTravelTime);
            final var appointmentStartAfterTravelTime = timeSlotStart.plusMinutes(config.getCalendar().getTravelTimeToCustomerInMinutes());

            final var appointmentInformation = fillAppointmentInformation(syncResponse);

            return appointmentCreator.bookAppointmentFor(
                    loginEmail.email(),
                    appointmentStartAfterTravelTime,
                    getAppointmentDuration(),
                    appointmentInformation
            );
        }
        // TODO: else: handle scheduled appointment
        return Mono.empty();
    }

    @NotNull
    private static AppointmentInformation fillAppointmentInformation(SyncToHandymanCompanyBcResponseValue syncResponse) {
        return new AppointmentInformation(
                syncResponse.salesOrderNo(),
                syncResponse.customerName(),
                syncResponse.customerPhoneNo(),
                syncResponse.customerEMail(),
                syncResponse.workDescription(),
                "AK24: " + syncResponse.salesOrderNo(),
                syncResponse.shipToAddress(),
                syncResponse.shipToPostCode(),
                syncResponse.shipToCity()
        );
    }

    private Duration getAppointmentDuration() {
        return Duration.ofMinutes(config.getCalendar().getFixedDurationOfAppointmentInMinutes());
    }

    /*
     * Rounds up the given dateTime to the next interval in minutes.
     * If the dateTime is already at the start of an interval (i.e. seconds and nanos are 0), it will not be rounded up to the next interval.
     * Example for an interval of 15 minutes:
     * 2023-10-01T12:00:00 -> 2023-10-01T12:00:00
     * 2023-10-01T12:00:01 -> 2023-10-01T12:15:00
     * 2023-10-01T12:14:59 -> 2023-10-01T12:15:00
     * 2023-10-01T12:15:00 -> 2023-10-01T12:15:00
     *
     * @param dateTime the dateTime to round up
     * @param intervalMinutes the interval in minutes to round up to
     *
     * @return the rounded up dateTime with seconds and nanoseconds set to 0
     */
    ZonedDateTime roundUpToNextInterval(final ZonedDateTime dateTime, final int intervalMinutes) {
        final var roundedMinute = dateTime.getMinute() + (dateTime.getSecond() > 0 || dateTime.getNano() > 0 ? 1 : 0);
        final var roundedToNextIntervalMinutes = ((roundedMinute + intervalMinutes - 1) / intervalMinutes) * intervalMinutes;
        return dateTime.truncatedTo(HOURS).plusMinutes(Math.min(roundedToNextIntervalMinutes, 60));
    }
}
