package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_rejection;

import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation.AppointmentRequestStatus.PENDING;
import com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.HandymanAppointmentQueryGettingClient;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@RequiredArgsConstructor
@Service
@Slf4j
public class AppointmentRequestRejectionService {

    final AppointmentRequestRejectionClient rejectionClient;
    final HandymanAppointmentQueryGettingClient gettingClient;

    public Mono<Void> reject(LoginEmail loginEmail, String appointmentRequestId, String reason) {
        return gettingClient.getRequestedHandyman(loginEmail, appointmentRequestId)
                .flatMap(handymanAppointmentQueryResponse -> {
                    if (!PENDING.getValue().equals(handymanAppointmentQueryResponse.handymenState())) {
                        return Mono.error(new UserFacingException(HttpStatus.GONE, "Handyman request can't be rejected. Is in state: " + handymanAppointmentQueryResponse.handymenState()));
                    } else {
                        return rejectionClient.rejectAppointmentRequest(
                                handymanAppointmentQueryResponse.requestedHandymenSystemId(),
                                reason);
                    }
                });
    }
}
