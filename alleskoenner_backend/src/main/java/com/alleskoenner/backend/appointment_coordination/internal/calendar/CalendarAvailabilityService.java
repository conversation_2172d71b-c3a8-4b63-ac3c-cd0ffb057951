package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import com.alleskoenner.backend.appointment_coordination.internal.appointment_coordination.NoHandymanAvailableByCalendar;
import com.alleskoenner.backend.appointment_coordination.internal.handymen_by_postal_code.HandymanWithPostalCode;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class CalendarAvailabilityService {

    private final AvailabilityRequester availabilityRequester;
    private final AvailabilityEvaluator availabilityEvaluator;

    public Mono<List<HandymanWithPostalCode>> getAvailableHandymenByCalendar(@NotNull List<HandymanWithPostalCode> handymen, Optional<ZonedDateTime> requestedDateTime, boolean isAsSoonAsPossible) {
        return Mono.fromCallable(() -> {
            final var emailAddresses = handymen.stream().map(HandymanWithPostalCode::outlookEMailAddress).toList();

            final var scheduleInformationList = availabilityRequester.getScheduleInformationList(emailAddresses, requestedDateTime, isAsSoonAsPossible);
            final var emailAddressesOfAvailableHandymen = availabilityEvaluator.getAvailableHandymenEmailsFromResponse(scheduleInformationList);

            var availableHandymen = handymen.stream()
                    .filter(handyman -> emailAddressesOfAvailableHandymen.contains(handyman.outlookEMailAddress()))
                    .toList();
            if (availableHandymen.isEmpty()) {
                if (isAsSoonAsPossible) {
                    throw new NoHandymanAvailableByCalendar("No handyman is currently available according to their calendar. Time of the ASAP request: " + ZonedDateTime.now() + ".");
                }
                var requestedDateTimeText = requestedDateTime.map(ZonedDateTime::toString).orElse("<time not available>");
                throw new NoHandymanAvailableByCalendar("No handyman is currently available according to their calendar. The requested time was: " + requestedDateTimeText + ".");
            }
            return availableHandymen;
        });
    }
}
