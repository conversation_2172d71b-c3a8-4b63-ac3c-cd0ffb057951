package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation;

import com.alleskoenner.backend.appointment_coordination.internal.business_central.AppointmentCoordinationBcConfig;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class AppointmentRequestEscalationUrlBuilder {
    private final AppointmentCoordinationBcConfig config;

    String buildGettingAppointmentRequestsToEscalateUrl() {
        var timedOutStatus = "Timed Out";
        var rejectedStatus = "Rejected";
        return String.format("v2.0/%s/api/ITV/appointments/v2.0/companies(%s)/appointmentRequests?$filter=status eq '%s' Or status eq '%s'", config.getBcEnvironment(), config.getBcCompanyId(), timedOutStatus, rejectedStatus);
    }

    String buildSettingAppointmentRequestToStatusEscalateUrl(String appointmentRequestId) {
        return String.format("v2.0/%s/api/ITV/appointments/v2.0/companies(%s)/appointmentRequests(%s)", config.getBcEnvironment(), config.getBcCompanyId(), appointmentRequestId);
    }
}
