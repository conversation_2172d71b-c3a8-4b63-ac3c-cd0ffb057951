package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class SyncToHandymanCompanyClient {

    private final WebClient appointmentCoordinationBusinessCentralWebClient;
    private final SyncToHandymanCompanyUrlBuilder urlBuilder;

    public Mono<SyncToHandymanCompanyBcResponseValue> syncSalesOrderAndCustomerToHandymanCompany(String appointmentRequestId) {

        return appointmentCoordinationBusinessCentralWebClient.post()
                .uri(urlBuilder.buildUrl(appointmentRequestId))

                .exchangeToMono(response -> {

                    if (response.statusCode().is2xxSuccessful()) {

                        log.info("Successfully synced sales order and customer to handyman company for appointment request {}", appointmentRequestId);
                        return response.bodyToMono(SyncToHandymanCompanyBcResponse.class)
                                .map(SyncToHandymanCompanyBcResponse::value)
                                .map(SyncToHandymanCompanyBcResponseMapper::map);
                    } else {
                        return mapResponseBodyToError(response, appointmentRequestId);
                    }
                });
    }

    private static Mono<SyncToHandymanCompanyBcResponseValue> mapResponseBodyToError(ClientResponse response, String appointmentRequestId) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(new WebClientResponseException(
                        String.format("Syncing customer data and sales order from appointment request %s failed with status code %s and body %s", appointmentRequestId, response.statusCode(), errorBody),
                        response.statusCode().value(),
                        response.statusCode().toString(), null, errorBody.getBytes(), null
                )));
    }
}
