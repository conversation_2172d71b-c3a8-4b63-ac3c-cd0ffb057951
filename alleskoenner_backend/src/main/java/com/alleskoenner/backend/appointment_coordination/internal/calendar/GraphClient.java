package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.microsoft.graph.serviceclient.GraphServiceClient;
import lombok.Getter;
import org.springframework.stereotype.Service;

@Getter
@Service
public class GraphClient {

    GraphServiceClient client;

    public GraphClient(AppointmentCoordinationConfig config) {

        AppointmentCoordinationConfig.Calendar.MicrosoftGraphClient microsoftGraphClientConfig = config.getCalendar()
                .getMicrosoftGraphClient();

        final ClientSecretCredential credential = new ClientSecretCredentialBuilder()
                .clientId(microsoftGraphClientConfig.getClientId())
                .tenantId(microsoftGraphClientConfig.getTenant())
                .clientSecret(microsoftGraphClientConfig.getClientSecret())
                .build();

        client = new GraphServiceClient(credential, microsoftGraphClientConfig.getScope());
    }
}
