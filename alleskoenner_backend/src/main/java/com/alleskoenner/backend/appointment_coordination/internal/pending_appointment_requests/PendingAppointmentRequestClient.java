package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.dtos.HandymanAppointmentQueryContextResponse;
import com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.dtos.HandymanAppointmentQueryResponse;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Component
@AllArgsConstructor
public class PendingAppointmentRequestClient {

    private final WebClient appointmentCoordinationBusinessCentralWebClient;
    private final PendingAppointmentRequestUrlBuilder pendingAppointmentRequestUrlBuilder;

    public Mono<List<HandymanAppointmentQueryResponse>> fetchPendingAppointmentRequests(LoginEmail loginEmail) {

        return appointmentCoordinationBusinessCentralWebClient.get()
                .uri(pendingAppointmentRequestUrlBuilder.buildUrl(loginEmail))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(HandymanAppointmentQueryContextResponse.class)
                                .map(HandymanAppointmentQueryContextResponse::value);
                    } else {
                        return mapResponseBodyToError(response);
                    }
                });
    }

    private static Mono<List<HandymanAppointmentQueryResponse>> mapResponseBodyToError(ClientResponse response) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> {
                    log.error("Fetching pending appointment requests failed with status code {} and body {}", response.statusCode(), errorBody);
                    return Mono.error(new WebClientResponseException(
                            "Fetching pending appointment requests failed with status code " + response.statusCode(),
                            response.statusCode().value(),
                            response.statusCode().toString(), null, errorBody.getBytes(), null
                    ));
                });
    }
}
