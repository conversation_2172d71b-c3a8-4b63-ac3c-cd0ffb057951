package com.alleskoenner.backend.appointment_coordination;

import java.time.ZonedDateTime;
import java.util.Optional;

public record AppointmentCoordinationDetails(
        String appointmentRequestId,
        String addressLine1,
        String city,
        String postalCode,
        String taskDescription,
        Optional<ZonedDateTime> requestedDateTime,
        boolean isAsSoonAsPossible
) {
}
