package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;


@CrossOrigin(value = {"http://localhost:4000/", "http://localhost:4173/", "capacitor://localhost"})
@RestController
@RequestMapping("/appointment-request")
@AllArgsConstructor
public class AppointmentRequestConfirmationController {

    private final AppointmentRequestConfirmationService appointmentRequestConfirmationService;

    @PutMapping(path = "/{appointmentRequestId}/confirm", produces = "application/json")
    public Mono<Void> confirmAndArchive(
            @AuthenticationPrincipal JwtAuthenticationToken principal,
            @PathVariable String appointmentRequestId) {
        return appointmentRequestConfirmationService.confirmAndArchive(new LoginEmail(principal.getToken().getClaim("email")), appointmentRequestId);
    }
}
