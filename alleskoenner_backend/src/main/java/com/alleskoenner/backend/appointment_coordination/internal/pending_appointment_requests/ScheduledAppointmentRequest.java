package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import java.time.ZonedDateTime;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public final class ScheduledAppointmentRequest extends AppointmentRequest {
    public final ZonedDateTime scheduledDateTime;

    public ScheduledAppointmentRequest(String addressLine1, String addressLine2, String postcode, String city, String appointmentRequestId, String description, ZonedDateTime expirationDateTime, ZonedDateTime scheduledDateTime) {
        super(addressLine1, addressLine2, postcode, city, appointmentRequestId, description, expirationDateTime, false);
        this.scheduledDateTime = scheduledDateTime;
    }
}
