package com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query;

import com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.dtos.HandymanAppointmentQueryContextResponse;
import com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.dtos.HandymanAppointmentQueryResponse;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class HandymanAppointmentQueryGettingClient {

    private final WebClient appointmentCoordinationBusinessCentralWebClient;
    private final HandymanAppointmentQueryUrlBuilder urlBuilder;

    public Mono<HandymanAppointmentQueryResponse> getRequestedHandyman(LoginEmail loginEmail, String appointmentRequestId) {

        return appointmentCoordinationBusinessCentralWebClient.get()
                .uri(urlBuilder.buildUrl(loginEmail, appointmentRequestId))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(HandymanAppointmentQueryContextResponse.class)
                                .flatMap(handymanAppointmentQueryContextResponse -> {
                                    final var responseSize = handymanAppointmentQueryContextResponse.value().size();
                                    if (responseSize != 1) {
                                        log.error("HandymanAppointmentQueryResponse expected to have exactly 1 result, but was {}. Login Email: {}, AppointmentRequestId: {}", responseSize, loginEmail, appointmentRequestId);
                                        return mapResponseBodyToError(response, loginEmail, appointmentRequestId);
                                    }
                                    return Mono.just(handymanAppointmentQueryContextResponse.value().getFirst());
                                });
                    } else {
                        return mapResponseBodyToError(response, loginEmail, appointmentRequestId);
                    }
                });
    }

    Mono<HandymanAppointmentQueryResponse> mapResponseBodyToError(ClientResponse response, LoginEmail loginEmail, String appointmentRequestId) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(
                        new BusinessCentralErrorResponseException(
                                "Could not find Requested Handyman with login Email " + loginEmail.email() + " and AppointmentRequestId " + appointmentRequestId,
                                response,
                                errorBody)));
    }
}
