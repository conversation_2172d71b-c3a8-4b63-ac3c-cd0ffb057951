package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_rejection;

import com.alleskoenner.backend.appointment_coordination.internal.appointment_request_rejection.dtos.AppointmentRequestRejectionRequest;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingErrorMessage;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;


@CrossOrigin(value = {"http://localhost:4000/", "http://localhost:4173/", "capacitor://localhost"})
@RestController
@RequestMapping("/appointment-request")
@AllArgsConstructor
public class AppointmentRequestRejectionController {

    private final AppointmentRequestRejectionService appointmentRequestRejectionService;


    @PutMapping(path = "/{appointmentRequestId}/reject", produces = "application/json")
    public Mono<Void> rejectAppointmentRequestForHandyman(
            @AuthenticationPrincipal JwtAuthenticationToken principal,
            @PathVariable String appointmentRequestId,
            @RequestBody AppointmentRequestRejectionRequest rejectAppointmentRequestDto
    ) {
        return appointmentRequestRejectionService.reject(new LoginEmail(principal.getToken()
                .getClaim("email")), appointmentRequestId, rejectAppointmentRequestDto.reason());
    }

    @ExceptionHandler(exception = UserFacingException.class, produces = "application/json")
    public ResponseEntity<UserFacingErrorMessage> handleError(UserFacingException exc) {
        return ResponseEntity.status(exc.getStatusCode()).body(new UserFacingErrorMessage(exc.getMessage()));
    }

    @ExceptionHandler(exception = BusinessCentralErrorResponseException.class, produces = "application/json")
    public ResponseEntity<UserFacingErrorMessage> handleError(BusinessCentralErrorResponseException exc) {
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new UserFacingErrorMessage("An error occurred while rejecting the appointment request."));
    }

}
