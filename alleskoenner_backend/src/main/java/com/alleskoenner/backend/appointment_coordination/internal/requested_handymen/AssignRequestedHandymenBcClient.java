package com.alleskoenner.backend.appointment_coordination.internal.requested_handymen;

import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Component
@AllArgsConstructor
public class AssignRequestedHandymenBcClient {

    private final WebClient appointmentCoordinationBusinessCentralWebClient;
    private final AssignRequestedHandymenUrlBuilder assignRequestedHandymenUrlBuilder;
    private final AssignRequestedHandymenPayloadBuilder assignRequestedHandymenPayloadBuilder;

    public Mono<String> assignRequestedHandymenToAppointmentRequest(String appointmentRequestId, List<LoginEmail> handymen) {

        return appointmentCoordinationBusinessCentralWebClient.post()
                .uri(assignRequestedHandymenUrlBuilder.buildAssignRequestedHandymenQueryUrl(appointmentRequestId))
                .bodyValue(assignRequestedHandymenPayloadBuilder.buildAssignRequestedHandymenPayload(handymen))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(AssignRequestedHandymenBcResponse.class)
                                .map(AssignRequestedHandymenBcResponse::value);
                    } else {
                        return mapResponseBodyToError(response, appointmentRequestId, handymen);
                    }
                });
    }

    private static Mono<String> mapResponseBodyToError(ClientResponse response, String appointmentRequestId, List<LoginEmail> handymen) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> {
                    log.error("Assigning handymen {} to appointment request {} failed with status code {} and body {}", handymen, appointmentRequestId, response.statusCode(), errorBody);
                    return Mono.error(new WebClientResponseException(
                            "Assigning handymen to appointment request failed with status code " + response.statusCode(),
                            response.statusCode().value(),
                            response.statusCode().toString(), null, errorBody.getBytes(), null
                    ));
                });
    }
}
