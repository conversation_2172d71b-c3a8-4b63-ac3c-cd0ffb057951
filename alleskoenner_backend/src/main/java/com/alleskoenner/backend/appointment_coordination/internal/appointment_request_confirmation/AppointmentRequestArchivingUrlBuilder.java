package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.alleskoenner.backend.appointment_coordination.internal.business_central.AppointmentCoordinationBcConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AppointmentRequestArchivingUrlBuilder {

    private final AppointmentCoordinationBcConfig config;

    public AppointmentRequestArchivingUrlBuilder(AppointmentCoordinationBcConfig config) {
        this.config = config;
    }

    String buildUrl() {
        return String.format("v2.0/%s/ODataV4/AppointmentApi_archiveAppointmentRequest?company=%s", config.getBcEnvironment(), config.getBcCompanyId());
    }

}
