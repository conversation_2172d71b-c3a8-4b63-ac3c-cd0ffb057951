package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_rejection;

import com.alleskoenner.backend.appointment_coordination.internal.business_central.AppointmentCoordinationBcConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AppointmentRequestRejectionUrlBuilder {

    private final AppointmentCoordinationBcConfig config;

    public AppointmentRequestRejectionUrlBuilder(AppointmentCoordinationBcConfig config) {
        this.config = config;
    }

    String buildUrl(String requestedHandymanId) {
        return String.format("v2.0/%s/api/ITV/handyman/v2.0/companies(%s)/requestedHandymen(%s)", config.getBcEnvironment(), config.getBcCompanyId(), requestedHandymanId);
    }

}
