package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import java.time.ZonedDateTime;

import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public final class AsapAppointmentRequest extends AppointmentRequest {

    public AsapAppointmentRequest(String addressLine1, String addressLine2, String postcode, String city, String appointmentRequestId, String description, ZonedDateTime expirationDateTime) {
        super(addressLine1, addressLine2, postcode, city, appointmentRequestId, description, expirationDateTime, true);
    }
}
