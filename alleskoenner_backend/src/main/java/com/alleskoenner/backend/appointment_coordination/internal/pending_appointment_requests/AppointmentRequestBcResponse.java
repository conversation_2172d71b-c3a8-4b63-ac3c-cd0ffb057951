package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import com.fasterxml.jackson.annotation.JsonProperty;

public record AppointmentRequestBcResponse(
        @JsonProperty("@odata.etag")
        String odataEtag,
        String appointmentSystemId,
        String systemId,
        String no,
        String requestedDeliveryDatetime,
        String status,
        String customerNo,
        String salesOrderNo,
        String requestedHandyman,
        String confirmedHandyman,
        boolean asSoonAsPossible,
        String taskDescription
) {
}
