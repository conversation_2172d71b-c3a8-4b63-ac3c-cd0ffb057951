package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_escalation;

import java.util.Arrays;

import lombok.Getter;

/**
 * Enum representing the possible statuses of an appointment request.
 */
@Getter public enum AppointmentRequestStatus {
    REJECTED("Rejected"),
    TIMED_OUT("Timed Out"),
    TIMED_OUT_ESCAPED("Timed_x0020_Out"),
    ESCALATED("Escalated");

    private final String value;

    AppointmentRequestStatus(String value) {
        this.value = value;
    }

    public static AppointmentRequestStatus from(String string) {
        return Arrays.stream(values())
                .filter(status -> status.value.equals(string))
                .findFirst()
                .orElse(null);
    }
}