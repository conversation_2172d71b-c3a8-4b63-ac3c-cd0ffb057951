package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import com.alleskoenner.backend.emails.AppointmentConfirmationEmailServiceInterface;
import com.alleskoenner.backend.emails.AsapAppointmentConfirmationEmailValues;
import com.alleskoenner.backend.emails.ScheduledAppointmentConfirmationEmailValues;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@Service
@AllArgsConstructor
public class AppointmentConfirmationEmailSender {

    private final AppointmentConfirmationEmailServiceInterface appointmentConfirmationEmailServiceInterface;

    public void sendAppointmentConfirmationEmail(SyncToHandymanCompanyBcResponseValue response) {

        if (response.asSoonAsPossible()) {
            sendAsapAppointmentConfirmationEmail(response).subscribeOn(Schedulers.boundedElastic()).subscribe();
        } else {
            sendScheduledAppointmentConfirmationEmail(response).subscribeOn(Schedulers.boundedElastic()).subscribe();
        }
    }

    @NotNull
    private Mono<Void> sendAsapAppointmentConfirmationEmail(SyncToHandymanCompanyBcResponseValue response) {
        var values = new AsapAppointmentConfirmationEmailValues(
                response.salesOrderNo(),
                response.shipToName(),
                response.shipToAddress(),
                response.shipToCity(),
                response.shipToPostCode(),
                response.customerEMail(),
                response.workDescription()
        );
        return appointmentConfirmationEmailServiceInterface
                .sendAsapAppointmentConfirmationEmail(values)
                .doOnSuccess(emailSendResult -> log.debug("Successfully sent asap appointment confirmation email with Id {}.", emailSendResult.getId()))
                .doOnError(throwable -> log.error("Error while sending asap appointment confirmation email with message: {}", throwable.getMessage(), throwable))
                .then();
    }

    @NotNull
    private Mono<Void> sendScheduledAppointmentConfirmationEmail(SyncToHandymanCompanyBcResponseValue response) {
        var appointmentTime = response.requestedDeliveryDatetime()
                .withZoneSameInstant(ZoneId.of("Europe/Berlin"));
        var weekday = appointmentTime.format(DateTimeFormatter.ofPattern("EEEE", Locale.GERMAN));
        var date = appointmentTime.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"));
        var time = appointmentTime.format(DateTimeFormatter.ofPattern("HH:mm"));

        var values = new ScheduledAppointmentConfirmationEmailValues(
                response.salesOrderNo(),
                response.shipToName(),
                response.shipToAddress(),
                response.shipToCity(),
                response.shipToPostCode(),
                response.customerEMail(),
                response.workDescription(),
                weekday,
                date,
                time
        );
        return appointmentConfirmationEmailServiceInterface
                .sendScheduledAppointmentConfirmationEmail(values)
                .doOnSuccess(emailSendResult -> log.debug("Successfully sent scheduled appointment confirmation email with Id {}.", emailSendResult.getId()))
                .doOnError(throwable -> log.error("Error while sending scheduled appointment confirmation email with message: {}", throwable.getMessage(), throwable))
                .then();
    }
}
