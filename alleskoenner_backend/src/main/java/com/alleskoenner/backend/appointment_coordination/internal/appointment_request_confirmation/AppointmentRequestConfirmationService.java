package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import java.util.function.Function;

import static com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation.AppointmentRequestStatus.PENDING;
import com.alleskoenner.backend.appointment_coordination.internal.calendar.CalendarBookingService;
import com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests.AppointmentRequestBcResponse;
import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import com.alleskoenner.backend.handyman_invoicing.CreateDefaultSalesOrderLinesServiceInterface;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@RequiredArgsConstructor
@Service
@Slf4j
public class AppointmentRequestConfirmationService {

    final AppointmentRequestConfirmationClient confirmationClient;
    final AppointmentRequestArchivingClient archivingClient;
    final AppointmentConfirmationEmailSender emailSender;
    final AppointmentRequestGettingClient gettingClient;
    final SyncToHandymanCompanyClient syncToHandymanCompanyClient;
    final CreateDefaultSalesOrderLinesServiceInterface createDefaultSalesOrderLinesService;
    final ErrorNotificationEmailServiceInterface errorNotificationEmailService;
    final CalendarBookingService calendarBookingService;

    public Mono<Void> confirmAndArchive(LoginEmail loginEmail, String appointmentRequestId) {

        return gettingClient.getAppointmentRequest(appointmentRequestId)

                .flatMap(checkIfAppointmentRequestIsPending())

//                TODO: check if the handyman is still available
//                .flatMap(checkIfHandymanIsAvailable())

                .flatMap(appointmentRequest -> confirmAppointmentRequestInMainInstance(loginEmail, appointmentRequest)
                        .thenReturn(appointmentRequest))

                .then(syncToHandymanCompany(appointmentRequestId))

                .flatMap(syncResponse -> createDefaultSalesOrderLines(syncResponse)
                        .thenReturn(syncResponse))

                .flatMap(syncResponse -> bookEarliestAvailableTimeslot(loginEmail, syncResponse)
                        .thenReturn(syncResponse))

//                TODO: return appointment start time and use it in confirmation email

                .doOnSuccess(emailSender::sendAppointmentConfirmationEmail)

                .then(archivingClient.archiveAppointmentRequest(appointmentRequestId)
                        .onErrorResume(e -> {
                            errorNotificationEmailService.sendErrorNotificationEmail(e);
                            log.error(e.getMessage(), e);
                            return Mono.empty();
                        })
                )
                .doOnError(e -> {
                    errorNotificationEmailService.sendErrorNotificationEmail(e);
                    log.error(e.getMessage(), e);
                })
                .then();
    }

    @NotNull
    private static Function<AppointmentRequestBcResponse, Mono<? extends AppointmentRequestBcResponse>> checkIfAppointmentRequestIsPending() {
        return appointmentRequest -> {
            if (!PENDING.getValue().equals(appointmentRequest.status())) {
                return Mono.error(new UserFacingException(HttpStatus.GONE, "Appointment Request is no longer open"));
            } else {
                return Mono.just(appointmentRequest);
            }
        };
    }

    private Mono<Void> confirmAppointmentRequestInMainInstance(LoginEmail loginEmail, AppointmentRequestBcResponse appointmentRequest) {
        return confirmationClient.confirmAppointmentRequest(loginEmail, appointmentRequest.systemId(), appointmentRequest.odataEtag());
    }

    private Mono<SyncToHandymanCompanyBcResponseValue> syncToHandymanCompany(String appointmentRequestId) {
        return syncToHandymanCompanyClient.syncSalesOrderAndCustomerToHandymanCompany(appointmentRequestId);
    }

    private Mono<Void> createDefaultSalesOrderLines(SyncToHandymanCompanyBcResponseValue syncResponse1) {
        return createDefaultSalesOrderLinesService.createDefaultSalesOrderLines(syncResponse1.companyId(), syncResponse1.handymanSalesOrderId());
    }

    private Mono<Void> bookEarliestAvailableTimeslot(LoginEmail loginEmail, SyncToHandymanCompanyBcResponseValue syncResponse) {
        return calendarBookingService.findEarliestMatchingTimeSlotAndBookAppointment(loginEmail, syncResponse);
    }
}
