package com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.dtos;


public record HandymanAppointmentQueryResponse(
        String systemCreatedAt,
        String requestedHandymanAppointmentNo,
        String handymenState,
        String requestedHandymenSystemId,
        boolean asSoonAsPossible,
        String confirmedHandyman,
        String customerNo,
        String handymanOrderNo,
        String requestedDeliveryDatetime,
        String requestedHandymanSkill,
        String appointmentStatus,
        String appointmentSystemId,
        String taskDescription,
        String salesOrderNo,
        String documentType,
        String salesOrderSystemId,
        String addressLine1,
        String addressLine2,
        String city,
        String postCode
) {}
