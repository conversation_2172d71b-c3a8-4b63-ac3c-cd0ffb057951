package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.alleskoenner.backend.appointment_coordination.internal.business_central.AppointmentCoordinationBcConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AppointmentRequestConfirmationUrlBuilder {

    private final AppointmentCoordinationBcConfig config;

    public AppointmentRequestConfirmationUrlBuilder(AppointmentCoordinationBcConfig config) {
        this.config = config;
    }

    String buildUrl(String appointmentRequestId) {
        return String.format("v2.0/%s/api/ITV/appointments/v2.0/companies(%s)/appointmentRequests(%s)", config.getBcEnvironment(), config.getBcCompanyId(), appointmentRequestId);
    }

}
