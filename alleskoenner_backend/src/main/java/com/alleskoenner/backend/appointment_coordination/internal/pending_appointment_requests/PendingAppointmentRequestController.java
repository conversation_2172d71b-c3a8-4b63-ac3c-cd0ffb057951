package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;


@CrossOrigin(value = {"http://localhost:4000/", "http://localhost:4173/", "capacitor://localhost"})
@RestController
@RequestMapping("/appointment-request")
@AllArgsConstructor
public class PendingAppointmentRequestController {

    private final PendingAppointmentRequestService appointmentRequestService;

    @GetMapping(path = "/pending", produces = "application/json")
    public Mono<List<AppointmentRequest>> getPendingAppointmentRequestsForHandyman(@AuthenticationPrincipal JwtAuthenticationToken principal) {
        return appointmentRequestService.getPendingAppointmentRequestsForHandyman(new LoginEmail(principal.getToken().getClaim("email")));
    }
}
