package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests.AppointmentRequestBcResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class AppointmentRequestGettingClient {

    private final WebClient appointmentCoordinationBusinessCentralWebClient;
    private final AppointmentRequestGettingUrlBuilder urlBuilder;

    public Mono<AppointmentRequestBcResponse> getAppointmentRequest(String appointmentRequestId) {

        return appointmentCoordinationBusinessCentralWebClient.get()
                .uri(urlBuilder.buildUrl(appointmentRequestId))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(AppointmentRequestBcResponse.class);
                    } else {
                        return mapResponseBodyToError(response, appointmentRequestId);
                    }
                });
    }

    Mono<AppointmentRequestBcResponse> mapResponseBodyToError(ClientResponse response, String appointmentRequestId) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(new WebClientResponseException(
                                "Could not find Appointment Request with id " + appointmentRequestId,
                                response.statusCode().value(),
                                response.statusCode().toString(),
                                null, errorBody.getBytes(),
                                null
                        ))
                );
    }
}
