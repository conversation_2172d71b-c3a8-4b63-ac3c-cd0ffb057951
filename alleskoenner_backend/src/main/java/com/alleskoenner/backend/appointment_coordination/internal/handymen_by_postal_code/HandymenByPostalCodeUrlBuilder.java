package com.alleskoenner.backend.appointment_coordination.internal.handymen_by_postal_code;

import com.alleskoenner.backend.appointment_coordination.internal.business_central.AppointmentCoordinationBcConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HandymenByPostalCodeUrlBuilder {

    private final AppointmentCoordinationBcConfig config;

    public HandymenByPostalCodeUrlBuilder(AppointmentCoordinationBcConfig config) {
        this.config = config;
    }

    String buildHandymanByPostalCodeQueryUrl(String postalCode) {
        return "v2.0/" + config.getBcEnvironment() + "/api/ITV/handyman/v2.0/companies(" + config.getBcCompanyId() + ")/handymanQuery?$filter=postCode eq '" + postalCode + "' And active eq true";
    }
}
