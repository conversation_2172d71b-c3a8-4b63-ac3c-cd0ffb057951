package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import java.time.ZonedDateTime;
import java.util.Optional;
import com.alleskoenner.backend.appointment_coordination.internal.MissingAppointmentRequestedDateTime;
import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class StartAndEndTimeCalculator {

    private final AppointmentCoordinationConfig config;

    record StartAndEndDateTime(ZonedDateTime startDateTimeIncludingTravelTime, ZonedDateTime endDateTimeWithFixedAppointmentDuration1) {
    }

    @NotNull StartAndEndDateTime getStartAndEndDateTime(Optional<ZonedDateTime> requestedDateTime, boolean isAsSoonAsPossible) {
        final var calendar = config.getCalendar();
        final var fixedDurationOfAppointmentInMinutes = calendar.getFixedDurationOfAppointmentInMinutes();

        if (isAsSoonAsPossible) {
            final var now = ZonedDateTime.now();
            return new StartAndEndDateTime(
                    now,
                    now.plus(calendar.getDefaultAsapWindow()).plusMinutes(fixedDurationOfAppointmentInMinutes)
            );
        } else {
            final var requested = requestedDateTime.orElseThrow(MissingAppointmentRequestedDateTime::new);
            return new StartAndEndDateTime(
                    requested.minusMinutes(calendar.getTravelTimeToCustomerInMinutes()),
                    requested.plusMinutes(fixedDurationOfAppointmentInMinutes)
            );
        }
    }
}
