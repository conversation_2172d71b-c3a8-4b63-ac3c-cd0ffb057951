package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class AppointmentRequestArchivingClient {

    private final WebClient appointmentCoordinationBusinessCentralWebClient;
    private final AppointmentRequestArchivingUrlBuilder urlBuilder;

    public Mono<Void> archiveAppointmentRequest(String appointmentRequestId) {

        record AppointmentRequestArchivingBody(String appointmentSystemID) {
        }

        return appointmentCoordinationBusinessCentralWebClient.post()
                .uri(urlBuilder.buildUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(new AppointmentRequestArchivingBody(appointmentRequestId))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return Mono.empty();
                    } else {
                        return mapResponseBodyToError(response, appointmentRequestId);
                    }
                });
    }

    private static Mono<Void> mapResponseBodyToError(ClientResponse response, String appointmentRequestId) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(new WebClientResponseException(
                        String.format("Archiving appointment requests %s failed with status code %s and body %s", appointmentRequestId, response.statusCode(), errorBody),
                        response.statusCode().value(),
                        response.statusCode().toString(), null, errorBody.getBytes(), null
                )));
    }

}
