package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDate;
import java.time.ZonedDateTime;

public record SyncToHandymanCompanyBcResponseValue(
        @JsonProperty("@odata.context")
        String odataContext,
        @JsonProperty("@odata.etag")
        String odataEtag,
        String customerID,
        String customerName,
        String customerNo,
        String salesOrderID,
        String salesOrderNo,
        String customerAddress,
        String customerCity,
        String customerAddress2,
        String customerEMail,
        String customerPhoneNo,
        String customerPostCode,
        String customerPostingGroup,
        String shipToName,
        String shipToAddress,
        String shipToAddress2,
        String shipToCity,
        String shipToPostCode,
        String shipToPhoneNo,
        String billToName,
        String billToAddress,
        String billToAddress2,
        String billToCity,
        String billToPostCode,
        String companyId,
        String handymanSalesOrderId,
        String genBusPostingGroup,
        String workDescription,
        LocalDate postingDate,
        LocalDate documentDate,
        String type,
        boolean isConditionsChecked,
        boolean asSoonAsPossible,
        ZonedDateTime requestedDeliveryDatetime
) {
}