package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import com.microsoft.graph.models.Attendee;
import com.microsoft.graph.models.BodyType;
import com.microsoft.graph.models.DateTimeTimeZone;
import com.microsoft.graph.models.EmailAddress;
import com.microsoft.graph.models.Event;
import com.microsoft.graph.models.ItemBody;
import com.microsoft.graph.models.Location;
import com.microsoft.graph.models.LocationType;
import com.microsoft.graph.models.PhysicalAddress;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class AppointmentCreator {
    private final GraphClient graphClient;

    public Mono<Void> bookAppointmentFor(String handymanEmail, ZonedDateTime timeSlotStart, Duration duration, AppointmentInformation appointmentInfo) {
        return Mono.fromRunnable(() -> {
            var event = new Event();

            event.setSubject(appointmentInfo.title());

            var startDateTimeZone = new DateTimeTimeZone();
            startDateTimeZone.setDateTime(timeSlotStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")));
            startDateTimeZone.setTimeZone(timeSlotStart.getZone().getId());
            event.setStart(startDateTimeZone);

            var endDateTimeZone = new DateTimeTimeZone();
            endDateTimeZone.setDateTime(timeSlotStart.plus(duration).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")));
            endDateTimeZone.setTimeZone(timeSlotStart.getZone().getId());
            event.setEnd(endDateTimeZone);

            event.setLocation(getLocation(appointmentInfo));

            var body = new ItemBody();
            body.setContent(getContent(appointmentInfo));
            body.setContentType(BodyType.Text);
            event.setBody(body);

            var emailAddress = new EmailAddress();
            emailAddress.setAddress(handymanEmail);
            var attendee = new Attendee();
            attendee.setEmailAddress(emailAddress);
            event.setAttendees(List.of(attendee));

            graphClient
                    .getClient()
                    .users()
                    .byUserId(handymanEmail)
                    .calendar()
                    .events()
                    .post(event);
            log.info("Successfully created a calendar appointment for handyman: {} at {} for {} minutes", handymanEmail, timeSlotStart, duration.toMinutes());
        });
    }

    @NotNull
    private static Location getLocation(AppointmentInformation appointmentInfo) {
        var location = new Location();
        location.setDisplayName(appointmentInfo.address() + ", " + appointmentInfo.postalCode() + " " + appointmentInfo.city());
        var physicalAddress = new PhysicalAddress();
        physicalAddress.setStreet(appointmentInfo.address());
        physicalAddress.setPostalCode(appointmentInfo.postalCode());
        physicalAddress.setCity(appointmentInfo.city());
        location.setAddress(physicalAddress);
        location.setLocationType(LocationType.StreetAddress);
        return location;
    }

    private static String getContent(AppointmentInformation appointmentInfo) {
        return appointmentInfo.description() + "\n\n"
                + "Auftragsnummer: " + appointmentInfo.orderNumber() + "\n\n"
                + "Name des Kunden: " + appointmentInfo.customerName() + "\n\n"
                + "Telefon: " + appointmentInfo.customerPhone() + "\n\n"
                + "E-Mail: " + appointmentInfo.customerEmail();
    }
}
