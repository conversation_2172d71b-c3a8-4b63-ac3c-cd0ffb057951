package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.alleskoenner.backend.appointment_coordination.internal.business_central.AppointmentCoordinationBcConfig;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class AppointmentRequestGettingUrlBuilder {
    private final AppointmentCoordinationBcConfig config;

    String buildUrl(String appointmentRequestId) {
        return String.format("v2.0/%s/api/ITV/appointments/v2.0/companies(%s)/appointmentRequests(%s)", config.getBcEnvironment(), config.getBcCompanyId(), appointmentRequestId);
    }
}
