package com.alleskoenner.backend.appointment_coordination.internal.appointment_request_confirmation;

import com.alleskoenner.backend.appointment_coordination.internal.business_central.AppointmentCoordinationBcConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SyncToHandymanCompanyUrlBuilder {

    private final AppointmentCoordinationBcConfig config;

    public String buildUrl(String appointmentRequestId) {
        return String.format("v2.0/%s/api/ITV/appointments/v2.0/companies(%s)/appointmentRequests(%s)/Microsoft.NAV.sendOrderToHandyman", config.getBcEnvironment(), config.getBcCompanyId(), appointmentRequestId);
    }
}
