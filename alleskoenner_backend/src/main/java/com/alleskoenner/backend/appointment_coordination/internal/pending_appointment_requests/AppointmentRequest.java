package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import java.time.ZonedDateTime;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode
@ToString
@AllArgsConstructor
public abstract class AppointmentRequest {
    public final String addressLine1;
    public final String addressLine2;
    public final String postcode;
    public final String city;
    public final String appointmentRequestId;
    public final String description;
    public final ZonedDateTime expirationDateTime;
    public final Boolean isAsap;
}
