package com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query;

import com.alleskoenner.backend.appointment_coordination.internal.business_central.AppointmentCoordinationBcConfig;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class HandymanAppointmentQueryUrlBuilder {
    private final AppointmentCoordinationBcConfig config;

    String buildUrl(LoginEmail loginEmail, String appointmentRequestId) {
        return String.format("v2.0/%s/api/ITV/handyman/v2.0/companies(%s)/handymanAppointmentQuery?$filter=loginEmail eq '%s' And appointmentSystemId eq %s", config.getBcEnvironment(), config.getBcCompanyId(), loginEmail, appointmentRequestId);
    }
}
