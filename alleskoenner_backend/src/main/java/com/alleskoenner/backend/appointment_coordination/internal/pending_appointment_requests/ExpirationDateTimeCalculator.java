package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import java.time.Duration;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.dtos.HandymanAppointmentQueryResponse;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class ExpirationDateTimeCalculator {

    private final AppointmentCoordinationConfig config;

    @NotNull
    ZonedDateTime calculateExpiredDateTime(HandymanAppointmentQueryResponse appointmentRequest) {
        var createdAt = ZonedDateTime.parse(appointmentRequest.systemCreatedAt());
        var createdTime = createdAt.toLocalTime();

        var acceptanceWindow = config.getAppointmentRequestAcceptanceTimeoutWindow();
        var quietStart = config.getQuietHours().getStart();
        var quietEnd = config.getQuietHours().getEnd();

        if (isExpiringWithinQuietHours(createdTime, quietStart, quietEnd, acceptanceWindow)) {
            var isEarlyMorning = isInEarlyMorning(createdTime, quietEnd);
            var quietEndToday = createdAt.with(quietEnd).withSecond(0).withNano(0);

            if (isEarlyMorning) {
                return quietEndToday.plus(acceptanceWindow);
            } else {
                return quietEndToday.plusDays(1).plus(acceptanceWindow);
            }
        }

        return createdAt.plus(acceptanceWindow).withSecond(0).withNano(0);
    }

    private boolean isExpiringWithinQuietHours(LocalTime createdTime, LocalTime quietStart, LocalTime quietEnd, Duration acceptanceWindow) {
        var expirationStartThreshold = quietStart.minus(acceptanceWindow);
        return createdTime.isAfter(expirationStartThreshold.minusMinutes(1)) || createdTime.isBefore(quietEnd);
    }

    private boolean isInEarlyMorning(LocalTime time, LocalTime quietEnd) {
        return time.isAfter(LocalTime.parse("00:00")) && time.isBefore(quietEnd);
    }
}
