package com.alleskoenner.backend.appointment_coordination.internal.calendar;

import com.alleskoenner.backend.appointment_coordination.internal.config.AppointmentCoordinationConfig;
import com.microsoft.graph.models.ScheduleInformation;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.List;

@Service
@AllArgsConstructor
public class AvailabilityEvaluator {

    private final AppointmentCoordinationConfig config;

    private static final char FREE_AVAILABILITY = '0';

    List<String> getAvailableHandymenEmailsFromResponse(List<ScheduleInformation> schedules) {
        return schedules.stream()
                .filter(this::isScheduleFree)
                .map(ScheduleInformation::getScheduleId)
                .toList();
    }

    private boolean isScheduleFree(ScheduleInformation scheduleInformationOfSingleHandyman) {
        final var doesUserExist = scheduleInformationOfSingleHandyman.getAvailabilityView() != null;
        var requiredTimeSlotAvailabilityViewString = String.valueOf(FREE_AVAILABILITY).repeat(calculateNumberOfRequiredTimeslots());
        return doesUserExist && scheduleInformationOfSingleHandyman.getAvailabilityView().contains(requiredTimeSlotAvailabilityViewString);
    }

    private int calculateNumberOfRequiredTimeslots() {
        var travelTime = config.getCalendar().getTravelTimeToCustomerInMinutes();
        var appointmentDuration = config.getCalendar().getFixedDurationOfAppointmentInMinutes();
        var intervalDuration = config.getCalendar().getAvailabilityViewIntervalInMinutes();
        return (travelTime + appointmentDuration) / intervalDuration;
    }

    /**
     * Returns the start time of the first available slot (block of '0's) of the required length in the availability view.
     *
     * @param scheduleInfo  the schedule information of the handyman
     * @param windowStart   the start of the time window to search for available slots
     * @param totalDuration the duration of the appointment including travel time
     * @return the start time of the first available slot or null if no slot is available
     */
    public ZonedDateTime getFirstAvailableTimeSlot(ScheduleInformation scheduleInfo, ZonedDateTime windowStart, Duration totalDuration) {
        final var availabilityView = scheduleInfo.getAvailabilityView();
        if (availabilityView == null) {
            return null;
        }
        final var requiredIntervals = (int) totalDuration.dividedBy(Duration.ofMinutes(config.getCalendar().getAvailabilityViewIntervalInMinutes()));
        final var requiredTimeSlotAvailabilityViewString = String.valueOf(FREE_AVAILABILITY).repeat(requiredIntervals);
        final var numberOfBusyIntervals = availabilityView.indexOf(requiredTimeSlotAvailabilityViewString);
        if (numberOfBusyIntervals == -1) {
            return null;
        }
        final var intervalDuration = config.getCalendar().getAvailabilityViewIntervalInMinutes();
        return windowStart
                .plusMinutes((long) numberOfBusyIntervals * intervalDuration);
    }
}
