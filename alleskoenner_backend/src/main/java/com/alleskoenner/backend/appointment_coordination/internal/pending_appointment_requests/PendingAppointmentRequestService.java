package com.alleskoenner.backend.appointment_coordination.internal.pending_appointment_requests;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.function.Predicate;

import com.alleskoenner.backend.appointment_coordination.internal.handyman_appointment_query.dtos.HandymanAppointmentQueryResponse;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@RequiredArgsConstructor
@Service
public class PendingAppointmentRequestService {

    final PendingAppointmentRequestClient pendingAppointmentRequestClient;
    final ExpirationDateTimeCalculator expirationDateTimeCalculator;

    public Mono<List<AppointmentRequest>> getPendingAppointmentRequestsForHandyman(LoginEmail handymanLoginMail) {
        return pendingAppointmentRequestClient.fetchPendingAppointmentRequests(handymanLoginMail)
                .map(appointmentRequests -> appointmentRequests.stream()
                        .filter(byNotExpired())
                        .map(appointmentRequest -> {
                            final var expirationDateTime = expirationDateTimeCalculator.calculateExpiredDateTime(appointmentRequest);
                            if (appointmentRequest.asSoonAsPossible()) {
                                return new AsapAppointmentRequest(
                                        appointmentRequest.addressLine1(),
                                        appointmentRequest.addressLine2(),
                                        appointmentRequest.postCode(),
                                        appointmentRequest.city(),
                                        appointmentRequest.appointmentSystemId(),
                                        appointmentRequest.taskDescription(),
                                        expirationDateTime
                                );
                            } else {
                                return new ScheduledAppointmentRequest(
                                        appointmentRequest.addressLine1(),
                                        appointmentRequest.addressLine2(),
                                        appointmentRequest.postCode(),
                                        appointmentRequest.city(),
                                        appointmentRequest.appointmentSystemId(),
                                        appointmentRequest.taskDescription(),
                                        expirationDateTime,
                                        ZonedDateTime.parse(appointmentRequest.requestedDeliveryDatetime()));
                            }
                        }).toList());
    }

    @NotNull
    private Predicate<HandymanAppointmentQueryResponse> byNotExpired() {
        return appointmentRequest ->
                expirationDateTimeCalculator.calculateExpiredDateTime(appointmentRequest).isAfter(ZonedDateTime.now());
    }
}
