package com.alleskoenner.backend.appointment_coordination.internal.appointment_coordination;

/**
 * When a business event occurs, that needs to be visible to business stakeholder (e.g. No handyman is currently available according to their calendar.  for a specific postal code),
 * this exception can be thrown and used to create a business notification mail.
 */
public class BusinessException extends RuntimeException {
    public BusinessException(String message) {
        super(message);
    }
}
