package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted;

import java.util.List;
import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.PostedSalesOrdersClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.dtos.PostedSalesOrderInformationItemResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@AllArgsConstructor
public class PostedHandymanOrdersService {

    private final PostedSalesOrdersClient postedSalesOrdersClient;
    private final HandymanCompanyIdFetcher companyIdFetcher;
    private final HandymanInvoicingConfig config;


    public Mono<List<PostedHandymanOrdersResponse>> getHandymanPostedOrders(LoginEmail loginEmail) {
        log.info("Fetching posted sales orders for handyman with email: {}", loginEmail.email());

        return companyIdFetcher.getHandymanCompanyIdByLoginEmail(loginEmail)
                .flatMap(companyId -> postedSalesOrdersClient.fetchPostedSalesOrders(loginEmail, companyId))
                .doOnSuccess(items -> log.info("Successfully fetched {} posted sales orders for handyman with email: {}",
                        items.size(), loginEmail.email()))
                .doOnError(error -> log.error("Error fetching posted sales orders for handyman with email: {}",
                        loginEmail.email(), error))
                .map(this::toHandymanOrders);
    }

    @NotNull
    private List<PostedHandymanOrdersResponse> toHandymanOrders(List<PostedSalesOrderInformationItemResponse> salesOrderInformationItemResponses) {
        return salesOrderInformationItemResponses.stream().map(item -> {
                    // Todo: Handyman will see the expiration date as the date of the posted order.
                    //  This is not exactly accurate. Later on we would need to show the exact time of the appointment.
                    return PostedHandymanOrdersResponse.from(item, config.getDefaultAsapWindow());
                }
        ).toList();
    }

}
