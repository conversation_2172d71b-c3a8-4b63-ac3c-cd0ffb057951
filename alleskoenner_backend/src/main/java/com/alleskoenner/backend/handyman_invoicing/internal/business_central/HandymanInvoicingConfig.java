package com.alleskoenner.backend.handyman_invoicing.internal.business_central;

import java.math.BigDecimal;
import java.time.Duration;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "handyman-invoicing")
public class HandymanInvoicingConfig {

    private Duration defaultAsapWindow;
    private BusinessCentral businessCentral;
    private DefaultSalesOrderLines defaultSalesOrderLines;
    private boolean salesOrderPostingEnabled = false;

    @Getter
    @Setter
    public static class BusinessCentral {
        private String baseurl;
        private String microsoftLogin;
        private String bcTenantId;
        private String bcEnvironment;
        private String bcClientId;
        private String bcMainCompanyId;
        private String bcMainEnvironment;
    }

    @Getter
    @Setter
    public static class DefaultSalesOrderLines {
        private SalesOrderLine travelCharge;
        private SalesOrderLine defaultWorkingHour;
        private SalesOrderLine smallPartsCharge;

        @Getter
        @Setter
        public static class SalesOrderLine {
            private String lineType;
            private String lineObjectNumber;
            private BigDecimal quantity;
        }
    }
}
