package com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email;

import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@AllArgsConstructor
public class HandymanCompanyIdFetcher {

    private final HandymanCompanyIdFetcherCache handymanCompanyIdFetcherCache;

    public Mono<String> getHandymanCompanyIdByLoginEmail(LoginEmail loginEmail) {
        return handymanCompanyIdFetcherCache.getAllHandymen(loginEmail)
                .flatMap(handymen -> handymen.stream()
                        .filter(handyman -> loginEmail.equals(handyman.loginEmail()))
                        .findFirst()
                        .map(handyman -> Mono.just(handyman.companyID()))
                        .orElseGet(() -> Mono.error(new IllegalStateException("Kein Handyman mit LoginEmail " + loginEmail + " gefunden")))
                );
    }
}
