package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines_options.dtos;

import java.math.BigDecimal;

public record SalesOrderLineOptionDTO(
        String description,
        String lineObjectNumber,
        BigDecimal unitPrice,
        String type,
        Rules rules
) {
    public record Rules(
            BigDecimal minQuantity,
            BigDecimal stepSize,
            boolean deletable,
            boolean duplicateAllowed,
            boolean quantityEditable
    ) {
    }
}