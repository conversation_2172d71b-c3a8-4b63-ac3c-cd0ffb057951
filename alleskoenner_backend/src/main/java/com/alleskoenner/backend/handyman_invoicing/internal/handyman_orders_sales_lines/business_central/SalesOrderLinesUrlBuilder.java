package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SalesOrderLinesUrlBuilder {

    private final HandymanInvoicingConfig config;

    public SalesOrderLinesUrlBuilder(HandymanInvoicingConfig config) {
        this.config = config;
    }

    String buildSalesOrderLinesUrl(String companyId, String salesOrderId) {
        return "v2.0/%s/%s/api/v2.0/companies(%s)/salesOrders(%s)/salesOrderLines"
                .formatted(
                        config.getBusinessCentral().getBcTenantId(),
                        config.getBusinessCentral().getBcEnvironment(),
                        companyId,
                        salesOrderId
                );
    }

    String buildSalesOrderLineUrl(String companyId, String salesOrderLineId) {
        return "v2.0/%s/%s/api/v2.0/companies(%s)/salesOrderLines(%s)"
                .formatted(
                        config.getBusinessCentral().getBcTenantId(),
                        config.getBusinessCentral().getBcEnvironment(),
                        companyId,
                        salesOrderLineId
                );
    }
}
