package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload.business_central;

import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class SignatureUploadClient {

    private final WebClient handymanInvoicingBcWebClient;
    private final SignatureUploadUrlBuilder signatureUploadUrlBuilder;

    public Mono<Void> uploadSignature(LoginEmail loginEmail, String companyId, String payload) {
        log.debug("Uploading signature to Business Central for handyman: {}", loginEmail.email());

        return handymanInvoicingBcWebClient.post()
                .uri(signatureUploadUrlBuilder.buildSignatureUploadUrl(companyId))
                .contentType(APPLICATION_JSON)
                .header("If-Match", "*")
                .bodyValue(payload)
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        log.debug("Successfully uploaded signature for handyman: {}", loginEmail.email());
                        return Mono.empty();
                    } else {
                        return mapResponseBodyToError(response, loginEmail);
                    }
                });
    }

    private Mono<Void> mapResponseBodyToError(ClientResponse response, LoginEmail loginEmail) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> {
                    log.error("Failed to upload signature for handyman: {} - Status: {}, Body: {}",
                            loginEmail.email(), response.statusCode(), errorBody);
                    return Mono.error(new BusinessCentralErrorResponseException(
                            "Error uploading signature for handyman with login Email " + loginEmail.email(),
                            response,
                            errorBody
                    ));
                });
    }
}
