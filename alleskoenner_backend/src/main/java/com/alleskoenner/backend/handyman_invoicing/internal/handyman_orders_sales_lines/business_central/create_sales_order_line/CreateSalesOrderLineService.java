package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.create_sales_order_line;

import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos.SalesOrderLineResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.SalesOrderLineCreateRequest;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class CreateSalesOrderLineService {
    private final CreateSalesOrderLineClient client;

    public Mono<SalesOrderLineResponse> createSalesOrderLine(
            String companyId,
            String salesOrderId,
            SalesOrderLineCreateRequest request) {
        return client.createSalesOrderLine(companyId, salesOrderId, request);
    }
}
