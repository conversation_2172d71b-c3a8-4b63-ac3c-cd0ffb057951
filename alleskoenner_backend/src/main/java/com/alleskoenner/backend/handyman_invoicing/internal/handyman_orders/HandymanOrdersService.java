package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders;

import java.time.ZonedDateTime;
import java.util.List;
import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.SalesOrderInformationClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos.SalesOrderInformationItemResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@AllArgsConstructor
public class HandymanOrdersService {

    private final SalesOrderInformationClient salesOrderInformationClient;
    private final HandymanCompanyIdFetcher companyIdFetcher;
    private final HandymanInvoicingConfig config;

    public Mono<List<HandymanOrdersResponse>> getHandymanOrders(LoginEmail loginEmail) {
        log.info("Fetching sales order information for handyman with email: {}", loginEmail.email());

        return companyIdFetcher.getHandymanCompanyIdByLoginEmail(loginEmail)
                .flatMap(companyId -> salesOrderInformationClient.fetchSalesOrderInformationList(loginEmail, companyId))
                .doOnSuccess(items -> log.info("Successfully fetched {} sales order information items for handyman with email: {}",
                        items.size(), loginEmail.email()))
                .doOnError(error -> log.error("Error fetching sales order information for handyman with email: {}",
                        loginEmail.email(), error))
                .map(this::toHandymanOrders);
    }

    @NotNull
    private List<HandymanOrdersResponse> toHandymanOrders(List<SalesOrderInformationItemResponse> salesOrderInformationItemResponses) {
        return salesOrderInformationItemResponses.stream().map(item -> {
                    final var expirationDateTime = ZonedDateTime.parse(item.systemCreatedAt())
                            .plus(config.getDefaultAsapWindow());
                    return HandymanOrdersResponse.from(item, expirationDateTime);
                }
        ).toList();
    }
}
