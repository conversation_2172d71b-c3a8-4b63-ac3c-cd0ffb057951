package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.PostedHandymanOrdersResponse;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingErrorMessage;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@CrossOrigin(value = {"http://localhost:4000/", "http://localhost:4173/", "capacitor://localhost"})
@RestController
@RequestMapping("/handyman-orders")
@AllArgsConstructor
public class HandymanOrdersPostingController {

    private final HandymanOrdersPostingService handymanOrdersPostingService;
    private final HandymanInvoicingConfig config;

    @PostMapping(value = "/{caseNumber}/post", produces = "application/json")
    public Mono<ResponseEntity<PostedHandymanOrdersResponse>> postSalesOrderInvoice(
            @AuthenticationPrincipal JwtAuthenticationToken principal,
            @PathVariable String caseNumber,
            @RequestBody @Validated HandymanOrdersPostingRequest handymanOrdersPostingRequest) {

        if (!config.isSalesOrderPostingEnabled()) {
            throw new UserFacingException(HttpStatus.FORBIDDEN, "Sales order posting is disabled");
        }

        var loginEmail = new LoginEmail(principal.getToken().getClaim("email"));

        return handymanOrdersPostingService.uploadSignatureToSalesOrder(loginEmail, handymanOrdersPostingRequest.customerSignature(), caseNumber)
                .then(handymanOrdersPostingService.postSalesOrderInvoice(loginEmail, handymanOrdersPostingRequest.salesOrderSystemId(), caseNumber))
                .map(handymanOrdersResponse -> ResponseEntity.ok()
                        .body(PostedHandymanOrdersResponse.from(handymanOrdersResponse, config.getDefaultAsapWindow())));
    }

    @ExceptionHandler(exception = UserFacingException.class, produces = "application/json")
    public ResponseEntity<UserFacingErrorMessage> handleError(UserFacingException exc) {
        return ResponseEntity.status(exc.getStatusCode()).body(new UserFacingErrorMessage(exc.getMessage()));
    }
}
