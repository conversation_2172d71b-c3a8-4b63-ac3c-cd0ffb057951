package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.create_sales_order_line;

import java.util.List;
import com.alleskoenner.backend.handyman_invoicing.CreateDefaultSalesOrderLinesServiceInterface;
import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.SalesOrderLineCreateRequest;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class CreateDefaultSalesOrderLinesService implements CreateDefaultSalesOrderLinesServiceInterface {
    private final CreateSalesOrderLineClient client;
    private final HandymanInvoicingConfig configuration;

    @Override
    public Mono<Void> createDefaultSalesOrderLines(String companyId, String salesOrderId) {

        var defaultSalesOrderLines = configuration.getDefaultSalesOrderLines();

        SalesOrderLineCreateRequest travelCharge = new SalesOrderLineCreateRequest(
                defaultSalesOrderLines.getTravelCharge().getLineType(),
                defaultSalesOrderLines.getTravelCharge().getLineObjectNumber(),
                defaultSalesOrderLines.getTravelCharge().getQuantity()
        );

        SalesOrderLineCreateRequest defaultWorkingHour = new SalesOrderLineCreateRequest(
                defaultSalesOrderLines.getDefaultWorkingHour().getLineType(),
                defaultSalesOrderLines.getDefaultWorkingHour().getLineObjectNumber(),
                defaultSalesOrderLines.getDefaultWorkingHour().getQuantity()
        );

        SalesOrderLineCreateRequest smallPartsCharge = new SalesOrderLineCreateRequest(
                defaultSalesOrderLines.getSmallPartsCharge().getLineType(),
                defaultSalesOrderLines.getSmallPartsCharge().getLineObjectNumber(),
                defaultSalesOrderLines.getSmallPartsCharge().getQuantity()
        );

        return Flux.fromIterable(List.of(travelCharge, defaultWorkingHour, smallPartsCharge))
                .concatMap(request -> client.createSalesOrderLine(companyId, salesOrderId, request))
                .then();
    }
}
