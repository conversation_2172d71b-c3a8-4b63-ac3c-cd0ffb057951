package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting;

import java.util.function.Consumer;

import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.PostedSalesOrdersClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.dtos.PostedSalesOrderInformationItemResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.business_central.SalesOrderPostingClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload.SignatureHashCalculator;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload.SignaturePayloadBuilder;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload.business_central.SignatureUploadClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@AllArgsConstructor
public class HandymanOrdersPostingService {

    private final SalesOrderPostingClient salesOrderPostingClient;
    private final PostedSalesOrdersClient postedSalesOrdersClient;
    private final InvoiceEmailSender invoiceEmailSender;
    private final HandymanCompanyIdFetcher companyIdFetcher;
    private final ErrorNotificationEmailServiceInterface errorNotificationEmailService;
    private final SignatureHashCalculator signatureHashCalculator;
    private final SignaturePayloadBuilder signaturePayloadBuilder;
    private final SignatureUploadClient signatureUploadClient;

    public Mono<Void> uploadSignatureToSalesOrder(LoginEmail loginEmail, String customerSignature, String caseNumber) {
        log.debug("Uploading signature for case number: {} by handyman: {}", caseNumber, loginEmail.email());

        return companyIdFetcher.getHandymanCompanyIdByLoginEmail(loginEmail)
                .flatMap(companyId -> {
                    try {
                        var signatureHash = signatureHashCalculator.calculateSha256Hash(customerSignature);
                        var payload = signaturePayloadBuilder.buildBase64Payload(caseNumber, customerSignature, signatureHash);

                        return signatureUploadClient.uploadSignature(loginEmail, companyId, payload);
                    } catch (Exception e) {
                        return Mono.error(new UserFacingException(HttpStatus.INTERNAL_SERVER_ERROR,
                                "Fehler beim Upload der Unterschrift. Bitte versuchen Sie es erneut."));
                    }
                })
                .doOnSuccess(_ -> log.debug("Successfully uploaded signature for case number: {} by handyman: {}",
                        caseNumber, loginEmail.email()))
                .doOnError(error -> {
                    if (!(error instanceof UserFacingException)) {
                        log.error("Error uploading signature for case number: {} by handyman: {}",
                                caseNumber, loginEmail.email(), error);
                        errorNotificationEmailService.sendErrorNotificationEmail(error);
                    }
                })
                .onErrorMap(error -> {
                    if (error instanceof UserFacingException) {
                        return error;
                    }
                    return new UserFacingException(HttpStatus.INTERNAL_SERVER_ERROR,
                            "Fehler beim Upload der Unterschrift. Bitte versuchen Sie es erneut.");
                });
    }

    public Mono<PostedSalesOrderInformationItemResponse> postSalesOrderInvoice(LoginEmail loginEmail, String salesOrderSystemId, String caseNumber) {
        log.debug("Posting sales order invoice for systemId: {} by handyman with email: {}", salesOrderSystemId, loginEmail.email());

        return companyIdFetcher.getHandymanCompanyIdByLoginEmail(loginEmail)

                .flatMap(companyId ->
                        salesOrderPostingClient.postSalesOrder(loginEmail, companyId, salesOrderSystemId)
                                .then(postedSalesOrdersClient.fetchPostedSalesOrder(loginEmail, companyId, caseNumber))
                )

                .doOnSuccess(unused -> log.info("Successfully posted sales order invoice for systemId: {} by handyman with email: {}",
                        salesOrderSystemId, loginEmail.email()))

                .doOnSuccess(trySendingInvoiceEmail(loginEmail, salesOrderSystemId))

                .doOnError(error -> {
                    errorNotificationEmailService.sendErrorNotificationEmail(error);
                    log.error("Error posting sales order invoice for systemId: {} by handyman with email: {}",
                            salesOrderSystemId, loginEmail.email(), error);
                });
    }


    private Consumer<PostedSalesOrderInformationItemResponse> trySendingInvoiceEmail(LoginEmail loginEmail, String salesOrderSystemId) {
        return postedSalesOrder -> {
            try {
                invoiceEmailSender.sendInvoiceEmail(postedSalesOrder, loginEmail, salesOrderSystemId);
            } catch (Exception e) {
                log.error("Unknown error while trying to send email: ", e);
            }
        };
    }
}
