package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.dtos;

import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonProperty;

public record PostedSalesOrderLineResponse(
        @JsonProperty("@odata.etag")
        String odataETag,
        String documentType,
        String documentNo,
        int lineNo,
        String systemId,
        String type,
        String no,
        String description,
        BigDecimal quantity,
        String unitOfMeasure,
        BigDecimal unitPrice,
        BigDecimal amount,
        String vatProdPostingGroup,
        BigDecimal vatPercent,
        BigDecimal vatAmount
) {
}
