package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.create_sales_order_line;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import org.springframework.stereotype.Component;

@Component
public class CreateSalesOrderLineUrlBuilder {
    private final HandymanInvoicingConfig config;

    public CreateSalesOrderLineUrlBuilder(HandymanInvoicingConfig config) {
        this.config = config;
    }

    public String buildSalesOrderLinesUrl(String companyId, String salesOrderId) {
        return "v2.0/%s/%s/api/v2.0/companies(%s)/salesOrders(%s)/salesOrderLines"
                .formatted(
                        config.getBusinessCentral().getBcTenantId(),
                        config.getBusinessCentral().getBcEnvironment(),
                        companyId,
                        salesOrderId
                );
    }
}
