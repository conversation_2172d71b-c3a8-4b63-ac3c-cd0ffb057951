package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting;

import com.alleskoenner.backend.emails.InvoiceEmailServiceInterface;
import com.alleskoenner.backend.emails.InvoiceEmailValues;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download.HandymanOrdersPdfDownloadService;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.dtos.PostedSalesOrderInformationItemResponse;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@AllArgsConstructor
public class InvoiceEmailSender {

    private final HandymanOrdersPdfDownloadService pdfDownloadService;
    private final InvoiceEmailServiceInterface invoiceEmailService;

    public void sendInvoiceEmail(PostedSalesOrderInformationItemResponse postedSalesOrder, LoginEmail loginEmail, String salesOrderSystemId) {

        pdfDownloadService.downloadPdf(loginEmail, postedSalesOrder.systemId())
                .flatMap(pdfDataFlux -> convertPdfDataToByteArray(pdfDataFlux)
                        .flatMap(pdfData -> {
                            var invoiceEmailValues = createInvoiceEmailValues(postedSalesOrder, pdfData);
                            return invoiceEmailService.sendInvoiceEmail(invoiceEmailValues);
                        }))
                .doOnSuccess(unused -> log.info("Invoice email sent successfully for sales order: {}", postedSalesOrder.orderNo()))
                // todo: add error notification
                .doOnError(error -> log.error("Error sending invoice email for posted sales order: {}", postedSalesOrder, error))
                .subscribe();
    }

    private Mono<byte[]> convertPdfDataToByteArray(Flux<DataBuffer> dataBufferFlux) {
        return DataBufferUtils.join(dataBufferFlux)
                .map(dataBuffer -> {
                    byte[] bytes = new byte[dataBuffer.readableByteCount()];
                    dataBuffer.read(bytes);
                    DataBufferUtils.release(dataBuffer);
                    return bytes;
                });
    }

    private InvoiceEmailValues createInvoiceEmailValues(PostedSalesOrderInformationItemResponse postedSalesOrder, byte[] pdfData) {
        return new InvoiceEmailValues(
                postedSalesOrder.billToAddress(),
                postedSalesOrder.billToCity(),
                postedSalesOrder.billToName(),
                postedSalesOrder.billToPostCode(),
                postedSalesOrder.orderNo(),
                pdfData,
                postedSalesOrder.sellToEmail(),
                postedSalesOrder.shipToAddress(),
                postedSalesOrder.shipToCity(),
                postedSalesOrder.shipToName(),
                postedSalesOrder.shipToPostCode()
        );
    }
}
