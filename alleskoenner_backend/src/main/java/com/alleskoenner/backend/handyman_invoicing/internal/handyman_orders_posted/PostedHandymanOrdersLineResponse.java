package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted;

import java.math.BigDecimal;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.dtos.PostedSalesOrderLineResponse;

public record PostedHandymanOrdersLineResponse(
        BigDecimal amount,
        String description,
        String lineObjectNumber,
        BigDecimal quantity,
        String type,
        String unitOfMeasure,
        BigDecimal unitPrice,
        String systemId,
        BigDecimal vatAmount,
        BigDecimal vatPercent
) {
    public static PostedHandymanOrdersLineResponse from(PostedSalesOrderLineResponse salesOrderLine) {
        return new PostedHandymanOrdersLineResponse(
                salesOrderLine.amount(),
                salesOrderLine.description(),
                salesOrderLine.no(),
                salesOrderLine.quantity(),
                salesOrderLine.type(),
                salesOrderLine.unitOfMeasure(),
                salesOrderLine.unitPrice(),
                salesOrderLine.systemId(),
                salesOrderLine.vatAmount(),
                salesOrderLine.vatPercent());
    }
}
