package com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class HandymenBcClient {

    private final WebClient handymanInvoicingBcWebClient;
    private final HandymenUrlBuilder handymenUrlBuilder;

    public Mono<List<Handyman>> fetchHandymen() {

        return handymanInvoicingBcWebClient.get()
                .uri(handymenUrlBuilder.buildHandymanUrl())
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(HandymenResponse.class)
                                .map(HandymenResponse::value);
                    } else {
                        return mapResponseBodyToError(response);
                    }
                });
    }

    private static Mono<List<Handyman>> mapResponseBodyToError(ClientResponse response) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> {
                    log.error("Request failed with status code {} and body {}", response.statusCode(), errorBody);
                    return Mono.error(new WebClientResponseException(
                            "Request failed with status code " + response.statusCode(),
                            response.statusCode().value(),
                            response.statusCode().toString(), null, errorBody.getBytes(), null
                    ));
                });
    }
}
