package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central;

import java.util.List;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.dtos.PostedSalesOrderInformationItemResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.dtos.PostedSalesOrderInformationListContextResponse;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class PostedSalesOrdersClient {

    private final WebClient handymanInvoicingBcWebClient;
    private final PostedSalesOrdersUrlBuilder postedSalesOrdersUrlBuilder;

    @NotNull
    public Mono<List<PostedSalesOrderInformationItemResponse>> fetchPostedSalesOrders(LoginEmail loginEmail, String companyId) {
        return handymanInvoicingBcWebClient.get()
                .uri(postedSalesOrdersUrlBuilder.buildPostedSalesOrderRequestUrl(companyId))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(PostedSalesOrderInformationListContextResponse.class)
                                .map(PostedSalesOrderInformationListContextResponse::value);
                    } else {
                        return mapResponseBodyToError(response, loginEmail);
                    }
                });
    }

    Mono<List<PostedSalesOrderInformationItemResponse>> mapResponseBodyToError(ClientResponse response, LoginEmail loginEmail) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(
                        new BusinessCentralErrorResponseException(
                                "Error fetching posted sales orders for handyman with login Email " + loginEmail.email(), response, errorBody)));
    }

    @NotNull
    public Mono<PostedSalesOrderInformationItemResponse> fetchPostedSalesOrder(LoginEmail loginEmail, String companyId, String caseNumber) {
        return handymanInvoicingBcWebClient.get()
                .uri(postedSalesOrdersUrlBuilder.buildSinglePostedSalesOrderRequestUrl(companyId, caseNumber))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(PostedSalesOrderInformationListContextResponse.class)
                                .map(PostedSalesOrderInformationListContextResponse::value)
                                .flatMap(list -> {
                                    if (list.size() == 1) {
                                        return Mono.just(list.getFirst());
                                    } else {
                                        return Mono.error(new BusinessCentralErrorResponseException(
                                                "Expected exactly one posted sales order, but got " + list.size(),
                                                response, null));
                                    }
                                });
                    } else {
                        return mapSingleResponseBodyToError(response, loginEmail);
                    }
                });
    }

    Mono<PostedSalesOrderInformationItemResponse> mapSingleResponseBodyToError(ClientResponse response, LoginEmail loginEmail) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(
                        new BusinessCentralErrorResponseException(
                                "Error fetching posted sales orders for handyman with login Email " + loginEmail.email(), response, errorBody)));
    }
}