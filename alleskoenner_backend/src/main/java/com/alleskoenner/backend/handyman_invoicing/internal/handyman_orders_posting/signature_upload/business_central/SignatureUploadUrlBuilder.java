package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload.business_central;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SignatureUploadUrlBuilder {

    private final HandymanInvoicingConfig config;

    public String buildSignatureUploadUrl(String companyId) {
        var url = "v2.0/%s/%s/ODataV4/HandymanApi_UploadSignature?company=%s"
                .formatted(
                        config.getBusinessCentral().getBcTenantId(),
                        config.getBusinessCentral().getBcEnvironment(),
                        companyId
                );

        log.debug("Built signature upload URL for company: {}", companyId);
        return url;
    }
}
