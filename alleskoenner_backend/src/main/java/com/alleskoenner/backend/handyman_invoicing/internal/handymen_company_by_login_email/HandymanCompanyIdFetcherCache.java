package com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email;

import java.util.List;
import com.alleskoenner.backend.shared.LoginEmail;
import com.github.benmanes.caffeine.cache.AsyncCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class HandymanCompanyIdFetcherCache {

    private final HandymenBcClient handymanBcClient;
    private final AsyncCache<String, List<Handyman>> handymanAsyncCache;

    public Mono<List<Handyman>> getAllHandymen(LoginEmail loginEmail) {
        return Mono.fromFuture(
                handymanAsyncCache.get(loginEmail.email(), (key, executor) -> {
                    log.info("Fetching Handymen from source and caching");
                    return handymanBcClient.fetchHandymen()
                            .doOnSuccess(list -> log.info("Fetched {} Handymen", list.size()))
                            .doOnError(error -> log.error("Error fetching Handymen", error))
                            .toFuture();
                })
        );
    }
}
