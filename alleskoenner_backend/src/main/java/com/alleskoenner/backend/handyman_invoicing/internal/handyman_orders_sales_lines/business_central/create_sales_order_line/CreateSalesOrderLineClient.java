package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.create_sales_order_line;

import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos.SalesOrderLineResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.SalesOrderLineCreateRequest;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class CreateSalesOrderLineClient {
    private final WebClient handymanInvoicingBcWebClient;
    private final CreateSalesOrderLineUrlBuilder urlBuilder;

    public Mono<SalesOrderLineResponse> createSalesOrderLine(
            String companyId,
            String salesOrderId,
            SalesOrderLineCreateRequest request) {

        return handymanInvoicingBcWebClient.post()
                .uri(urlBuilder.buildSalesOrderLinesUrl(companyId, salesOrderId))
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(SalesOrderLineResponse.class);
                    } else {
                        return mapResponseBodyToError(response, companyId, "Error creating sales order line " + request + " on sales order " + salesOrderId);
                    }
                });
    }

    private <T> Mono<T> mapResponseBodyToError(ClientResponse response, String companyId, String message) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(
                        new BusinessCentralErrorResponseException(
                                message + " for handyman with company id " + companyId, response, errorBody)));
    }
}
