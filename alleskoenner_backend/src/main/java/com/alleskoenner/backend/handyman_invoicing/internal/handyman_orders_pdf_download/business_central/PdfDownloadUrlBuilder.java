package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download.business_central;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PdfDownloadUrlBuilder {
    private final HandymanInvoicingConfig config;

    public PdfDownloadUrlBuilder(HandymanInvoicingConfig config) {
        this.config = config;
    }

    public String buildPdfDownloadUrl(String companyId, String salesInvoiceSystemId) {
        return "v2.0/%s/%s/api/v2.0/companies(%s)/salesInvoices(%s)/pdfDocument/pdfDocumentContent"
                .formatted(
                        config.getBusinessCentral().getBcTenantId(),
                        config.getBusinessCentral().getBcEnvironment(),
                        companyId,
                        salesInvoiceSystemId
                );
    }
}
