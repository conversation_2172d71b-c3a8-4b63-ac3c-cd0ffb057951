package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.business_central;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SalesOrderPostingUrlBuilder {

    private final HandymanInvoicingConfig config;

    String buildSalesInvoicePostUrl(String handymanCompanyId, String salesOrderSystemId) {
        return "v2.0/%s/%s/api/v2.0/companies(%s)/salesOrders(%s)/Microsoft.NAV.shipAndInvoice"
                .formatted(config.getBusinessCentral().getBcTenantId(), config.getBusinessCentral().getBcEnvironment(), handymanCompanyId, salesOrderSystemId);
    }

}