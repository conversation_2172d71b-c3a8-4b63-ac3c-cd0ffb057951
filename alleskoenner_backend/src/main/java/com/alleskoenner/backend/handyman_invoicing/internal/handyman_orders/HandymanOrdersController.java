package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders;

import java.util.List;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@CrossOrigin(value = {"http://localhost:4000/", "http://localhost:4173/", "capacitor://localhost"})
@RestController
@AllArgsConstructor
public class HandymanOrdersController {

    private final HandymanOrdersService handymanOrdersService;

    @GetMapping(value = "/handyman-orders", produces = "application/json")
    public Mono<ResponseEntity<List<HandymanOrdersResponse>>> getHandymanOrders(
            @AuthenticationPrincipal JwtAuthenticationToken principal) {

        var loginEmail = new LoginEmail(principal.getToken().getClaim("email"));

        return handymanOrdersService.getHandymanOrders(loginEmail)
                .map(handymanOrdersResponses -> ResponseEntity.ok().body(handymanOrdersResponses));
    }
}
