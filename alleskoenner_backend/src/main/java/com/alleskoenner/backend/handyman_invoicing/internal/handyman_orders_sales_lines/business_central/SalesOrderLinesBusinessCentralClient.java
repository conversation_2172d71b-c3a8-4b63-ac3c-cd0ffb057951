package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central;

import java.util.List;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.BcSalesOrderLineResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.SalesOrderLinesListResponse;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class SalesOrderLinesBusinessCentralClient {

    private final WebClient handymanInvoicingBcWebClient;
    private final SalesOrderLinesUrlBuilder salesOrderLinesUrlBuilder;

    public Mono<List<BcSalesOrderLineResponse>> getSalesOrderLines(
            LoginEmail loginEmail,
            String companyId,
            String salesOrderId) {

        return handymanInvoicingBcWebClient.get()
                .uri(salesOrderLinesUrlBuilder.buildSalesOrderLinesUrl(companyId, salesOrderId))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(SalesOrderLinesListResponse.class)
                                .map(SalesOrderLinesListResponse::value);
                    } else {
                        return mapResponseBodyToError(response, loginEmail, "Error getting sales order lines for sales order " + salesOrderId);
                    }
                });
    }

    public Mono<Void> deleteSalesOrderLine(
            LoginEmail loginEmail,
            String companyId,
            String salesOrderLineId) {

        return handymanInvoicingBcWebClient.delete()
                .uri(salesOrderLinesUrlBuilder.buildSalesOrderLineUrl(companyId, salesOrderLineId))
                .header(HttpHeaders.IF_MATCH, "*") // Required for PATCH/DELETE operations in Business Central
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return Mono.empty();
                    } else {
                        return mapResponseBodyToError(response, loginEmail, "Error deleting sales order line with id " + salesOrderLineId);
                    }
                });
    }

    private <T> Mono<T> mapResponseBodyToError(ClientResponse response, LoginEmail loginEmail, String message) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(
                        new BusinessCentralErrorResponseException(message + " for handyman with login Email " + loginEmail.email(), response, errorBody)));
    }
}
