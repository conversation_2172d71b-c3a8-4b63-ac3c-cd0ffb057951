package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SalesOrderInformationUrlBuilder {

    private final HandymanInvoicingConfig config;

    public SalesOrderInformationUrlBuilder(HandymanInvoicingConfig config) {
        this.config = config;
    }

    String buildSalesOrderInformationRequestUrl(String handymanCompanyId) {
        return "v2.0/%s/%s/api/itv/handymanApp/v2.0/companies(%s)/salesOrderInformationList?$expand=salesOrderLines".formatted(config.getBusinessCentral()
                .getBcTenantId(), config.getBusinessCentral().getBcEnvironment(), handymanCompanyId);
    }
}
