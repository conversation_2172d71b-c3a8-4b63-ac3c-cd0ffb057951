package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines;

import java.util.List;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.SalesOrderLinesBusinessCentralClient;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.create_sales_order_line.CreateSalesOrderLineService;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.BcSalesOrderLineResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines.business_central.dtos.SalesOrderLineCreateRequest;
import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@AllArgsConstructor
public class HandymanOrdersSalesLinesService {

    private final HandymanCompanyIdFetcher companyIdFetcher;
    private final SalesOrderLinesBusinessCentralClient salesOrderLinesBusinessCentralClient;
    private final CreateSalesOrderLineService createSalesOrderLineService;

    /**
     * Updates all sales order lines for a given sales order.
     * This method deletes all existing sales order lines and creates new ones based on the provided request.
     *
     * @param loginEmail the login email of the user
     * @param request    the sales order lines update request containing the sales order ID and the new sales order lines
     * @return a Mono that completes when the update is done
     */
    public Mono<Void> updateSalesOrderLines(LoginEmail loginEmail, SalesOrderLinesUpdateRequest request) {
        log.info("Updating sales order lines for sales order {} by user {}", request.salesOrderId(), loginEmail.email());

        if (request.salesOrderId() == null || request.salesOrderId().isBlank()) {
            return Mono.error(new UserFacingException(HttpStatus.BAD_REQUEST, "Sales order ID is required"));
        }

        if (request.salesOrderLines() == null) {
            return Mono.error(new UserFacingException(HttpStatus.BAD_REQUEST, "Sales order lines list is required"));
        }

        return companyIdFetcher.getHandymanCompanyIdByLoginEmail(loginEmail)

                .flatMap(companyId ->
                        getSalesOrderLines(loginEmail, companyId, request.salesOrderId())
                                .flatMap(existingLines -> deleteExistingSalesOrderLines(loginEmail, companyId, existingLines))
                                .then(createNewSalesOrderLines(loginEmail, companyId, request.salesOrderId(), request.salesOrderLines())))

                .doOnSuccess(v -> log.info("Successfully updated sales order lines for sales order {} by user {}",
                        request.salesOrderId(), loginEmail.email()))
                .onErrorMap(e -> {
                    if (e instanceof BusinessCentralErrorResponseException) {
                        log.error("Business Central error while updating sales order lines for sales order {} by user {}: {}",
                                request.salesOrderId(), loginEmail.email(), e.getMessage());
                    }
                    log.error("Error while updating sales order lines for sales order {} by user {}: {}",
                            request.salesOrderId(), loginEmail.email(), e.getMessage());
                    return e;
                });
    }

    private Mono<List<BcSalesOrderLineResponse>> getSalesOrderLines(LoginEmail loginEmail, String companyId, String salesOrderId) {
        return salesOrderLinesBusinessCentralClient.getSalesOrderLines(loginEmail, companyId, salesOrderId)
                .doOnSuccess(lines -> log.info("Retrieved {} sales order lines for sales order {} by user {}",
                        lines.size(), salesOrderId, loginEmail.email()))
                .doOnError(e -> log.error("Error retrieving sales order lines for sales order {} by user {}: {}",
                        salesOrderId, loginEmail.email(), e.getMessage()));
    }

    private Mono<Void> deleteExistingSalesOrderLines(LoginEmail loginEmail, String companyId, List<BcSalesOrderLineResponse> existingLines) {
        if (existingLines.isEmpty()) {
            return Mono.empty();
        }

        return Flux.fromIterable(existingLines)
                .concatMap(line -> salesOrderLinesBusinessCentralClient.deleteSalesOrderLine(loginEmail, companyId, line.id()))
                .then();
    }

    private Mono<Void> createNewSalesOrderLines(
            LoginEmail loginEmail,
            String companyId,
            String salesOrderId,
            List<SalesOrderLineRequest> salesOrderLines) {

        if (salesOrderLines.isEmpty()) {
            return Mono.empty();
        }

        return Flux.fromIterable(salesOrderLines)
                .concatMap(lineRequest -> {
                    SalesOrderLineCreateRequest createRequest = new SalesOrderLineCreateRequest(
                            lineRequest.type(),
                            lineRequest.lineObjectNumber(),
                            lineRequest.quantity()
                    );

                    return createSalesOrderLineService.createSalesOrderLine(companyId, salesOrderId, createRequest);
                })
                .then();
    }
}
