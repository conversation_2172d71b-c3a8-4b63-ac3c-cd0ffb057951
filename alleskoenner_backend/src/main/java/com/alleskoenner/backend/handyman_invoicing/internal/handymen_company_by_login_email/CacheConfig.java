package com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email;

import java.time.Duration;
import java.util.List;
import com.github.benmanes.caffeine.cache.AsyncCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CacheConfig {

    @Bean
    public AsyncCache<String, List<Handyman>> handymanCompanyIdsAsyncCache() {
        return Caffeine.newBuilder()
                .expireAfterWrite(Duration.ofHours(2))
                .maximumSize(1000)
                .buildAsync();
    }
}
