package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HexFormat;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SignatureHashCalculator {

    private static final String DATA_URL_PREFIX = "data:";
    private static final String SHA_256_ALGORITHM = "SHA-256";
    private static final char DATA_SEPARATOR = ',';


    public String calculateSha256Hash(String dataUrl) {
        validateInput(dataUrl);

        var base64Data = extractBase64DataFromDataUrl(dataUrl);
        byte[] imageBytes;
        try {
            imageBytes = Base64.getDecoder().decode(base64Data);
        } catch (Exception e) {
            throw new RuntimeException("Base64 decoding of signature image failed.", e);
        }

        try {
            var digest = MessageDigest.getInstance(SHA_256_ALGORITHM);
            var hashBytes = digest.digest(imageBytes);
            var hash = HexFormat.of().formatHex(hashBytes);

            log.debug("Successfully calculated SHA256 hash for signature with length: {} bytes", imageBytes.length);
            return hash;
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA-256 algorithm not available", e);
            throw new RuntimeException("Failed to calculate signature hash", e);
        }
    }

    private void validateInput(String dataUrl) {
        if (dataUrl == null) {
            throw new IllegalArgumentException("DataUrl cannot be null");
        }
        if (dataUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("DataUrl cannot be empty");
        }
    }

    private String extractBase64DataFromDataUrl(String dataUrl) {
        if (!dataUrl.startsWith(DATA_URL_PREFIX)) {
            throw new IllegalArgumentException("Invalid data URL format - must start with 'data:'");
        }

        var commaIndex = dataUrl.indexOf(DATA_SEPARATOR);
        if (commaIndex == -1) {
            throw new IllegalArgumentException("Invalid data URL format - missing comma separator");
        }

        if (commaIndex == dataUrl.length() - 1) {
            throw new IllegalArgumentException("Invalid data URL format - empty data part");
        }

        var base64Part = dataUrl.substring(commaIndex + 1);
        if (base64Part.trim().isEmpty()) {
            throw new IllegalArgumentException("Invalid data URL - empty base64 data");
        }

        return base64Part;
    }
}