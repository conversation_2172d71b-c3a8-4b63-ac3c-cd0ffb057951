package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines_options;

import java.util.List;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines_options.dtos.SalesOrderLineOptionDTO;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@CrossOrigin(value = {"http://localhost:4000/", "http://localhost:4173/", "capacitor://localhost"})
@RestController
@RequestMapping("/handyman-orders-sales-lines-options")
@AllArgsConstructor
public class HandymanOrdersSalesLinesOptionsController {

    private final HandymanOrdersSalesLinesOptionsService optionsService;

    @GetMapping(produces = "application/json")
    public Mono<ResponseEntity<List<SalesOrderLineOptionDTO>>> getSalesOrderLineOptions() {
        return optionsService.getSalesOrderLineOptions()
                .map(ResponseEntity::ok);
    }
}
