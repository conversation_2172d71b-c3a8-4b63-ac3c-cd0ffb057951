package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines_options;

import java.math.BigDecimal;
import java.util.List;

import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines_options.dtos.SalesOrderLineOptionDTO;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
public class HandymanOrdersSalesLinesOptionsService {

    public Mono<List<SalesOrderLineOptionDTO>> getSalesOrderLineOptions() {
        List<SalesOrderLineOptionDTO> options = List.of(
                new SalesOrderLineOptionDTO(
                        "Handwerkerleistung",
                        "R0020",
                        BigDecimal.valueOf(81.93),
                        "Resource",
                        new SalesOrderLineOptionDTO.Rules(
                                BigDecimal.valueOf(1.0),
                                BigDecimal.valueOf(0.25),
                                false,
                                false,
                                true
                        )
                ),
                new SalesOrderLineOptionDTO(
                        "Anfahrtskosten",
                        "1000",
                        BigDecimal.valueOf(29.3),
                        "Item",
                        new SalesOrderLineOptionDTO.Rules(
                                BigDecimal.valueOf(1.0),
                                BigDecimal.valueOf(1.0),
                                false,
                                false,
                                false
                        )
                ),
                new SalesOrderLineOptionDTO(
                        "Kleinteile-Pauschale",
                        "1003",
                        BigDecimal.valueOf(4.95),
                        "Item",
                        new SalesOrderLineOptionDTO.Rules(
                                BigDecimal.valueOf(1.0),
                                BigDecimal.valueOf(1.0),
                                true,
                                false,
                                true
                        )
                )
        );
        return Mono.just(options);
    }
}
