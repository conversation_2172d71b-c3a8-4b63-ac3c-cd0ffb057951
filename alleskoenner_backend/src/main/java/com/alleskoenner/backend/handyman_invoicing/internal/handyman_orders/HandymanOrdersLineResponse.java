package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders;

import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos.SalesOrderLineResponse;

import java.math.BigDecimal;

public record HandymanOrdersLineResponse(
        BigDecimal amount,
        String description,
        String lineObjectNumber,
        BigDecimal quantity,
        String type,
        String unitOfMeasure,
        BigDecimal unitPrice,
        String systemId,
        BigDecimal vatAmount,
        BigDecimal vatPercent
) {
    public static HandymanOrdersLineResponse from(SalesOrderLineResponse salesOrderLine) {
        return new HandymanOrdersLineResponse(
                salesOrderLine.amount(),
                salesOrderLine.description(),
                salesOrderLine.no(),
                salesOrderLine.quantity(),
                salesOrderLine.type(),
                salesOrderLine.unitOfMeasure(),
                salesOrderLine.unitPrice(),
                salesOrderLine.systemId(),
                salesOrderLine.vatAmount(),
                salesOrderLine.vatPercent());
    }
}