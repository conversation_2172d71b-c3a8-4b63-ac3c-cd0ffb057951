package com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HandymenUrlBuilder {

    private final HandymanInvoicingConfig config;

    public HandymenUrlBuilder(HandymanInvoicingConfig config) {
        this.config = config;
    }

    String buildHandymanUrl() {
        return "v2.0/" + config.getBusinessCentral().getBcMainEnvironment() + "/api/ITV/handyman/v2.0/companies(" + config.getBusinessCentral()
                .getBcMainCompanyId() + ")/handyman";
    }
}
