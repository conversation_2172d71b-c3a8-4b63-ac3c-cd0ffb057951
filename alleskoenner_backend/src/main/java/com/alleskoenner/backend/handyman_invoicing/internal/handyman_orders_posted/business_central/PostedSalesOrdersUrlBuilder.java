package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central;

import com.alleskoenner.backend.handyman_invoicing.internal.business_central.HandymanInvoicingConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PostedSalesOrdersUrlBuilder {

    private final HandymanInvoicingConfig config;

    public PostedSalesOrdersUrlBuilder(HandymanInvoicingConfig config) {
        this.config = config;
    }

    String buildPostedSalesOrderRequestUrl(String handymanCompanyId) {
        return "v2.0/%s/%s/api/itv/handymanApp/v2.0/companies(%s)/postedSalesOrderInformationList?$expand=postedSalesOrderLines"
                .formatted(config.getBusinessCentral().getBcTenantId(), config.getBusinessCentral().getBcEnvironment(), handymanCompanyId);
    }

    String buildSinglePostedSalesOrderRequestUrl(String handymanCompanyId, String caseNumber) {
        return "v2.0/%s/%s/api/itv/handymanApp/v2.0/companies(%s)/postedSalesOrderInformationList?$filter=salesOrderNo eq '%s'&$expand=postedSalesOrderLines"
                .formatted(config.getBusinessCentral().getBcTenantId(), config.getBusinessCentral().getBcEnvironment(), handymanCompanyId, caseNumber);
    }
}