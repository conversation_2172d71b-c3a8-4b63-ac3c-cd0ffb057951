package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download.business_central;

import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

@Slf4j
@Component
@AllArgsConstructor
public class PdfDownloadBusinessCentralClient {
    private final WebClient handymanInvoicingBcWebClient;
    private final PdfDownloadUrlBuilder pdfDownloadUrlBuilder;

    public Flux<DataBuffer> downloadPdf(LoginEmail loginEmail, String companyId, String salesInvoiceSystemId) {
        String url = pdfDownloadUrlBuilder.buildPdfDownloadUrl(companyId, salesInvoiceSystemId);

        log.info("Requesting PDF from Business Central: {}", url);

        return handymanInvoicingBcWebClient.get()
                .uri(url)
                .header(HttpHeaders.ACCEPT_LANGUAGE, "de-DE")
                .accept(MediaType.APPLICATION_PDF)
                .exchangeToFlux(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToFlux(DataBuffer.class);
                    } else {
                        return mapResponseBodyToError(response, loginEmail, salesInvoiceSystemId);
                    }
                });
    }

    private <T> Flux<T> mapResponseBodyToError(ClientResponse response, LoginEmail loginEmail, String salesInvoiceSystemId) {
        return response.bodyToMono(String.class)
                .flatMapMany(errorBody -> Flux.error(
                        new BusinessCentralErrorResponseException("Error downloading PDF for sales invoice " + salesInvoiceSystemId + " for handyman with login Email " + loginEmail.email() + ": " + errorBody, response, errorBody)));
    }
}
