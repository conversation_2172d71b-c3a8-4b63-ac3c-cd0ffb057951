package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.business_central;

import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class SalesOrderPostingClient {

    private final WebClient handymanInvoicingBcWebClient;
    private final SalesOrderPostingUrlBuilder salesOrderPostingUrlBuilder;

    public Mono<Void> postSalesOrder(LoginEmail loginEmail, String companyId, String salesOrderSystemId) {

        return handymanInvoicingBcWebClient.post()
                .uri(salesOrderPostingUrlBuilder.buildSalesInvoicePostUrl(companyId, salesOrderSystemId))
                .contentType(APPLICATION_JSON)
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return Mono.empty();
                    } else {
                        return mapResponseBodyToError(response, "Error posting Sales Invoice for handyman with login Email " + loginEmail.email());
                    }
                });
    }

    private <T> Mono<T> mapResponseBodyToError(ClientResponse response, String message) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(
                        new BusinessCentralErrorResponseException(message, response, errorBody)));
    }
}