package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos;

import java.math.BigDecimal;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;

public record SalesOrderInformationItemResponse(
        @JsonProperty("@odata.etag")
        String odataETag,
        String orderNo,
        String documentType,
        boolean asSoonAsPossible,
        String requestedDeliveryDatetime,
        String sellToCustomerNo,
        BigDecimal amountIncludingVAT,
        BigDecimal amountExcludingVAT,
        String sellToCustomerName,
        String sellToCustomerName2,
        String sellToAddress,
        String sellToCity,
        String sellToPostCode,
        String sellToPhoneNo,
        String sellToEmail,
        String shipToName,
        String shipToAddress,
        String shipToAddress2,
        String shipToCity,
        String shipToPostCode,
        String shipToPhoneNo,
        String billToName,
        String billToAddress,
        String billToAddress2,
        String billToCity,
        String billToPostCode,
        String status,
        String systemId,
        String systemCreatedAt,
        String workDescription,
        List<SalesOrderLineResponse> salesOrderLines
) {
}
