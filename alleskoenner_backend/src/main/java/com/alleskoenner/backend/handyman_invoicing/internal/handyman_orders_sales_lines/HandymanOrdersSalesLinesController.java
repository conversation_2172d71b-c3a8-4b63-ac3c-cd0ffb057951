package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_sales_lines;

import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingErrorMessage;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@CrossOrigin(value = {"http://localhost:4000/", "http://localhost:4173/", "capacitor://localhost"})
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/handyman-orders-sales-lines")
public class HandymanOrdersSalesLinesController {

    private final HandymanOrdersSalesLinesService handymanOrdersSalesLinesService;

    /**
     * Updates all sales order lines for a given sales order.
     * This endpoint deletes all existing sales order lines and creates new ones based on the provided request.
     *
     * @param principal the authenticated user
     * @param request   the sales order lines update request containing the sales order ID and the new sales order lines
     * @return a ResponseEntity with HTTP 200 OK when the update is successful
     */
    @PutMapping
    public Mono<Void> updateSalesOrderLines(
            @AuthenticationPrincipal JwtAuthenticationToken principal,
            @RequestBody SalesOrderLinesUpdateRequest request) {

        LoginEmail loginEmail = new LoginEmail(principal.getToken().getClaim("email"));
        log.info("Received request to update sales order lines for sales order {} by user {}",
                request.salesOrderId(), loginEmail.email());

        return handymanOrdersSalesLinesService.updateSalesOrderLines(loginEmail, request);
    }

    @ExceptionHandler(exception = UserFacingException.class, produces = "application/json")
    public ResponseEntity<UserFacingErrorMessage> handleError(UserFacingException exc) {
        return ResponseEntity.status(exc.getStatusCode()).body(new UserFacingErrorMessage(exc.getMessage()));
    }

    @ExceptionHandler(produces = "application/json")
    public ResponseEntity<UserFacingErrorMessage> handleError(RuntimeException exc) {
        log.error("An error occurred while updating sales order lines: {}", exc.getMessage(), exc);
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new UserFacingErrorMessage("An error occurred while updating sales order lines."));
    }
}
