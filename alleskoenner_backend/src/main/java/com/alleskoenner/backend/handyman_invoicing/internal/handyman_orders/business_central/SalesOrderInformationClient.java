package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central;

import java.util.List;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos.SalesOrderInformationItemResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos.SalesOrderInformationListContextResponse;
import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.shared.BusinessCentralErrorResponseException;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class SalesOrderInformationClient {

    private final WebClient handymanInvoicingBcWebClient;
    private final SalesOrderInformationUrlBuilder salesOrderInformationUrlBuilder;
    private final HandymanCompanyIdFetcher companyIdFetcher;

    @NotNull
    public Mono<List<SalesOrderInformationItemResponse>> fetchSalesOrderInformationList(LoginEmail loginEmail, String companyId) {
        return handymanInvoicingBcWebClient.get()
                .uri(salesOrderInformationUrlBuilder.buildSalesOrderInformationRequestUrl(companyId))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(SalesOrderInformationListContextResponse.class)
                                .map(SalesOrderInformationListContextResponse::value);
                    } else {
                        return mapResponseBodyToError(response, loginEmail);
                    }
                });
    }

    Mono<List<SalesOrderInformationItemResponse>> mapResponseBodyToError(ClientResponse response, LoginEmail loginEmail) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> Mono.error(
                        new BusinessCentralErrorResponseException(
                                "Error fetching Sales Order Information List for handyman with login Email " + loginEmail.email(), response, errorBody)));
    }
}
