package com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email;

import java.util.List;

import com.alleskoenner.backend.shared.LoginEmail;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.With;

@With
public record Handyman(
        @JsonProperty("@odata.etag")
        String odataEtag,
        String systemId,
        String handymanSystemId,
        LoginEmail loginEmail,
        String companyID,
        String companyName,
        List<String> deviceTokens,
        String environmentName,
        String name,
        String outlookEMailAddress
) {
}
