package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posting.signature_upload;

import java.util.Base64;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class SignaturePayloadBuilder {

    private final ObjectMapper objectMapper;

    public String buildBase64Payload(String caseNumber, String customerSignature, String signatureHash) {
        log.debug("Building base64 payload for case number: {}", caseNumber);

        var signatureBase64 = extractBase64DataFromDataUrl(customerSignature);

        var innerPayload = new SignatureUploadPayload(caseNumber, signatureBase64, signatureHash);

        try {
            var innerPayloadJson = objectMapper.writeValueAsString(innerPayload);
            var base64EncodedPayload = Base64.getEncoder().encodeToString(innerPayloadJson.getBytes());

            var outerPayload = new Base64PayloadWrapper(base64EncodedPayload);
            var result = objectMapper.writeValueAsString(outerPayload);

            log.debug("Successfully built base64 payload for case number: {}", caseNumber);
            return result;
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize signature upload payload for case number: {}", caseNumber, e);
            throw new RuntimeException("Failed to build signature upload payload", e);
        }
    }

    private String extractBase64DataFromDataUrl(String dataUrl) {
        if (!dataUrl.startsWith("data:")) {
            throw new IllegalArgumentException("Invalid data URL format");
        }

        var commaIndex = dataUrl.indexOf(',');
        if (commaIndex == -1) {
            throw new IllegalArgumentException("Invalid data URL format - missing comma");
        }

        return dataUrl.substring(commaIndex + 1);
    }

    private record SignatureUploadPayload(
            String salesOrderNumber,
            String signatureBase64,
            String signatureHash
    ) {
    }

    private record Base64PayloadWrapper(
            String base64Payload
    ) {
    }
}
