package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download;

import com.alleskoenner.backend.handyman_invoicing.internal.handymen_company_by_login_email.HandymanCompanyIdFetcher;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download.business_central.PdfDownloadBusinessCentralClient;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.core.io.buffer.DataBuffer;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@AllArgsConstructor
public class HandymanOrdersPdfDownloadService {
    private final HandymanCompanyIdFetcher companyIdFetcher;
    private final PdfDownloadBusinessCentralClient pdfDownloadBusinessCentralClient;

    public Mono<Flux<DataBuffer>> downloadPdf(LoginEmail loginEmail, String salesInvoiceSystemId) {
        if (salesInvoiceSystemId == null || salesInvoiceSystemId.isBlank()) {
            return Mono.error(new UserFacingException(HttpStatus.BAD_REQUEST, "Sales invoice system ID is required"));
        }
        return companyIdFetcher.getHandymanCompanyIdByLoginEmail(loginEmail)
                .flatMap(companyId -> {
                    log.info("Fetching PDF for sales invoice {} and company {}", salesInvoiceSystemId, companyId);
                    return Mono.just(pdfDownloadBusinessCentralClient.downloadPdf(loginEmail, companyId, salesInvoiceSystemId));
                });
    }
}
