package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders.business_central.dtos.SalesOrderInformationItemResponse;
import com.fasterxml.jackson.annotation.JsonGetter;

public record HandymanOrdersResponse(
        String addressLine1,
        BigDecimal amountExcludingVAT,
        BigDecimal amountIncludingVAT,
        String caseNumber,
        String city,
        String description,
        String displayName,
        String email,
        ZonedDateTime expirationDateTime,
        @JsonGetter("isAsap") boolean isAsap,
        List<HandymanOrdersLineResponse> orderLines,
        String phoneNumber,
        String postalCode,
        ZonedDateTime scheduledDateTime,
        String systemId,
        String billToName,
        String billToAddress,
        String billToAddress2,
        String billToCity,
        String billToPostCode
) {

    public static HandymanOrdersResponse from(SalesOrderInformationItemResponse salesOrderResponse, ZonedDateTime expirationDateTime) {
        List<HandymanOrdersLineResponse> orderLines;
        if (salesOrderResponse.salesOrderLines() != null) {
            orderLines = salesOrderResponse.salesOrderLines().stream()
                    .map(HandymanOrdersLineResponse::from)
                    .collect(Collectors.toList());
        } else {
            orderLines = Collections.emptyList();
        }

        return new HandymanOrdersResponse(
                salesOrderResponse.shipToAddress(),
                salesOrderResponse.amountExcludingVAT(),
                salesOrderResponse.amountIncludingVAT(),
                salesOrderResponse.orderNo(),
                salesOrderResponse.shipToCity(),
                salesOrderResponse.workDescription(),
                salesOrderResponse.shipToName(),
                salesOrderResponse.sellToEmail(),
                expirationDateTime,
                salesOrderResponse.asSoonAsPossible(),
                orderLines,
                salesOrderResponse.shipToPhoneNo(),
                salesOrderResponse.shipToPostCode(),
                salesOrderResponse.asSoonAsPossible()
                        ? null
                        : ZonedDateTime.parse(salesOrderResponse.requestedDeliveryDatetime()),
                salesOrderResponse.systemId(),
                salesOrderResponse.billToName(),
                salesOrderResponse.billToAddress(),
                salesOrderResponse.billToAddress2(),
                salesOrderResponse.billToCity(),
                salesOrderResponse.billToPostCode()
        );
    }

}
