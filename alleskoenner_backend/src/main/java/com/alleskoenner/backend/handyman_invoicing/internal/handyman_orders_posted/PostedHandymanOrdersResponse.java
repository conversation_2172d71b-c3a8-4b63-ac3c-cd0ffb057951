package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_posted.business_central.dtos.PostedSalesOrderInformationItemResponse;
import com.fasterxml.jackson.annotation.JsonGetter;

public record PostedHandymanOrdersResponse(
        String addressLine1,
        BigDecimal amountExcludingVAT,
        BigDecimal amountIncludingVAT,
        String caseNumber,
        String city,
        String description,
        String displayName,
        String email,
        ZonedDateTime expirationDateTime,
        @JsonGetter("isAsap") boolean isAsap,
        List<PostedHandymanOrdersLineResponse> orderLines,
        String phoneNumber,
        String postalCode,
        ZonedDateTime scheduledDateTime,
        String systemId,
        String workDescription,
        String salesOrderNumber,
        String billToName,
        String billToAddress,
        String billToAddress2,
        String billToCity,
        String billToPostCode
) {

    public static PostedHandymanOrdersResponse from(PostedSalesOrderInformationItemResponse salesOrderResponse, Duration defaultAsapWindow) {
        List<PostedHandymanOrdersLineResponse> orderLines;
        if (salesOrderResponse.postedSalesOrderLines() != null) {
            orderLines = salesOrderResponse.postedSalesOrderLines().stream()
                    .map(PostedHandymanOrdersLineResponse::from)
                    .collect(Collectors.toList());
        } else {
            orderLines = Collections.emptyList();
        }
        final var expirationDateTime = ZonedDateTime.parse(salesOrderResponse.systemCreatedAt()).plus(defaultAsapWindow);

        return new PostedHandymanOrdersResponse(
                salesOrderResponse.shipToAddress(),
                salesOrderResponse.amountExcludingVAT(),
                salesOrderResponse.amountIncludingVAT(),
                salesOrderResponse.orderNo(),
                salesOrderResponse.shipToCity(),
                salesOrderResponse.workDescription(),
                salesOrderResponse.shipToName(),
                salesOrderResponse.sellToEmail(),
                expirationDateTime,
                salesOrderResponse.asSoonAsPossible(),
                orderLines,
                salesOrderResponse.shipToPhoneNo(),
                salesOrderResponse.shipToPostCode(),
                salesOrderResponse.asSoonAsPossible()
                        ? null
                        : ZonedDateTime.parse(salesOrderResponse.requestedDeliveryDatetime()),
                salesOrderResponse.systemId(),
                salesOrderResponse.workDescription(),
                salesOrderResponse.salesOrderNo(),
                salesOrderResponse.billToName(),
                salesOrderResponse.billToAddress(),
                salesOrderResponse.billToAddress2(),
                salesOrderResponse.billToCity(),
                salesOrderResponse.billToPostCode()
        );
    }

}
