package com.alleskoenner.backend.handyman_invoicing.internal.handyman_orders_pdf_download;

import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingErrorMessage;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@CrossOrigin(value = {"http://localhost:4000/", "http://localhost:4173/", "capacitor://localhost"})
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/handyman-orders")
public class HandymanOrdersPdfDownloadController {

    private final HandymanOrdersPdfDownloadService handymanOrdersPdfDownloadService;

    @GetMapping(value = "/{systemId}/pdf", produces = MediaType.APPLICATION_PDF_VALUE)
    public Mono<ResponseEntity<Flux<DataBuffer>>> downloadPdf(
            @AuthenticationPrincipal JwtAuthenticationToken principal,
            @PathVariable String systemId) {

        LoginEmail loginEmail = new LoginEmail(principal.getToken().getClaim("email"));

        log.info("Received request to download PDF for sales invoice {} by user {}", systemId, loginEmail);
        return handymanOrdersPdfDownloadService.downloadPdf(loginEmail, systemId)
                .map(pdfFlux -> ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=invoice-" + systemId + ".pdf")
                        .contentType(MediaType.APPLICATION_PDF)
                        .body(pdfFlux)
                );
    }

    @ExceptionHandler(exception = UserFacingException.class, produces = "application/json")
    public ResponseEntity<UserFacingErrorMessage> handleError(UserFacingException exc) {
        return ResponseEntity.status(exc.getStatusCode()).body(new UserFacingErrorMessage(exc.getMessage()));
    }

    @ExceptionHandler(produces = "application/json")
    public ResponseEntity<UserFacingErrorMessage> handleError(RuntimeException exc) {
        log.error("An error occurred while downloading the PDF: {}", exc.getMessage(), exc);
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new UserFacingErrorMessage("An error occurred while downloading the PDF."));
    }
}
