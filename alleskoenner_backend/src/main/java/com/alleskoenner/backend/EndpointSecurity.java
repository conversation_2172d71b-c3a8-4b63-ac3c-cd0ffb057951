package com.alleskoenner.backend;


import java.util.List;

import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.ReactiveAuthorizationManager;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.web.server.authorization.AuthorizationContext;
import org.springframework.stereotype.Component;

@Component
public class EndpointSecurity {

    @Value("${security.client.customer-client-id}")
    private String customerClientId;

    @Value("${security.client.customer-magic-link-client-id}")
    private String customerMagicLinkClientId;

    @Value("${security.client.handyman-client-id}")
    private String handymanClientId;

    @NotNull
    public Customizer<ServerHttpSecurity.AuthorizeExchangeSpec> getAuthorizeExchangeSpecCustomizer() {
        return exchanges -> exchanges
                .pathMatchers(HttpMethod.OPTIONS).permitAll() // CORS Preflight
                .pathMatchers("/order/**").access(isCustomer())
                .pathMatchers("/customer/details/**").access(isCustomer())
                .pathMatchers("/appointment-request/pending").access(isHandyman())
                .pathMatchers("/appointment-request/*/confirm").access(isHandyman())
                .pathMatchers("/appointment-request/*/reject").access(isHandyman())
                .pathMatchers("/handyman-orders").access(isHandyman())
                .pathMatchers("/handyman-orders/**").access(isHandyman())
                .pathMatchers("/handyman-orders-sales-lines").access(isHandyman())
                .pathMatchers("/handyman-orders-sales-lines-options").access(isHandyman())
                .pathMatchers("/auth/magic-link-request", "/auth/magic-link-login").permitAll()
                .pathMatchers("/actuator/health").permitAll()
                .anyExchange().denyAll();
    }

    @NotNull
    private ReactiveAuthorizationManager<AuthorizationContext> isCustomer() {
        return checkAzp(List.of(customerClientId, customerMagicLinkClientId));
    }

    @NotNull
    private ReactiveAuthorizationManager<AuthorizationContext> isHandyman() {
        return checkAzp(handymanClientId);
    }

    @NotNull
    private static ReactiveAuthorizationManager<AuthorizationContext> checkAzp(final String clientId) {
        return checkAzp(List.of(clientId));
    }

    @NotNull
    private static ReactiveAuthorizationManager<AuthorizationContext> checkAzp(final List<String> clientIds) {
        return (authenticationMono, _) ->
                authenticationMono.map(authentication -> {
                    if (authentication instanceof JwtAuthenticationToken token &&
                            token.getPrincipal() instanceof Jwt principal &&
                            (clientIds.contains((String) principal.getClaim("azp")))) {
                        return new AuthorizationDecision(true);
                    }
                    return new AuthorizationDecision(false);
                });
    }

}
