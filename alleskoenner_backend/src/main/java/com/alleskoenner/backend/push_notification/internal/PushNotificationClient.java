package com.alleskoenner.backend.push_notification.internal;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.security.spec.InvalidKeySpecException;

import com.eatthepath.pushy.apns.ApnsClient;
import com.eatthepath.pushy.apns.ApnsClientBuilder;
import lombok.Getter;
import org.springframework.stereotype.Service;

@Service
@Getter
public class PushNotificationClient {

    private final ApnsClient apnsClient;

    public PushNotificationClient(PushNotificationConfiguration pushNotificationConfiguration, CertificateLoader certificateLoader) throws IOException, CertificateException, NoSuchAlgorithmException, InvalidKeySpecException {

        X509Certificate certificate = certificateLoader.getCertificate(pushNotificationConfiguration.getCertificateBase64());
        PrivateKey privateKey = certificateLoader.getPrivateKey(pushNotificationConfiguration.getPrivateKey());

        apnsClient = new ApnsClientBuilder()
                .setApnsServer(ApnsClientBuilder.PRODUCTION_APNS_HOST)
                .setClientCredentials(certificate, privateKey, "")
                .build();
    }

}
