package com.alleskoenner.backend.push_notification.internal;

import java.time.Duration;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "push-notification")
public class PushNotificationConfiguration {
    private String privateKey;
    private String certificateBase64;
    private String notificationTopic;

    private AppointmentRequestNotificationRetry appointmentRequestNotificationRetry;

    @Getter
    @Setter
    public static class AppointmentRequestNotificationRetry {
        private int maximumAttempts = 5;
        private Duration minimumBackoffDuration = Duration.ofMillis(500);
        private Duration maximumBackoffDuration = Duration.ofSeconds(30);
    }
}

