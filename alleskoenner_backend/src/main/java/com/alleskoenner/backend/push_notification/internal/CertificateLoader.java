package com.alleskoenner.backend.push_notification.internal;

import java.io.ByteArrayInputStream;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import org.springframework.stereotype.Service;

@Service
public class CertificateLoader {

    public X509Certificate getCertificate(String certString) throws CertificateException {
        byte[] decodedBytes = Base64.getDecoder().decode(certString);

        CertificateFactory certFactory = CertificateFactory.getInstance("X.509");
        ByteArrayInputStream inputStream = new ByteArrayInputStream(decodedBytes);
        return (X509Certificate) certFactory.generateCertificate(inputStream);
    }

    public PrivateKey getPrivateKey(String keyString) throws NoSuchAlgorithmException, InvalidKeySpecException {
        String privateKeyPEM = keyString.replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");

        byte[] keyBytes = Base64.getDecoder().decode(privateKeyPEM);

        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(keySpec);
    }
}
