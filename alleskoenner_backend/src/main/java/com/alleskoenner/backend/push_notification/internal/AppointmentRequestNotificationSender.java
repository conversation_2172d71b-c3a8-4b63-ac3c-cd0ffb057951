package com.alleskoenner.backend.push_notification.internal;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import com.alleskoenner.backend.push_notification.AppointmentRequestNotificationSenderInterface;
import com.eatthepath.pushy.apns.PushNotificationResponse;
import com.eatthepath.pushy.apns.util.SimpleApnsPayloadBuilder;
import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
import com.eatthepath.pushy.apns.util.TokenUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@AllArgsConstructor
public class AppointmentRequestNotificationSender implements AppointmentRequestNotificationSenderInterface {

    private final PushNotificationClient pushNotificationClient;
    private final PushNotificationConfiguration configuration;
    private final NotificationSender notificationSender;
    private final ErrorNotificationEmailServiceInterface errorNotificationEmailService;

    @Override
    public Mono<Void> sendAppointmentRequestNotification(List<String> deviceTokens, ZonedDateTime zonedDateTime, boolean isAsSoonAsPossible) {
        var apnsClient = pushNotificationClient.getApnsClient();
        var payload = buildPayload(zonedDateTime, isAsSoonAsPossible);

        return Flux.fromIterable(deviceTokens)
                .map(TokenUtil::sanitizeTokenString)
                .map(token -> new SimpleApnsPushNotification(token, configuration.getNotificationTopic(), payload))

                .flatMap(notification -> notificationSender.sendNotificationWithRetry(notification, apnsClient))

                .collectList()
                .doOnNext(this::logNotificationResultAndSendErrorNotificationEmail)
                .then(Mono.empty());
    }

    private void logNotificationResultAndSendErrorNotificationEmail(List<PushNotificationResponse<SimpleApnsPushNotification>> pushNotificationResponses) {

        var notificationResponses = pushNotificationResponses.stream()
                .collect(Collectors.partitioningBy(PushNotificationResponse::isAccepted));

        notificationResponses.get(true).forEach(response -> {
            log.debug("Notification for device {} was accepted with APNs Id {}.", response.getPushNotification().getToken(), response.getApnsId());
        });

        notificationResponses.get(false).forEach(response -> {
            log.error("Notification to device token {} was rejected with reason \"{}\" and APNs Id {}.", response.getPushNotification()
                    .getToken(), response.getRejectionReason(), response.getApnsId());
        });

        var collectedRejectedReasons = notificationResponses.get(false)
                .stream()
                .map(AppointmentRequestNotificationSender::buildErrorMessageForRejectedReason
                ).collect(Collectors.joining("\n"));
        if (!collectedRejectedReasons.isEmpty()) {
            errorNotificationEmailService.sendErrorNotificationEmail(new Throwable(collectedRejectedReasons));
        }
    }

    @NotNull
    private static String buildErrorMessageForRejectedReason(PushNotificationResponse<SimpleApnsPushNotification> response) {
        var reasonText = response.getRejectionReason()
                .map(rejectionReason -> "with reason '" + rejectionReason + "'.")
                .orElseGet(() -> "without reason.");

        return "Notification for device with device token '" + response.getPushNotification()
                .getToken() + "' was rejected " + reasonText;
    }

    private static String buildPayload(ZonedDateTime zonedDateTime, Boolean asSoonAsPossible) {

        var dateTimeFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy, HH 'Uhr'");

        String subtitle;
        try {
            subtitle = asSoonAsPossible ? "So schnell wie möglich" : dateTimeFormatter.format(zonedDateTime);
        } catch (Exception e) {
            log.error("Could not format ZonedDateTime: {}", e.getMessage(), e);
            subtitle = "Zeit leider unbekannt";
        }


        return new SimpleApnsPayloadBuilder()
                .setAlertTitle("Neue Anfrage")
                .setAlertSubtitle(subtitle)
                .setSound("default")
                .addCustomProperty("route", "appointment-requests")
                .build();
    }
}
