package com.alleskoenner.backend.push_notification.internal;

import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import com.eatthepath.pushy.apns.ApnsClient;
import com.eatthepath.pushy.apns.PushNotificationResponse;
import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

@Slf4j
@Service
@AllArgsConstructor
public class NotificationSender {

    private final PushNotificationConfiguration configuration;

    @NotNull Mono<PushNotificationResponse<SimpleApnsPushNotification>> sendNotificationWithRetry(SimpleApnsPushNotification notification, ApnsClient apnsClient) {
        final Supplier<CompletableFuture<? extends PushNotificationResponse<SimpleApnsPushNotification>>> completableFutureSupplier = () -> apnsClient.sendNotification(notification);
        PushNotificationConfiguration.AppointmentRequestNotificationRetry retryConfig = configuration.getAppointmentRequestNotificationRetry();
        return Mono.fromFuture(completableFutureSupplier)
                .retryWhen(
                        Retry.backoff(retryConfig.getMaximumAttempts(), retryConfig.getMinimumBackoffDuration())
                                .maxBackoff(retryConfig.getMaximumBackoffDuration())
                                .onRetryExhaustedThrow((_, signal) -> signal.failure()))
                .onErrorResume(exception -> {
                    log.error("APNs Notification failed after retries: {}", exception.getMessage(), exception);
                    return Mono.empty();
                });
    }
}
