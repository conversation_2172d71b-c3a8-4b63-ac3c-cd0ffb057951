package com.alleskoenner.backend;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.NimbusReactiveJwtDecoder;
import org.springframework.security.oauth2.jwt.ReactiveJwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.oauth2.server.resource.authentication.ReactiveJwtAuthenticationConverterAdapter;
import org.springframework.security.web.server.SecurityWebFilterChain;

@Configuration
@Profile("!test")
public class SecurityConfig {

    private final EndpointSecurity endpointSecurity;

    private final ReactiveJwtDecoder customerJwtDecoder = NimbusReactiveJwtDecoder.withJwkSetUri("https://oidc.klosebrothers.de/realms/alleskoenner24/protocol/openid-connect/certs")
            .build();
    private final ReactiveJwtDecoder handymanJwtDecoder = NimbusReactiveJwtDecoder.withJwkSetUri("https://oidc.klosebrothers.de/realms/alleskoenner24-handyman/protocol/openid-connect/certs")
            .build();


    public SecurityConfig(EndpointSecurity endpointSecurity) {
        this.endpointSecurity = endpointSecurity;
    }

    @Bean
    public SecurityWebFilterChain orderSecurityFilterChain(ServerHttpSecurity http) {

        http
                .csrf(ServerHttpSecurity.CsrfSpec::disable)
                .authorizeExchange(endpointSecurity.getAuthorizeExchangeSpecCustomizer())
                .oauth2ResourceServer(oauth2 -> oauth2
                        .jwt(jwt -> jwt
                                .jwtDecoder(customerOrHandymanJwtDecoder())
                                .jwtAuthenticationConverter(reactiveJwtAuthenticationConverter())
                        )
                );

        return http.build();
    }

    @Bean
    public ReactiveJwtDecoder customerOrHandymanJwtDecoder() {
        return token -> customerJwtDecoder.decode(token)
                .onErrorResume(_ -> handymanJwtDecoder.decode(token));
    }

    @Bean
    public ReactiveJwtAuthenticationConverterAdapter reactiveJwtAuthenticationConverter() {
        Converter<Jwt, AbstractAuthenticationToken> converter = jwt -> {
            var clientId = jwt.getClaimAsString("azp");
            var authorities = extractAuthorities(jwt, clientId);
            return new JwtAuthenticationToken(jwt, authorities);
        };
        return new ReactiveJwtAuthenticationConverterAdapter(converter);
    }

    private Collection<SimpleGrantedAuthority> extractAuthorities(Jwt jwt, String clientId) {
        Map<String, Object> resourceAccess = jwt.getClaim("resource_access");

        var roles = extractRolesFromNestedResourceAccessObject(resourceAccess, clientId);
        return roles.stream()
                .filter(Objects::nonNull)
                .map(Object::toString)
                .map(role -> "ROLE_" + role.toUpperCase())
                .map(SimpleGrantedAuthority::new)
                .toList();
    }

    @SuppressWarnings("unchecked")
    private Collection<Object> extractRolesFromNestedResourceAccessObject(Map<String, Object> resourceAccess, String clientId) {
        return Optional.ofNullable(resourceAccess)
                .filter(_ -> clientId != null)
                .map(ra -> ra.get(clientId))
                .filter(Map.class::isInstance)
                .map(Map.class::cast)
                .map(clientMap -> clientMap.get("roles"))
                .filter(Collection.class::isInstance)
                .map(Collection.class::cast)
                .orElse(Collections.emptyList());
    }
}
