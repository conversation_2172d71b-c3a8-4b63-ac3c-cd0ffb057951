package com.alleskoenner.backend;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.modulith.Modulithic;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;

@Modulithic
@SpringBootApplication
@EnableCaching
@EnableWebFluxSecurity
@EnableConfigurationProperties
@ComponentScan(basePackages = {"com.alleskoenner.backend.shared", "com.alleskoenner.backend"})
public class AlleskoennerBackendApplication {

    public static void main(String[] args) {
        SpringApplication.run(AlleskoennerBackendApplication.class, args);
    }

}
