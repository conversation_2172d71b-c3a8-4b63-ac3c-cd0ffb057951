package com.alleskoenner.backend;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;

@Configuration
@Profile("test")
public class TestSecurityConfig {

    private final EndpointSecurity endpointSecurity;

    public TestSecurityConfig(EndpointSecurity endpointSecurity) {
        this.endpointSecurity = endpointSecurity;
    }

    @Bean
    public SecurityWebFilterChain orderSecurityFilterChain(ServerHttpSecurity http) {
        http
                .csrf(ServerHttpSecurity.CsrfSpec::disable)
                .authorizeExchange(endpointSecurity.getAuthorizeExchangeSpecCustomizer());
        return http.build();
    }
}
