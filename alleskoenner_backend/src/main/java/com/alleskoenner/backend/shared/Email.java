package com.alleskoenner.backend.shared;


import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonValue;
import org.springframework.lang.NonNull;

public class Email {
    @JsonValue
    private final String email;

    public Email(@NonNull String email) {
        this.email = email.toLowerCase(); // Normalize
    }

    public String email() {
        return email;
    }

    @Override
    public String toString() {
        return email;
    }

    // email addresses are the same even if they are in different cases
    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Email that = (Email) o;
        return Objects.equals(email.toLowerCase(), that.email.toLowerCase());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(email);
    }
}
