package com.alleskoenner.backend.shared;

import org.springframework.web.reactive.function.client.ClientResponse;

public class BusinessCentralErrorResponseException extends RuntimeException {
    public final ClientResponse response;

    public BusinessCentralErrorResponseException(String message, ClientResponse response, String errorBody) {
        super(message + "\nBusiness Central responded with an error:\n" +
                "Status: " + response.statusCode().value() + "\n" +
                "Body: " + errorBody + "\n");
        this.response = response;
    }
}
