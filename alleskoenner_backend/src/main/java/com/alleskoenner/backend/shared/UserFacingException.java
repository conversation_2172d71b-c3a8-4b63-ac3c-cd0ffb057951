package com.alleskoenner.backend.shared;

import java.util.Objects;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatusCode;
import org.springframework.web.server.ResponseStatusException;

public class UserFacingException extends ResponseStatusException {
    public UserFacingException(HttpStatusCode status, String reason) {
        super(status, reason);
    }

    @NotNull
    @Override
    public String getMessage() {
        return Objects.requireNonNull(super.getReason());
    }
}
