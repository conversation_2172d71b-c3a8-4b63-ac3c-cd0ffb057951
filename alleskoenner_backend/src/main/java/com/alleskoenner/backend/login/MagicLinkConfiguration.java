package com.alleskoenner.backend.login;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "magic-link")
public class MagicLinkConfiguration {
    private String redirectUrl;
    private int tokenValidityMinutes = 15;
}
