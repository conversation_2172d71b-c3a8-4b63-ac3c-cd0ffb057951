package com.alleskoenner.backend.login;

import com.alleskoenner.backend.emails.MagicLinkEmailServiceInterface;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * Service for magic link authentication.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MagicLinkService {

    private final MagicLinkKeycloakService magicLinkKeycloakService;
    private final MagicLinkConfiguration magicLinkConfiguration;
    private final MagicLinkEmailServiceInterface magicLinkEmailService;

    /**
     * Creates and sends a magic link to the specified email address.
     *
     * @param email The email address to send the magic link to
     * @return A Mono that completes when the magic link is sent
     */
    public Mono<Void> createAndSendMagicLink(String email) {
        var redirectUri = magicLinkConfiguration.getRedirectUrl();
        final var tokenValidityMinutes = magicLinkConfiguration.getTokenValidityMinutes();

        return magicLinkKeycloakService.createMagicLink(email, redirectUri)
                .flatMap(magicLink -> Mono.fromRunnable(() -> magicLinkEmailService.sendMagicLinkEmail(email, magicLink, tokenValidityMinutes)))
                .doOnSuccess(_ -> log.info("Magic link created and sent to {}", email))
                .doOnError(error -> log.error("Failed to create and send magic link to {}: ", email, error))
                .then();

    }

}
