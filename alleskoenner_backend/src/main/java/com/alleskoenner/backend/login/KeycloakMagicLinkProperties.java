package com.alleskoenner.backend.login;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "keycloak.magic-link")
public class KeycloakMagicLinkProperties {
    private String tokenUrl;
    private String magicLinkUrl;
    private String clientId;
    private String username;
    private String password;
    private String magicLinkClientId;
}
