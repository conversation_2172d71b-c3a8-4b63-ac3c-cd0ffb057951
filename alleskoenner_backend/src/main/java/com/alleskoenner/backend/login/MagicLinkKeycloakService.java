package com.alleskoenner.backend.login;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class MagicLinkKeycloakService {

    private final WebClient.Builder webClientBuilder;
    private final KeycloakMagicLinkProperties props;

    public Mono<String> createMagicLink(String email, String redirectUri) {
        return getApiToken()
                .flatMap(token -> requestMagicLink(token, email, redirectUri));
    }

    private Mono<String> getApiToken() {
        return webClientBuilder.build()
                .post()
                .uri(props.getTokenUrl())
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(BodyInserters.fromFormData("grant_type", "password")
                        .with("client_id", props.getClientId())
                        .with("username", props.getUsername())
                        .with("password", props.getPassword()))
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(json -> json.get("access_token").asText());
    }

    private Mono<String> requestMagicLink(String token, String email, String redirectUri) {
        ObjectNode body = JsonNodeFactory.instance.objectNode();
        body.put("email", email);
        body.put("client_id", props.getMagicLinkClientId());
        body.put("redirect_uri", redirectUri);
        body.put("expiration_seconds", 3600);
        body.put("force_create", true);
        body.put("update_profile", false);
        body.put("update_password", false);
        body.put("send_email", false);

        return webClientBuilder.build()
                .post()
                .uri(props.getMagicLinkUrl())
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(body)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(json -> json.get("link").asText());
    }
}
