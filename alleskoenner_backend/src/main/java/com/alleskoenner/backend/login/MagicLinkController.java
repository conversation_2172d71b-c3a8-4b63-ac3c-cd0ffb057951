package com.alleskoenner.backend.login;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * Controller for magic link authentication.
 */
@CrossOrigin("http://localhost:3000/")
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class MagicLinkController {

    private final MagicLinkService magicLinkService;

    /**
     * Sends a magic link to the specified email address.
     *
     * @param request The email request containing the email address
     * @return A Mono that emits a ResponseEntity when the magic link is sent
     */
    @PostMapping("/magic-link-request")
    public Mono<ResponseEntity<Void>> sendMagicLink(@RequestBody EmailRequest request) {
        return magicLinkService.createAndSendMagicLink(request.getEmail())
                .then(Mono.just(ResponseEntity.ok().build()));
    }

}
