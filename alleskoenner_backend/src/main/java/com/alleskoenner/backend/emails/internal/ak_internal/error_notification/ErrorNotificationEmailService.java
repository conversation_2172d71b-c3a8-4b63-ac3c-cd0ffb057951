package com.alleskoenner.backend.emails.internal.ak_internal.error_notification;

import java.time.Duration;
import java.util.Arrays;
import java.util.stream.Collectors;
import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import com.alleskoenner.backend.emails.ErrorNotificationEmailValues;
import com.alleskoenner.backend.emails.internal.AzureEmailClient;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.azure.core.util.polling.AsyncPollResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.Disposable;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@Service
@AllArgsConstructor
public class ErrorNotificationEmailService implements ErrorNotificationEmailServiceInterface {

    private final AzureEmailClient azureEmailClient;
    private final AzureEmailConfiguration azureEmailConfiguration;
    private final ErrorNotificationEmailCreator errorNotificationEmailCreator;
    private static final Duration SENDING_EMAIL_TIMEOUT = Duration.ofSeconds(30);

    @Override
    public Disposable sendErrorNotificationEmail(Throwable throwable, String... additionalMessages) {
        return createErrorNotificationEmailMono(throwable, additionalMessages)
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe();
    }

    Mono<Void> createErrorNotificationEmailMono(Throwable throwable, String... additionalMessages) {
        if (!azureEmailConfiguration.getInternalErrorNotification().isEnabled()) {
            return Mono.empty();
        }

        var additionalMessagesText = additionalMessages.length > 0
                ? "\n\nAdditional messages:\n" + String.join("\n", additionalMessages)
                : "";

        var errorNotificationEmailValues = new ErrorNotificationEmailValues(
                "Error message:\n" + throwable.getMessage() + additionalMessagesText,
                Arrays.stream(throwable.getStackTrace())
                        .map(StackTraceElement::toString)
                        .collect(Collectors.joining("\n"))
        );

        var recipientAddresses = Arrays.stream(azureEmailConfiguration.getInternalErrorNotification().getRecipientAddresses()).toList();

        if (recipientAddresses.isEmpty()) {
            log.error("Error while trying to send error notification email", new IllegalStateException("No recipient addresses configured for error notification emails"));
            return Mono.empty();
        }

        var message = errorNotificationEmailCreator.createEmailMessage(
                errorNotificationEmailValues,
                azureEmailConfiguration.getSenderAddress(),
                recipientAddresses
        );

        return Mono.fromRunnable(() -> azureEmailClient.getEmailClient().beginSend(message)
                        .last()
                        .timeout(SENDING_EMAIL_TIMEOUT)
                        .flatMap(AsyncPollResponse::getFinalResult)
                        .subscribe())
                .doOnSuccess(unused -> log.info("Error notification email sent successfully"))
                .doOnError(error -> log.error("Failed to send error notification email: ", error))
                .then();
    }
}
