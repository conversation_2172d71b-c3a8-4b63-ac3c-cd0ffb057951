package com.alleskoenner.backend.emails.internal.appointment_confirmation;

import java.time.Duration;
import com.alleskoenner.backend.emails.AppointmentConfirmationEmailServiceInterface;
import com.alleskoenner.backend.emails.AsapAppointmentConfirmationEmailValues;
import com.alleskoenner.backend.emails.ScheduledAppointmentConfirmationEmailValues;
import com.alleskoenner.backend.emails.internal.AzureEmailClient;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.azure.communication.email.models.EmailSendResult;
import com.azure.core.util.polling.AsyncPollResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class AppointmentConfirmationEmailService implements AppointmentConfirmationEmailServiceInterface {

    private final AzureEmailClient azureEmailClient;
    private final AzureEmailConfiguration azureEmailConfiguration;
    private final AppointmentConfirmationEmailCreator appointmentConfirmationEmailCreator;
    private static final Duration SENDING_EMAIL_TIMEOUT = Duration.ofSeconds(30);

    @Override
    public Mono<EmailSendResult> sendAsapAppointmentConfirmationEmail(AsapAppointmentConfirmationEmailValues values) {

        var message = appointmentConfirmationEmailCreator.createAppointmentEmailMessage(values, azureEmailConfiguration.getSenderAddress());

        return azureEmailClient.getEmailClient().beginSend(message)
                .last()
                .timeout(SENDING_EMAIL_TIMEOUT)
                .flatMap(AsyncPollResponse::getFinalResult);
    }

    @Override
    public Mono<EmailSendResult> sendScheduledAppointmentConfirmationEmail(ScheduledAppointmentConfirmationEmailValues values) {

        var message = appointmentConfirmationEmailCreator.createAppointmentEmailMessage(values, azureEmailConfiguration.getSenderAddress());

        return azureEmailClient.getEmailClient().beginSend(message)
                .last()
                .timeout(SENDING_EMAIL_TIMEOUT)
                .flatMap(AsyncPollResponse::getFinalResult);
    }
}