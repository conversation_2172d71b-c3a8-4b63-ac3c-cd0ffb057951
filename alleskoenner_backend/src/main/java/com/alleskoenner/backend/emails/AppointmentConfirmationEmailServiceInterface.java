package com.alleskoenner.backend.emails;

import com.azure.communication.email.models.EmailSendResult;
import reactor.core.publisher.Mono;

public interface AppointmentConfirmationEmailServiceInterface {
    Mono<EmailSendResult> sendAsapAppointmentConfirmationEmail(AsapAppointmentConfirmationEmailValues values);

    Mono<EmailSendResult> sendScheduledAppointmentConfirmationEmail(ScheduledAppointmentConfirmationEmailValues values);

}