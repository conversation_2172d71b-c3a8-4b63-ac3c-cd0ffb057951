package com.alleskoenner.backend.emails.internal.invoice;

import java.time.Duration;
import com.alleskoenner.backend.emails.InvoiceEmailServiceInterface;
import com.alleskoenner.backend.emails.InvoiceEmailValues;
import com.alleskoenner.backend.emails.internal.AzureEmailClient;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.azure.communication.email.models.EmailSendResult;
import com.azure.core.util.polling.AsyncPollResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@AllArgsConstructor
public class InvoiceEmailService implements InvoiceEmailServiceInterface {

    private final AzureEmailClient azureEmailClient;
    private final AzureEmailConfiguration azureEmailConfiguration;
    private final InvoiceEmailCreator invoiceEmailCreator;
    private static final Duration SENDING_EMAIL_TIMEOUT = Duration.ofSeconds(30);

    @Override
    public Mono<EmailSendResult> sendInvoiceEmail(InvoiceEmailValues invoiceEmailValues) {
        var message = invoiceEmailCreator.createEmailMessage(invoiceEmailValues, azureEmailConfiguration.getSenderAddress());

        return azureEmailClient.getEmailClient().beginSend(message)
                .last()
                .timeout(SENDING_EMAIL_TIMEOUT)
                .flatMap(AsyncPollResponse::getFinalResult);
    }

}