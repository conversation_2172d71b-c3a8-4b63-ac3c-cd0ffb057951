package com.alleskoenner.backend.emails.internal.order_confirmation;

import java.io.IOException;
import java.util.Collections;

import com.alleskoenner.backend.emails.ConfirmationEmailValues;
import com.alleskoenner.backend.emails.ConfirmationEmailValuesRenderer;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.azure.communication.email.models.EmailAddress;
import com.azure.communication.email.models.EmailAttachment;
import com.azure.communication.email.models.EmailMessage;
import com.azure.core.util.BinaryData;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import static org.springframework.http.MediaType.APPLICATION_PDF_VALUE;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class OrderConfirmationEmailCreator {

    private final AzureEmailConfiguration config;

    private static final String TERMS_AND_CONDITIONS_FILE_DISPLAY_NAME = "Allgemeine_Geschäftsbedingungen.pdf";

    public EmailMessage createEmailMessage(ConfirmationEmailValues emailValues, String senderAddress) {
        return new EmailMessage()
                .setSenderAddress(senderAddress)
                .setSubject("Wir haben Ihren Auftrag erhalten - Vorgangsnummer: " + emailValues.orderNumber())
                .setBodyPlainText("Vielen Dank für Ihren Auftrag mit der Vorgangsnummer: " + emailValues.orderNumber())
                .setBodyHtml(ConfirmationEmailValuesRenderer.of().execute(emailValues))
                .setToRecipients(Collections.singletonList(new EmailAddress(emailValues.email())))
                .setAttachments(createTermsAndConditionsAttachment());
    }

    public EmailAttachment createTermsAndConditionsAttachment() {
        var termsAndConditionsFileName = config.getTermsAndConditionsFileName();
        Resource resource = new ClassPathResource(termsAndConditionsFileName);
        try (var inputStream = resource.getInputStream()) {
            return new EmailAttachment(
                    TERMS_AND_CONDITIONS_FILE_DISPLAY_NAME,
                    APPLICATION_PDF_VALUE,
                    BinaryData.fromStream(inputStream)
            );
        } catch (IOException e) {
            throw new RuntimeException("Could not find or read AGB pdf file named \"" + termsAndConditionsFileName + "\".", e);
        }
    }
}
