package com.alleskoenner.backend.emails;

import io.jstach.jstache.JStache;

//        todo: Change template according to new design
//        todo: Email address is not yet shown in template.
//        todo: addressLine2 and phoneNumber is optional. This will also have to be reflected in the template.
@JStache(path = "orderRequestConfirmation.mustache")
public record ConfirmationEmailValues(
        String orderNumber,
        String displayName,
        String addressLine1,
//        String addressLine2,
        String postalCode,
        String city,
//        String phoneNumber,
        String email,
        String taskDescription
) {
}
