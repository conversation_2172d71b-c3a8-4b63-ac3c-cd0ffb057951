package com.alleskoenner.backend.emails.internal.magic_link;

import java.time.Duration;

import com.alleskoenner.backend.emails.MagicLinkEmailServiceInterface;
import com.alleskoenner.backend.emails.internal.AzureEmailClient;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.azure.core.util.polling.AsyncPollResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * Service for sending magic link emails.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class Magic<PERSON>inkEmailService implements MagicLinkEmailServiceInterface {

    private final AzureEmailClient azureEmailClient;
    private final AzureEmailConfiguration azureEmailConfiguration;
    private final MagicLinkEmailCreator magicLinkEmailCreator;
    private static final Duration SENDING_EMAIL_TIMEOUT = Duration.ofSeconds(30);

    /**
     * Sends a magic link email.
     *
     * @param email                The recipient's email address
     * @param magicLink            The magic link URL
     * @param tokenValidityMinutes The validity of the magic link in minutes
     */
    @Override
    public void sendMagicLinkEmail(String email, String magicLink, int tokenValidityMinutes) {
        // TODO: consider removing the magic link from log
        log.info("Sending email to {} with magic link {}", email, magicLink);
        createMagicLinkEmailMono(email, magicLink, tokenValidityMinutes)
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe();
    }

    /**
     * Creates a Mono for sending a magic link email.
     *
     * @param email                The recipient's email address
     * @param magicLink            The magic link URL
     * @param tokenValidityMinutes
     * @return A Mono that completes when the email is sent
     */
    Mono<Void> createMagicLinkEmailMono(String email, String magicLink, int tokenValidityMinutes) {
        if (azureEmailConfiguration.getMagicLinkEmail() == null || !azureEmailConfiguration.getMagicLinkEmail().isEnabled()) {
            log.warn("Magic link emails are disabled");
            return Mono.empty();
        }

        var magicLinkEmailValues = new MagicLinkEmailValues(
                email,
                magicLink,
                tokenValidityMinutes
        );

        var message = magicLinkEmailCreator.createEmailMessage(
                magicLinkEmailValues,
                azureEmailConfiguration.getSenderAddress(),
                email
        );

        return Mono.fromRunnable(() -> azureEmailClient.getEmailClient().beginSend(message)
                        .last()
                        .timeout(SENDING_EMAIL_TIMEOUT)
                        .flatMap(AsyncPollResponse::getFinalResult)
                        .subscribe())
                .doOnSuccess(_ -> log.info("Magic link email sent successfully to {}", email))
                .doOnError(error -> log.error("Failed to send magic link email to {}: ", email, error))
                .then();
    }
}
