package com.alleskoenner.backend.emails;

/**
 * Interface for sending magic link emails.
 */
public interface MagicLinkEmailServiceInterface {
    /**
     * Sends a magic link email to the specified address.
     *
     * @param email                The recipient's email address
     * @param magicLink            The magic link URL
     * @param tokenValidityMinutes
     */
    void sendMagicLinkEmail(String email, String magicLink, int tokenValidityMinutes);
}
