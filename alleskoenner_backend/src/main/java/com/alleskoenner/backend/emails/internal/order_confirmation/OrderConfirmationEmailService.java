package com.alleskoenner.backend.emails.internal.order_confirmation;

import com.alleskoenner.backend.emails.ConfirmationEmailServiceInterface;
import com.alleskoenner.backend.emails.ConfirmationEmailValues;
import com.alleskoenner.backend.emails.internal.AzureEmailClient;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.azure.communication.email.models.EmailMessage;
import com.azure.communication.email.models.EmailSendResult;
import com.azure.core.util.polling.AsyncPollResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import java.time.Duration;

@Service
@AllArgsConstructor
public class OrderConfirmationEmailService implements ConfirmationEmailServiceInterface {

    private final AzureEmailClient azureEmailClient;
    private final AzureEmailConfiguration azureEmailConfiguration;
    private final OrderConfirmationEmailCreator confirmationEmailCreator;
    private static final Duration SENDING_EMAIL_TIMEOUT = Duration.ofSeconds(30);

    @Override
    public Mono<EmailSendResult> sendConfirmationEmail(ConfirmationEmailValues confirmationEmailValues) {

        EmailMessage message = confirmationEmailCreator.createEmailMessage(confirmationEmailValues, azureEmailConfiguration.getSenderAddress());

        return azureEmailClient.getEmailClient().beginSend(message)
                .last()
                .timeout(SENDING_EMAIL_TIMEOUT)
                .flatMap(AsyncPollResponse::getFinalResult);
    }
}
