package com.alleskoenner.backend.emails.internal.ak_internal.business_notification;

import java.util.List;
import com.alleskoenner.backend.emails.BusinessNotificationEmailValues;
import com.alleskoenner.backend.emails.BusinessNotificationEmailValuesRenderer;
import com.azure.communication.email.models.EmailAddress;
import com.azure.communication.email.models.EmailMessage;
import org.springframework.stereotype.Component;

@Component
class BusinessNotificationEmailCreator {

    EmailMessage createEmailMessage(BusinessNotificationEmailValues values, String senderAddress, List<String> recipientAddresses) {
        return new EmailMessage()
                .setSenderAddress(senderAddress)
                .setSubject("Alleskönner24 Business Notification")
                .setBodyPlainText(values.text())
                .setBodyHtml(BusinessNotificationEmailValuesRenderer.of().execute(values))
                .setToRecipients(recipientAddresses.stream().map(EmailAddress::new).toList());
    }
}
