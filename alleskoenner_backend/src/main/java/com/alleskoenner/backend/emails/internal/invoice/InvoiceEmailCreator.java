package com.alleskoenner.backend.emails.internal.invoice;

import java.util.Collections;
import com.alleskoenner.backend.emails.InvoiceEmailValues;
import com.alleskoenner.backend.emails.InvoiceEmailValuesRenderer;
import com.azure.communication.email.models.EmailAddress;
import com.azure.communication.email.models.EmailAttachment;
import com.azure.communication.email.models.EmailMessage;
import com.azure.core.util.BinaryData;
import org.springframework.stereotype.Component;

@Component
class InvoiceEmailCreator {

    EmailMessage createEmailMessage(InvoiceEmailValues values, String senderAddress) {
        var binaryData = BinaryData.fromBytes(values.pdfAttachment());
        var attachment = new EmailAttachment(
                "Rechnung_" + values.invoiceNumber() + ".pdf",
                "application/pdf",
                binaryData
        );

        return new EmailMessage()
                .setSenderAddress(senderAddress)
                .setSubject("Ihre Rechnung - Rechnungsnummer: " + values.invoiceNumber())
                .setBodyPlainText("Vielen Dank für Ihren Auftrag. Anbei finden Sie Ihre Rechnung mit der Rechnungsnummer: " + values.invoiceNumber())
                .setBodyHtml(InvoiceEmailValuesRenderer.of().execute(values))
                .setToRecipients(Collections.singletonList(new EmailAddress(values.recipientEmail())))
                .setAttachments(Collections.singletonList(attachment));
    }
}
