package com.alleskoenner.backend.emails.internal.ak_internal.error_notification;

import java.util.List;
import com.alleskoenner.backend.emails.ErrorNotificationEmailValues;
import com.alleskoenner.backend.emails.ErrorNotificationEmailValuesRenderer;
import com.azure.communication.email.models.EmailAddress;
import com.azure.communication.email.models.EmailMessage;
import org.springframework.stereotype.Component;

@Component
class ErrorNotificationEmailCreator {

    EmailMessage createEmailMessage(ErrorNotificationEmailValues values, String senderAddress, List<String> recipientAddresses) {
        return new EmailMessage()
                .setSenderAddress(senderAddress)
                .setSubject("Alleskönner24 Error Notification")
                .setBodyPlainText(values.text() + "\n\nStacktrace:\n" + values.stacktrace())
                .setBodyHtml(ErrorNotificationEmailValuesRenderer.of().execute(values))
                .setToRecipients(recipientAddresses.stream().map(EmailAddress::new).toList());
    }
}
