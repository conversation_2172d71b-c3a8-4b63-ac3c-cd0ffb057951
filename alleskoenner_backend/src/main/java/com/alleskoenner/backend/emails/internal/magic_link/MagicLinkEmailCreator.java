package com.alleskoenner.backend.emails.internal.magic_link;

import java.util.List;

import com.azure.communication.email.models.EmailAddress;
import com.azure.communication.email.models.EmailMessage;
import org.springframework.stereotype.Component;

@Component
public class MagicLinkEmailCreator {

    public EmailMessage createEmailMessage(MagicLinkEmailValues values, String senderAddress, String recipientAddress) {
        return new EmailMessage()
                .setSenderAddress(senderAddress)
                .setSubject("Ihr Login-Link für DeinHandwerker365.de")
                .setBodyPlainText("Klicken Sie auf diesen Link, um sich anzumelden: " + values.magicLink())
                .setBodyHtml(MagicLinkEmailValuesRenderer.of().execute(values))
                .setToRecipients(List.of(new EmailAddress(recipientAddress)));
    }
}
