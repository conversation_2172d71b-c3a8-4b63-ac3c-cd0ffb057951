package com.alleskoenner.backend.emails.internal.ak_internal.business_notification;

import java.time.Duration;
import java.util.Arrays;
import com.alleskoenner.backend.emails.BusinessNotificationEmailServiceInterface;
import com.alleskoenner.backend.emails.BusinessNotificationEmailValues;
import com.alleskoenner.backend.emails.internal.AzureEmailClient;
import com.alleskoenner.backend.emails.internal.AzureEmailConfiguration;
import com.azure.core.util.polling.AsyncPollResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.Disposable;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@Service
@AllArgsConstructor
public class BusinessNotificationEmailService implements BusinessNotificationEmailServiceInterface {

    private final AzureEmailClient azureEmailClient;
    private final AzureEmailConfiguration azureEmailConfiguration;
    private final BusinessNotificationEmailCreator businessNotificationEmailCreator;
    private static final Duration SENDING_EMAIL_TIMEOUT = Duration.ofSeconds(30);

    @Override
    public Disposable sendBusinessNotificationEmail(String notificationText) {
        return createBusinessNotificationEmailMono(notificationText)
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe();
    }

    Mono<Void> createBusinessNotificationEmailMono(String notificationText) {
        if (!azureEmailConfiguration.getInternalBusinessNotification().isEnabled()) {
            return Mono.empty();
        }

        var businessNotificationEmailValues = new BusinessNotificationEmailValues(notificationText);

        var recipientAddresses = Arrays.stream(azureEmailConfiguration.getInternalBusinessNotification().getRecipientAddresses()).toList();

        if (recipientAddresses.isEmpty()) {
            log.error("Error while trying to send business notification email", new IllegalStateException("No recipient addresses configured for business notification emails"));
            return Mono.empty();
        }

        var message = businessNotificationEmailCreator.createEmailMessage(
                businessNotificationEmailValues,
                azureEmailConfiguration.getSenderAddress(),
                recipientAddresses
        );

        return Mono.fromRunnable(() -> azureEmailClient.getEmailClient().beginSend(message)
                        .last()
                        .timeout(SENDING_EMAIL_TIMEOUT)
                        .flatMap(AsyncPollResponse::getFinalResult)
                        .subscribe())
                .doOnSuccess(unused -> log.info("Business notification email sent successfully"))
                .doOnError(error -> log.error("Failed to send business notification email: ", error))
                .then();
    }
}
