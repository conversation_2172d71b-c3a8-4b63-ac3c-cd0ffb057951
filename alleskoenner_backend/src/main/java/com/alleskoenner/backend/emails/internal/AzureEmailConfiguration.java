package com.alleskoenner.backend.emails.internal;

import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "azure.email")
public class AzureEmailConfiguration {
    private String connectionString;
    private String senderAddress;
    private MagicLinkEmail magicLinkEmail;
    private InternalBusinessNotification internalBusinessNotification;
    private InternalErrorNotification internalErrorNotification;
    private String termsAndConditionsFileName = "AGB.pdf";

    @Getter
    @Setter
    public static class MagicLinkEmail {
        private boolean enabled = true;
    }

    @Getter
    @Setter
    public static class InternalBusinessNotification {
        private boolean enabled = false;
        private String[] recipientAddresses;
    }

    @Getter
    @Setter
    public static class InternalErrorNotification {
        private boolean enabled = false;
        private String[] recipientAddresses;
    }

    @PostConstruct
    private void logIfEmailsAreDisabled() {
        if (magicLinkEmail == null || !magicLinkEmail.isEnabled()) {
            log.warn("Magic link email notification is disabled.");
        }
        if (internalBusinessNotification == null || !internalBusinessNotification.isEnabled()) {
            log.warn("Business email notification is disabled.");
        }
        if (internalErrorNotification == null || !internalErrorNotification.isEnabled()) {
            log.warn("Error email notification is disabled.");
        }
    }
}
