package com.alleskoenner.backend.emails.internal.appointment_confirmation;

import java.util.Collections;
import com.alleskoenner.backend.emails.AsapAppointmentConfirmationEmailValues;
import com.alleskoenner.backend.emails.ScheduledAppointmentConfirmationEmailValues;
import com.azure.communication.email.models.EmailAddress;
import com.azure.communication.email.models.EmailMessage;
import io.jstach.jstachio.JStachio;
import org.springframework.stereotype.Service;

@Service
public class AppointmentConfirmationEmailCreator {

    private EmailMessage createEmailMessage(String html, String recipient, String orderNumber, String senderAddress) {
        return new EmailMessage()
                .setSenderAddress(senderAddress)
                .setSubject("Dein Auftrag wurde angenommen - Auftragsnummer: " + orderNumber)
                .setBodyPlainText("Vielen Dank für Ihren Auftrag mit der Auftragsnummer: " + orderNumber)
                .setBodyHtml(html)
                .setToRecipients(Collections.singletonList(new EmailAddress(recipient)));
    }

    public EmailMessage createAppointmentEmailMessage(AsapAppointmentConfirmationEmailValues values, String senderAddress) {
        var html = JStachio.render(values);
        return createEmailMessage(html, values.email(), values.orderNumber(), senderAddress);
    }

    public EmailMessage createAppointmentEmailMessage(ScheduledAppointmentConfirmationEmailValues values, String senderAddress) {
        var html = JStachio.render(values);
        return createEmailMessage(html, values.email(), values.orderNumber(), senderAddress);
    }
}