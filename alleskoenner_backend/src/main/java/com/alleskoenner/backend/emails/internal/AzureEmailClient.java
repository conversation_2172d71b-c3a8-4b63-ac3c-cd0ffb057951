package com.alleskoenner.backend.emails.internal;

import com.azure.communication.email.EmailAsyncClient;
import com.azure.communication.email.EmailClientBuilder;
import lombok.Getter;
import org.springframework.stereotype.Service;

@Service
public class AzureEmailClient {

    private final AzureEmailConfiguration azureEmailConfiguration;

    @Getter
    private final EmailAsyncClient emailClient;

    public AzureEmailClient(AzureEmailConfiguration azureEmailConfiguration) {
        this.azureEmailConfiguration = azureEmailConfiguration;
        this.emailClient = new EmailClientBuilder()
                .connectionString(azureEmailConfiguration.getConnectionString())
                .buildAsyncClient();
    }
}
