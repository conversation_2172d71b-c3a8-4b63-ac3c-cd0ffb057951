package com.alleskoenner.backend.customer_order.internal.order;

import java.util.stream.Collectors;

import com.alleskoenner.backend.shared.UserFacingErrorMessage;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.support.WebExchangeBindException;
import reactor.core.publisher.Mono;

@CrossOrigin("http://localhost:3000/")
@RestController
@RequestMapping("/order")
public class OrderController {

    private final OrderService orderService;

    // This maps to the Keycloak-role "customer_impersonation" in the ressource_access of JWT token
    // See SecurityConfig for the extracting role from the JWT token
    private static final String ROLE_CUSTOMER_IMPERSONATION = "ROLE_CUSTOMER_IMPERSONATION";

    public OrderController(OrderService orderService) {
        this.orderService = orderService;
    }

    @PostMapping(consumes = "application/json", produces = "application/json")
    public Mono<ResponseEntity<OrderSummary>> createOrder(@AuthenticationPrincipal JwtAuthenticationToken principal,
            @Validated @RequestBody OrderDetailsRequest orderDetailsRequest) {

        return orderService.createOrder(
                        new OrderDetails(
                                orderDetailsRequest.getShipToName(),
                                orderDetailsRequest.getShipToAddressLine1(),
                                orderDetailsRequest.getShipToAddressLine2(),
                                orderDetailsRequest.getShipToCity(),
                                orderDetailsRequest.getShipToPostalCode(),
                                orderDetailsRequest.getShipToEmail(),
                                orderDetailsRequest.getShipToPhoneNumber(),
                                orderDetailsRequest.getBillToName(),
                                orderDetailsRequest.getBillToAddressLine1(),
                                orderDetailsRequest.getBillToAddressLine2(),
                                orderDetailsRequest.getBillToCity(),
                                orderDetailsRequest.getBillToPostalCode(),
                                orderDetailsRequest.getBillToEmail(),
                                orderDetailsRequest.getBillToPhoneNumber(),
                                orderDetailsRequest.getCustomerType(),
                                orderDetailsRequest.getTaskCategory(),
                                orderDetailsRequest.getTaskDescription(),
                                orderDetailsRequest.getIsConditionsChecked(),
                                orderDetailsRequest.getIsAsSoonAsPossible(),
                                orderDetailsRequest.getAppointmentRequestDate(),
                                orderDetailsRequest.getRequestedHandymanSkill(),
                                getOrderEmail(principal, orderDetailsRequest))
                )
                .map(orderSummary -> ResponseEntity.ok().body(orderSummary));
    }

    @ExceptionHandler(WebExchangeBindException.class)
    public ResponseEntity<UserFacingErrorMessage> handleValidationError(WebExchangeBindException exception) {
        var message = exception.getBindingResult()
                .getAllErrors()
                .stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining(" "));
        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(new UserFacingErrorMessage(message));
    }

    @NotNull
    private static OrderEmail getOrderEmail(JwtAuthenticationToken principal, OrderDetailsRequest orderDetailsRequest) {
        boolean hasCustomerImpersonationRole = principal.getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals(ROLE_CUSTOMER_IMPERSONATION));

        if (hasCustomerImpersonationRole) {
            return new OrderEmail(orderDetailsRequest.getEmail());
        } else {
            return new OrderEmail(principal.getToken().getClaim("email"));
        }
    }
}
