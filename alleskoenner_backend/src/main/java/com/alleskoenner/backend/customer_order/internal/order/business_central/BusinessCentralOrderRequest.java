package com.alleskoenner.backend.customer_order.internal.order.business_central;

import java.time.ZonedDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

public record BusinessCentralOrderRequest(
        String shipToName,
        String shipToAddressLine1,
        String shipToAddressLine2,
        String shipToCity,
        String shipToPostalCode,
        String shipToEmail,
        String shipToPhoneNumber,
        String billToName,
        String billToAddressLine1,
        String billToAddressLine2,
        String billToCity,
        String billToPostalCode,
        String billToEmail,
        String billToPhoneNumber,
        String email,
        String customerType,
        String taskCategory,
        String taskDescription,
        Boolean isConditionsChecked,
        Boolean asSoonAsPossible,
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
        ZonedDateTime appointmentRequestDateTime,
        String requestedHandymanSkill
) {
}
