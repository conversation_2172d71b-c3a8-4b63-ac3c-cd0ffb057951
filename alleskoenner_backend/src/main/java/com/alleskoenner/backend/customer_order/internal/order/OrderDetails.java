package com.alleskoenner.backend.customer_order.internal.order;

import java.time.ZonedDateTime;

public record OrderDetails(
        String shipToName,
        String shipToAddressLine1,
        String shipToAddressLine2,
        String shipToCity,
        String shipToPostalCode,
        String shipToEmail,
        String shipToPhoneNumber,
        String billToName,
        String billToAddressLine1,
        String billToAddressLine2,
        String billToCity,
        String billToPostalCode,
        String billToEmail,
        String billToPhoneNumber,
        String customerType,
        String taskCategory,
        String taskDescription,
        Boolean isConditionsChecked,
        Boolean isAsSoonAsPossible,
        ZonedDateTime appointmentRequestDate,
        String requestedHandymanSkill,
        OrderEmail orderEmail
) {

}
