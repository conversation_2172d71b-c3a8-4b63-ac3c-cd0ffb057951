package com.alleskoenner.backend.customer_order.internal.order.business_central;

import com.alleskoenner.backend.customer_order.internal.business_central.BusinessCentralConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BusinessCentralUrlBuilder {

    private final BusinessCentralConfig config;

    public BusinessCentralUrlBuilder(BusinessCentralConfig config) {
        this.config = config;
    }

    String buildOrderRequestUrl() {
        return "v2.0/" + config.getBcEnvironment() + "/api/ITV/appointments/v2.0/companies(" + config.getBcCompanyId() + ")/orders";
    }
}
