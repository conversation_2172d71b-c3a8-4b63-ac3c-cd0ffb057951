package com.alleskoenner.backend.customer_order.internal.user_details.business_central;

import com.alleskoenner.backend.customer_order.internal.business_central.BusinessCentralConfig;
import com.alleskoenner.backend.shared.LoginEmail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BusinessCentralCustomerUrlBuilder {

    private final BusinessCentralConfig config;

    public BusinessCentralCustomerUrlBuilder(BusinessCentralConfig config) {
        this.config = config;
    }

    String buildCustomerRequestUrl(LoginEmail customerEmail) {
        return "v2.0/" + config.getBcEnvironment() + "/api/v2.0/companies(" + config.getBcCompanyId() + ")/customers?$filter=email eq '" + customerEmail + "'";
    }
}
