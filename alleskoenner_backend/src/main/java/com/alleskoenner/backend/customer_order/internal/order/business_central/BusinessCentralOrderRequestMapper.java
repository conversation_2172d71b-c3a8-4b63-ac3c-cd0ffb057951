package com.alleskoenner.backend.customer_order.internal.order.business_central;


import com.alleskoenner.backend.customer_order.internal.order.OrderDetails;
import org.springframework.stereotype.Service;

@Service
public class BusinessCentralOrderRequestMapper {

    public BusinessCentralOrderRequest mapOrderDetailsToBusinessCentralOrderRequest(OrderDetails orderDetails) {
        return new BusinessCentralOrderRequest(
                orderDetails.shipToName(),
                orderDetails.shipToAddressLine1(),
                orderDetails.shipToAddressLine2(),
                orderDetails.shipToCity(),
                orderDetails.shipToPostalCode(),
                orderDetails.shipToEmail(),
                orderDetails.shipToPhoneNumber(),
                orderDetails.billToName(),
                orderDetails.billToAddressLine1(),
                orderDetails.billToAddressLine2(),
                orderDetails.billToCity(),
                orderDetails.billToPostalCode(),
                orderDetails.billToEmail(),
                orderDetails.billToPhoneNumber(),
                orderDetails.orderEmail().email(),
                orderDetails.customerType(),
                orderDetails.taskCategory(),
                orderDetails.taskDescription(),
                orderDetails.isConditionsChecked(),
                orderDetails.isAsSoonAsPossible(),
                orderDetails.appointmentRequestDate(),
                orderDetails.requestedHandymanSkill()
        );
    }

}
