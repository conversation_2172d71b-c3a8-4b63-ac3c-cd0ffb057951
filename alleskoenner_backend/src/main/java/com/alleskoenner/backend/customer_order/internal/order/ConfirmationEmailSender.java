package com.alleskoenner.backend.customer_order.internal.order;

import com.alleskoenner.backend.customer_order.internal.order.business_central.BusinessCentralOrderResponse;
import com.alleskoenner.backend.emails.ConfirmationEmailServiceInterface;
import com.alleskoenner.backend.emails.ConfirmationEmailValues;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@AllArgsConstructor
public class ConfirmationEmailSender {

    private final ConfirmationEmailServiceInterface confirmationEmailServiceInterface;

    void sendConfirmationEmail(OrderDetails orderDetails, BusinessCentralOrderResponse businessCentralOrderResponse) {

        ConfirmationEmailValues confirmationEmailValues = createConfirmationEmailValues(orderDetails, businessCentralOrderResponse);

        confirmationEmailServiceInterface.sendConfirmationEmail(confirmationEmailValues)
                .doOnSuccess(emailSendResult -> log.debug("Email with Id {} successfully sent", emailSendResult.getId()))
                .onErrorResume(throwable -> {
                    log.error("Error while sending email with message: {}", throwable.getMessage(), throwable);
                    return Mono.empty();
                })
                .subscribe();
    }

    static ConfirmationEmailValues createConfirmationEmailValues(OrderDetails orderDetails, BusinessCentralOrderResponse businessCentralOrderResponse) {
        return new ConfirmationEmailValues(
                businessCentralOrderResponse.orderNumber(),
                orderDetails.shipToName(),
                orderDetails.shipToAddressLine1(),
                orderDetails.shipToPostalCode(),
                orderDetails.shipToCity(),
                orderDetails.orderEmail().email(),
                orderDetails.taskDescription()
        );
    }
}
