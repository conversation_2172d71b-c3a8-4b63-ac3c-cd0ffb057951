package com.alleskoenner.backend.customer_order.internal.user_details;

import com.alleskoenner.backend.customer_order.internal.user_details.business_central.BusinessCentralCustomerClient;
import com.alleskoenner.backend.customer_order.internal.user_details.business_central.BusinessCentralCustomerResponseValue;
import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import static org.springframework.http.HttpStatus.NOT_FOUND;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class UserDetailsService {

    private final BusinessCentralCustomerClient bcClient;
    private final ErrorNotificationEmailServiceInterface errorNotificationEmailService;

    public Mono<UserDetailResponse> getUserDetails(LoginEmail customerEmail) {
        return bcClient.sendCustomerRequest(customerEmail)

                .flatMap(customerBcResponseValues -> validateAndReturnResponse(customerEmail, customerBcResponseValues))

                .map(UserDetailResponse::from)

                .doOnError(e -> {
                    var errorMessage = String.format("Error occurred while getting user details for customer %s", customerEmail);
                    errorNotificationEmailService.sendErrorNotificationEmail(e);
                    log.error(errorMessage, e);
                });
    }

    @NotNull
    private static Mono<BusinessCentralCustomerResponseValue> validateAndReturnResponse(LoginEmail customerEmail, List<BusinessCentralCustomerResponseValue> customerBcResponseValues) {
        final var responseSize = customerBcResponseValues.size();
        if (responseSize == 0) {
            log.info("Tried to fetch customer with email {}, but no customer was found. Customer probably was not yet created.", customerEmail);
            return Mono.error(new UserFacingException(NOT_FOUND, "Customer not found. Probably not yet created."));
        }
        if (responseSize > 1) {
            log.error("Getting customer expected to have exactly 1 result, but was {}. Customer Email: {}", responseSize, customerEmail);
            return Mono.error(new RuntimeException("Expected exactly 1 customer, but got " + responseSize));
        }
        return Mono.just(customerBcResponseValues.getFirst());
    }

}
