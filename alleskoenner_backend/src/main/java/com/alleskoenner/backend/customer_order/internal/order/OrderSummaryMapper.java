package com.alleskoenner.backend.customer_order.internal.order;

import com.alleskoenner.backend.customer_order.internal.order.business_central.BusinessCentralOrderResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class OrderSummaryMapper {

    public OrderSummary mapBusinessCentralResponseToOrderSummary(OrderDetails orderDetails, BusinessCentralOrderResponse orderResponse) {
        return new OrderSummary(
                orderResponse.orderNumber(),
                orderDetails.shipToName(),
                orderDetails.shipToAddressLine1(),
                orderDetails.shipToAddressLine2(),
                orderDetails.shipToCity(),
                orderDetails.shipToPostalCode(),
                orderDetails.shipToEmail(),
                orderDetails.shipToPhoneNumber(),
                orderDetails.billToName(),
                orderDetails.billToAddressLine1(),
                orderDetails.billToAddressLine2(),
                orderDetails.billToCity(),
                orderDetails.billToPostalCode(),
                orderDetails.billToEmail(),
                orderDetails.billToPhoneNumber(),
                orderDetails.orderEmail().email(),
                orderDetails.customerType(),
                orderDetails.taskCategory(),
                orderDetails.taskDescription(),
                orderDetails.isConditionsChecked()
        );
    }
}
