package com.alleskoenner.backend.customer_order.internal.user_details.business_central;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.LocalDate;

public record BusinessCentralCustomerResponseValue(
        @JsonProperty("@odata.etag")
        String odataEtag,
        String id,
        String number,
        String displayName,
        String type,
        String addressLine1,
        String addressLine2,
        String city,
        String state,
        String country,
        String postalCode,
        String phoneNumber,
        String email,
        String website,
        String salespersonCode,
        BigDecimal balanceDue,
        BigDecimal creditLimit,
        Boolean taxLiable,
        String taxAreaId,
        String taxAreaDisplayName,
        String taxRegistrationNumber,
        String currencyId,
        String currencyCode,
        String paymentTermsId,
        String shipmentMethodId,
        String paymentMethodId,
        String blocked,
        LocalDate lastModifiedDateTime
) {
}