package com.alleskoenner.backend.customer_order.internal.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class OrderSummary {
    private String salesOrderNumber;
    private String shipToName;
    private String shipToAddressLine1;
    private String shipToAddressLine2;
    private String shipToCity;
    private String shipToPostalCode;
    private String shipToEmail;
    private String shipToPhoneNumber;
    private String billToName;
    private String billToAddressLine1;
    private String billToAddressLine2;
    private String billToCity;
    private String billToPostalCode;
    private String billToEmail;
    private String billToPhoneNumber;
    private String email;
    private String type;
    private String taskCategory;
    private String taskDescription;
    @JsonProperty("isConditionsChecked")
    private boolean isConditionsChecked;
}
