package com.alleskoenner.backend.customer_order.internal.order;

import java.time.ZonedDateTime;
import java.util.Optional;
import java.util.function.Consumer;

import com.alleskoenner.backend.appointment_coordination.AppointmentCoordinationDetails;
import com.alleskoenner.backend.appointment_coordination.AppointmentCoordinationServiceInterface;
import com.alleskoenner.backend.customer_order.internal.order.business_central.BusinessCentralClient;
import com.alleskoenner.backend.customer_order.internal.order.business_central.BusinessCentralOrderResponse;
import com.alleskoenner.backend.emails.ErrorNotificationEmailServiceInterface;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@AllArgsConstructor
public class OrderService {

    private final BusinessCentralClient businessCentralClient;
    private final OrderSummaryMapper orderSummaryMapper;
    private final ConfirmationEmailSender confirmationEmailSender;
    private final AppointmentCoordinationServiceInterface appointmentCoordinationService;
    private final ErrorNotificationEmailServiceInterface errorNotificationEmailService;

    public Mono<OrderSummary> createOrder(OrderDetails orderDetails) {
        return businessCentralClient.sendOrderRequest(orderDetails)

                .doOnSuccess(trySendingConfirmationEmail(orderDetails))

                .doOnSuccess(tryStartingAppointmentCoordination(orderDetails))

                .map(orderResponse -> orderSummaryMapper.mapBusinessCentralResponseToOrderSummary(orderDetails, orderResponse))

                .doOnError(e -> {
                    errorNotificationEmailService.sendErrorNotificationEmail(e);
                    log.error("Some error occurred during appointment coordination: ", e);
                });
    }

    private Consumer<BusinessCentralOrderResponse> trySendingConfirmationEmail(OrderDetails orderDetails) {
        return orderResponse -> {
            try {
                confirmationEmailSender.sendConfirmationEmail(orderDetails, orderResponse);
            } catch (Exception e) {
                log.error("Unknown error while trying to send email: ", e);
            }
        };
    }

    private Consumer<BusinessCentralOrderResponse> tryStartingAppointmentCoordination(OrderDetails orderDetails) {
        return businessCentralOrderResponse -> {

            Optional<ZonedDateTime> requestedDateTimeOrNow = orderDetails.isAsSoonAsPossible() ? Optional.empty() : Optional.of(orderDetails.appointmentRequestDate());

            var appointmentCoordinationDetails = new AppointmentCoordinationDetails(
                    businessCentralOrderResponse.appointmentRequestId(),
                    orderDetails.shipToAddressLine1(),
                    orderDetails.shipToCity(),
                    orderDetails.shipToPostalCode(),
                    orderDetails.taskDescription(),
                    requestedDateTimeOrNow,
                    orderDetails.isAsSoonAsPossible());

            appointmentCoordinationService
                    .startAppointmentCoordination(appointmentCoordinationDetails)
                    .onErrorComplete()
                    .doOnError(e -> log.error("Error while trying to start appointment coordination: ", e))
                    .subscribe();
        };
    }
}
