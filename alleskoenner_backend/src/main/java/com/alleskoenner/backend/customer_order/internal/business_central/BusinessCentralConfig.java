package com.alleskoenner.backend.customer_order.internal.business_central;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "business-central")
public class BusinessCentralConfig {

    private String baseurl;
    private String microsoftLogin;
    private String bcCompanyId;
    private String bcTenantId;
    private String bcEnvironment;
    private String bcClientId;

}
