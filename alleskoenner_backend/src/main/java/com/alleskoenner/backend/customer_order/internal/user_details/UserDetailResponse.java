package com.alleskoenner.backend.customer_order.internal.user_details;

import com.alleskoenner.backend.customer_order.internal.user_details.business_central.BusinessCentralCustomerResponseValue;

public record UserDetailResponse(
        String displayName,
        String type,
        String addressLine1,
        String addressLine2,
        String city,
        String country,
        String postalCode,
        String phoneNumber
) {
    public static UserDetailResponse from(BusinessCentralCustomerResponseValue businessCentralCustomerResponseValue) {
        return new UserDetailResponse(
                businessCentralCustomerResponseValue.displayName(),
                businessCentralCustomerResponseValue.type(),
                businessCentralCustomerResponseValue.addressLine1(),
                businessCentralCustomerResponseValue.addressLine2(),
                businessCentralCustomerResponseValue.city(),
                businessCentralCustomerResponseValue.country(),
                businessCentralCustomerResponseValue.postalCode(),
                businessCentralCustomerResponseValue.phoneNumber()
        );
    }
}