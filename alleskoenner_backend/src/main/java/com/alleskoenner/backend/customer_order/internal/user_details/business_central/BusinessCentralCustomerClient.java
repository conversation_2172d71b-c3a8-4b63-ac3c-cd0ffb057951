package com.alleskoenner.backend.customer_order.internal.user_details.business_central;

import com.alleskoenner.backend.shared.LoginEmail;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import java.util.List;

@Slf4j
@Component
@AllArgsConstructor
public class BusinessCentralCustomerClient {

    private final WebClient bcWebClient;
    private final BusinessCentralCustomerUrlBuilder businessCentralCustomerUrlBuilder;

    public Mono<List<BusinessCentralCustomerResponseValue>> sendCustomerRequest(LoginEmail customerEmail) {

        return bcWebClient.get()
                .uri(businessCentralCustomerUrlBuilder.buildCustomerRequestUrl(customerEmail))
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(BusinessCentralCustomerResponse.class)
                                .map(BusinessCentralCustomerResponse::value);
                    } else {
                        return mapResponseBodyToError(response);
                    }
                });
    }

    private static Mono<List<BusinessCentralCustomerResponseValue>> mapResponseBodyToError(ClientResponse response) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> {
                    log.error("Request failed with status code {} and body {}", response.statusCode(), errorBody);
                    return Mono.error(new WebClientResponseException(
                            "Request failed with status code " + response.statusCode(),
                            response.statusCode().value(),
                            response.statusCode().toString(), null, errorBody.getBytes(), null
                    ));
                });
    }
}
