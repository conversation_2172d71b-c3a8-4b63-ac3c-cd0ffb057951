package com.alleskoenner.backend.customer_order.internal.order;

import java.time.ZonedDateTime;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.FutureOrPresent;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;

@Data
@AllArgsConstructor
@With
@NoArgsConstructor
public class OrderDetailsRequest {

    @NotBlank(message = "shipToName is required")
    @Size(max = 100, message = "shipToName must not exceed 100 characters")
    private String shipToName;

    @NotBlank(message = "shipToAddressLine1 is required")
    @Size(max = 150, message = "shipToAddressLine1 must not exceed 150 characters")
    private String shipToAddressLine1;

    @Size(max = 100, message = "shipToAddressLine2 must not exceed 100 characters")
    private String shipToAddressLine2;

    @NotBlank(message = "shipToCity is required")
    @Size(max = 100, message = "shipToCity must not exceed 100 characters")
    private String shipToCity;

    @NotBlank(message = "shipToPostalCode is required")
    @Size(max = 50, message = "shipToPostalCode must not exceed 50 characters")
    private String shipToPostalCode;

    @NotBlank(message = "shipToEmail must not be empty")
    private String shipToEmail;

    @Size(max = 50, message = "shipToPhoneNumber must not exceed 50 characters")
    private String shipToPhoneNumber;

    @NotBlank(message = "billToName is required")
    @Size(max = 100, message = "billToName must not exceed 100 characters")
    private String billToName;

    @NotBlank(message = "billToAddressLine1 is required")
    @Size(max = 150, message = "billToAddressLine1 must not exceed 150 characters")
    private String billToAddressLine1;

    @Size(max = 100, message = "billToAddressLine2 must not exceed 100 characters")
    private String billToAddressLine2;

    @NotBlank(message = "billToCity is required")
    @Size(max = 100, message = "billToCity must not exceed 100 characters")
    private String billToCity;

    @NotBlank(message = "billToPostalCode is required")
    @Size(max = 50, message = "billToPostalCode must not exceed 50 characters")
    private String billToPostalCode;

    @NotBlank(message = "billToEmail must not be empty")
    private String billToEmail;

    @Size(max = 50, message = "billToPhoneNumber must not exceed 50 characters")
    private String billToPhoneNumber;

    @NotBlank(message = "email must not be empty")
    private String email;

    @NotBlank(message = "Customer type is required")
    @Size(max = 100, message = "Customer type must not exceed 100 characters")
    @Pattern(regexp = "Person", message = "Customer type must be 'Person'")
    private String customerType;

    @NotBlank(message = "Task category must not be empty")
    @Size(max = 100, message = "Task category must not exceed 100 characters")
    private String taskCategory;

    @NotBlank(message = "Task description must not be empty")
    @Size(max = 2000, message = "Task description must not exceed 2000 characters")
    private String taskDescription;

    @NotNull(message = "Conditions must be checked")
    @JsonProperty("isConditionsChecked")
    private Boolean isConditionsChecked;

    @NotNull(message = "ASAP must be true or false")
    @JsonProperty("isAsSoonAsPossible")
    private Boolean isAsSoonAsPossible;

    //todo Bug: Text is not sent to the backend. Cant figure out why, will dismiss for now.
    @FutureOrPresent(message = "Appointment request date must be in the present or future")
    private ZonedDateTime appointmentRequestDate;

    private String requestedHandymanSkill;

}
