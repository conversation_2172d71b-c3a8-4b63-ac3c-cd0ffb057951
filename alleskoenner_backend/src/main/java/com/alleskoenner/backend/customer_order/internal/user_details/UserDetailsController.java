package com.alleskoenner.backend.customer_order.internal.user_details;

import com.alleskoenner.backend.shared.LoginEmail;
import com.alleskoenner.backend.shared.UserFacingErrorMessage;
import com.alleskoenner.backend.shared.UserFacingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@CrossOrigin("http://localhost:3000/")
@RestController
@RequestMapping("/customer/details")
@RequiredArgsConstructor
public class UserDetailsController {

    private final UserDetailsService userDetailsService;

    @GetMapping(produces = "application/json")
    public Mono<ResponseEntity<UserDetailResponse>> getUserDetails(@AuthenticationPrincipal JwtAuthenticationToken principal) {

        var customerEmail = new LoginEmail(principal.getToken().getClaim("email"));

        return userDetailsService.getUserDetails(customerEmail)
                .map(userDetail -> ResponseEntity.ok().body(userDetail));
    }

    @ExceptionHandler(exception = UserFacingException.class, produces = "application/json")
    public ResponseEntity<UserFacingErrorMessage> handleError(UserFacingException exc) {
        return ResponseEntity.status(exc.getStatusCode()).body(new UserFacingErrorMessage(exc.getMessage()));
    }

    @ExceptionHandler(produces = "application/json")
    public ResponseEntity<UserFacingErrorMessage> handleError(RuntimeException exc) {
        var errorMessage = "An error occurred while fetching user details";
        log.error(errorMessage + ": {}", exc.getMessage(), exc);
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new UserFacingErrorMessage(errorMessage + "."));
    }
}
