package com.alleskoenner.backend.customer_order.internal.order.business_central;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BusinessCentralOrderResponseBody {
    private String odataContext; // "@odata.context" -> odataContext
    private String odataEtag;    // "@odata.etag" -> odataEtag
    private int id;
    private String addressLine1;
    private String addressLine2;
    private String appointmentRequestDate;
    private String city;
    private String displayName;
    private String customerNo;
    private String customerID;
    private String appointmentNo;
    private String appointmentSystemID;
    private String salesOrderNo;
    private String salesOrderID;
    private String email;
    @JsonProperty("isConditionsChecked")
    private boolean isConditionsChecked;
    private String phoneNumber;
    private String postalCode;
    private String requestedHandymanSkill;
    private String taskCategory;
    private String taskDescription;
    private String customerType;
}
