package com.alleskoenner.backend.customer_order.internal.order.business_central;

import com.alleskoenner.backend.customer_order.internal.order.OrderDetails;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@AllArgsConstructor
public class BusinessCentralClient {

    private final WebClient bcWebClient;
    private final BusinessCentralUrlBuilder businessCentralUrlBuilder;
    private final BusinessCentralOrderRequestMapper businessCentralOrderRequestMapper;

    public Mono<BusinessCentralOrderResponse> sendOrderRequest(OrderDetails orderDetails) {

        var mappedRequestPayload = businessCentralOrderRequestMapper.mapOrderDetailsToBusinessCentralOrderRequest(orderDetails);

        return bcWebClient.post()
                .uri(businessCentralUrlBuilder.buildOrderRequestUrl())
                .bodyValue(mappedRequestPayload)
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return mapResponseBodyIntoOrderResponse(response);
                    } else {
                        return mapResponseBodyToError(response);
                    }
                });
    }

    private static Mono<BusinessCentralOrderResponse> mapResponseBodyIntoOrderResponse(ClientResponse response) {
        return response.bodyToMono(BusinessCentralOrderResponseBody.class)
                .map(orderResponseBody ->
                        new BusinessCentralOrderResponse(
                                response.statusCode(),
                                orderResponseBody.getSalesOrderNo(),
                                orderResponseBody.getAppointmentSystemID()
                        ));
    }

    private static Mono<BusinessCentralOrderResponse> mapResponseBodyToError(ClientResponse response) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> {
                    log.error("Request failed with status code {} and body {}", response.statusCode(), errorBody);
                    return Mono.error(new WebClientResponseException(
                            "Request failed with status code " + response.statusCode(),
                            response.statusCode().value(),
                            response.statusCode().toString(), null, errorBody.getBytes(), null
                    ));
                });
    }
}
