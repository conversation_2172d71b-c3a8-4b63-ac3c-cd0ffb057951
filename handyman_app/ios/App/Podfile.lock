PODS:
  - Capacitor (7.4.0):
    - Capacitor<PERSON>ordova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorCordova (7.4.0)
  - CapacitorFilesystem (7.1.1):
    - Capacitor
    - IONFilesystemLib (~> 1.0)
  - CapacitorPushNotifications (7.0.1):
    - Capacitor
  - CapawesomeTeamCapacitorFileOpener (7.0.1):
    - Capacitor
  - IONFilesystemLib (1.0.0)

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorFilesystem (from `../../node_modules/@capacitor/filesystem`)"
  - "CapacitorPushNotifications (from `../../node_modules/@capacitor/push-notifications`)"
  - "CapawesomeTeamCapacitorFileOpener (from `../../node_modules/@capawesome-team/capacitor-file-opener`)"

SPEC REPOS:
  trunk:
    - IONFilesystemLib

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorFilesystem:
    :path: "../../node_modules/@capacitor/filesystem"
  CapacitorPushNotifications:
    :path: "../../node_modules/@capacitor/push-notifications"
  CapawesomeTeamCapacitorFileOpener:
    :path: "../../node_modules/@capawesome-team/capacitor-file-opener"

SPEC CHECKSUMS:
  Capacitor: 10feab36396883c55373328124a677f118cd4b5f
  CapacitorApp: febecbb9582cb353aed037e18ec765141f880fe9
  CapacitorCordova: 723e017d8a80ab2ede9959fbe37556c35ac860d5
  CapacitorFilesystem: 39173fe5a0e77851119998d0d8c61c70551a412a
  CapacitorPushNotifications: 6a2794788c583dd89215f1805ca4bced1b13dbdf
  CapawesomeTeamCapacitorFileOpener: ae3340a148c8947dda81ed2a8a600988667909b3
  IONFilesystemLib: ceacae793975039530458eabab0c495c70515a0d

PODFILE CHECKSUM: 40b4acad0a5305ec12854e9ba4ce3214a09d8eb1

COCOAPODS: 1.16.2
