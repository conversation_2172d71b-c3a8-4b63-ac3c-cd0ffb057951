<script lang="ts" setup>
import DefaultLayout from '@/layout/DefaultLayout.vue';
import { onMounted, onUnmounted, ref } from 'vue';
import { usePostedOrderService } from '@/services/postedOrderService.ts';
import type { Order } from '@/types/Order.type.ts';
import PostedOrderList from '@/components/posted_orders/PostedOrderList.vue';

const { getAllPostedOrders } = usePostedOrderService();

const postedOrders = ref<Order[]>([]);
const isLoading = ref(true);

onMounted(async () => {
  postedOrders.value = (await getAllPostedOrders()).postedOrders;
  isLoading.value = false;
});

onUnmounted(() => {});
</script>

<template>
  <DefaultLayout>
    <PostedOrderList :isLoading="isLoading" :postedOrders="postedOrders" />
  </DefaultLayout>
</template>
