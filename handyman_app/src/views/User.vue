<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import { userManager, userState } from '@/auth/auth.ts';
import DefaultButton from '@/components/DefaultButton.vue';
import { useDeviceStore } from '@/stores/deviceStore.ts';
import { onMounted, ref } from 'vue';
import { faArrowRightFromBracket, faUser } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import DefaultLayout from '@/layout/DefaultLayout.vue';
import { showErrorToast, showSuccessToast } from '@/Toaster.ts';

const deviceToken = ref<string | undefined>(undefined);

function getDeviceToken() {
  deviceToken.value = useDeviceStore().deviceToken;
}

function copyDeviceTokenAndTriggerToast() {
  if (!deviceToken.value) {
    showErrorToast('<PERSON><PERSON>ce <PERSON> vorhanden, nichts kopiert!');
    return;
  }

  const textToCopy = deviceToken.value;

  navigator.clipboard
    .writeText(textToCopy)
    .then(() => {
      showSuccessToast('Device Token kopiert!');
    })
    .catch(() => {
      showErrorToast('Fehler beim Kopieren des Device Tokens');
    });
}

async function logout() {
  await userManager.signoutRedirect();
}

onMounted(() => {
  getDeviceToken();
});
</script>

<template>
  <DefaultLayout>
    <div class="flex flex-col items-center justify-center text-center space-y-4 h-full">
      <FontAwesomeIcon :icon="faUser" class="text-cerulean-600 bg-cerulean-200 rounded-full p-10 text-7xl" />

      <div class="text-2xl font-semibold">
        {{ userState?.profile.name ?? userState?.profile.email ?? 'Nicht gesetzter Name' }}
      </div>

      <div class="text-lg text-gray-600">
        {{ userState?.profile.email ?? 'Nicht gesetzte Email' }}
      </div>

      <div class="text-sm max-w-[60ch] break-words text-center text-gray-400" @click="copyDeviceTokenAndTriggerToast">
        Device Token: {{ deviceToken ?? 'Kein Device Token vorhanden' }}
      </div>
      <DefaultButton
        v-if="userState !== null"
        :icon="faArrowRightFromBracket"
        class="bg-white text-bamboo-600 mt-6 border border-bamboo-600"
        @click.prevent="logout"
      >
        <span class="text-base">Logout</span>
      </DefaultButton>
    </div>
  </DefaultLayout>
</template>
