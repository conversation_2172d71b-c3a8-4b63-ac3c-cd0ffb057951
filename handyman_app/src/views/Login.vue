<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import { login, userManager } from '@/auth/auth.ts';
import router from '@/router/routes.ts';

async function loginBtn() {
  login('/');
}

userManager.getUser().then((user) => {
  if (user != null) {
    router.push('/');
  }
});
</script>

<template>
  <div class="h-screen grid grid-cols-[4fr_3fr] gap-16 relative bg-gradient-cerulean">
    <div class="col-start-1 row-start-1 flex items-end overflow-hidden">
      <img alt="Wrench" class="max-h-[90vh] object-contain" src="../assets/img/wrench.svg" />
    </div>

    <div class="col-start-2 row-start-1 m-5 bg-white rounded-lg flex flex-col justify-center items-center">
      <div class="flex flex-col justify-center">
        <img alt="Alleskoenner Logo" src="../assets/img/logo/logo-with-text.svg" />
      </div>

      <div class="pt-8 flex flex-col justify-center max-w-120">
        <div class="text-2xl font-bold text-center">Willkommen zurück!</div>
        <div class="text-base text-center pt-2 px-20">Melden Sie sich hier mit Ihren Microsoft Zugangsdaten an.</div>
      </div>

      <div class="pt-12 flex flex-col justify-center">
        <button
          class="rounded-lg h-10 w-80 bg-white border border-gray-300 mx-5 box-border flex items-center justify-center hover:scale-110 transition-transform duration-200 ease-in-out"
          @click.prevent="loginBtn"
        >
          <img alt="Microsoft Logo" class="p-2" src="../assets/img/microsoft.svg" />
          Mit Microsoft anmelden
        </button>
      </div>
    </div>

    <div class="absolute bottom-3 left-5 text-white text-base font-normal leading-6">
      © Alleskoenner24.com | Nahariyastraße 1, 33602 Bielefeld | 0521/ 120 079 20
    </div>
  </div>
</template>
