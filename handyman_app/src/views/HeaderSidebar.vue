<script lang="ts" setup>
import { userState } from '@/auth/auth.ts';
import HeaderBar from '@/components/Header.vue';
import Sidebar from '@/components/Sidebar.vue';
</script>

<template>
  <div class="flex flex-col h-screen">
    <HeaderBar />
    <div class="flex flex-1 overflow-hidden">
      <Sidebar v-if="userState !== null" :is-mobile-menu-open="false" :user="userState" class="overflow-y-auto" />
      <slot></slot>
    </div>
  </div>
</template>

<style scoped></style>
