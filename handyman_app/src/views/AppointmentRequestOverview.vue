<script lang="ts" setup>
import AppointmentRequestList from '@/components/AppointmentRequestList.vue';
import DefaultLayout from '@/layout/DefaultLayout.vue';
import InformationCard from '@/components/InformationCard.vue';
</script>

<template>
  <DefaultLayout>
    <InformationCard
      card-type="information"
      message="Wählen Sie eine Anfrage aus, um Details zu erhalten und sich den Auftrag zu sichern!"
    />
    <AppointmentRequestList />
  </DefaultLayout>
</template>
