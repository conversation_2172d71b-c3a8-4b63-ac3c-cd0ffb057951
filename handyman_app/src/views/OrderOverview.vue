<script lang="ts" setup>
import DefaultLayout from '@/layout/DefaultLayout.vue';
import OrderList from '@/components/orders/OrderList.vue';
import dayjs from 'dayjs';
import { useAppointmentConfirmationService } from '@/services/appointmentConfirmationService.ts';
import { onMounted, onUnmounted, ref } from 'vue';
import InformationCard from '@/components/InformationCard.vue';
import DefaultButton from '@/components/DefaultButton.vue';
import { useOrderService } from '@/services/orderService.ts';
import type { Order } from '@/types/Order.type.ts';
import { faArrowRotateLeft } from '@fortawesome/free-solid-svg-icons';

const messageDelayInMilliseconds = 10000;
const messageDelayInSeconds = messageDelayInMilliseconds / 1000;

const isJustConfirmed = ref<boolean>(false);

const asapOrders = ref<Order[]>([]);
const scheduledOrders = ref<Order[]>([]);
const isLoading = ref(true);

const { getAllOrders } = useOrderService();

let timeout: ReturnType<typeof setTimeout> | undefined;

onMounted(async () => {
  const mostRecentConfirmationDateTime = useAppointmentConfirmationService().getMostRecentConfirmationDateTime();

  if (!!mostRecentConfirmationDateTime) {
    const timeSinceConfirmation = dayjs().diff(mostRecentConfirmationDateTime, 'millisecond');
    isJustConfirmed.value = timeSinceConfirmation < messageDelayInMilliseconds;
    timeout = setTimeout(() => {
      isJustConfirmed.value = false;
    }, messageDelayInMilliseconds - timeSinceConfirmation);
  }

  await reloadOrders();
  isLoading.value = false;
});

onUnmounted(() => {
  if (timeout) {
    clearTimeout(timeout);
  }
});

async function reloadOrders() {
  ({ asapOrders: asapOrders.value, scheduledOrders: scheduledOrders.value } = await getAllOrders());
}
</script>

<template>
  <DefaultLayout>
    <InformationCard
      v-if="isJustConfirmed"
      :hide-handyman-image="true"
      :message="`Es kann bis zu ${messageDelayInSeconds} Sekunden dauern, bis ein kürzlich angenommener Auftrag in der Liste erscheint.`"
      card-type="information"
    >
      <template #custom-content>
        <DefaultButton :icon="faArrowRotateLeft" class="text-cerulean-700 bg-white mr-4" @click.prevent="reloadOrders">
          Aktualisieren
        </DefaultButton>
      </template>
    </InformationCard>
    <OrderList :asap-orders="asapOrders" :isLoading="isLoading" :scheduled-orders="scheduledOrders" />
  </DefaultLayout>
</template>
