<script lang="ts" setup>
import { userState } from '@/auth/auth.ts';
import Sidebar from '@/components/Sidebar.vue';
import Header from '@/components/Header.vue';
import { ref } from 'vue';

const isMobileMenuOpen = ref(false);

const changeMobileMenuOpen = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value;
};
</script>

<template>
  <Header :isMobileMenuOpen="isMobileMenuOpen" @toggleMenu="changeMobileMenuOpen" />

  <div class="flex h-[calc(100vh-60px)] mt-15">
    <Sidebar
      v-if="userState !== null"
      :isMobileMenuOpen="isMobileMenuOpen"
      :user="userState"
      @closeMenu="isMobileMenuOpen = false"
    />
    <main class="flex-1 overflow-y-auto p-8">
      <slot />
    </main>
  </div>
</template>
