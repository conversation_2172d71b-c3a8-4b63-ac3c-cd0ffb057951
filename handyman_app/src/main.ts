import './assets/main.css';
import './assets/tailwind.css';
import 'vue3-toastify/dist/index.css';
import './assets/toast.css';

import { createApp } from 'vue';
import { createPinia } from 'pinia';

import App from './App.vue';
import router from './router/routes.ts';
import { setupNotifications } from '@/services/pushNotificationService.ts';
import '@/auth/auth.ts';

const app = createApp(App);

app.use(createPinia());
app.use(router);

app.mount('#app');

setupNotifications();
