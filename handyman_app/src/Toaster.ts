import type { Id, ToastOptions } from 'vue3-toastify';
import { toast } from 'vue3-toastify';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { h } from 'vue';
import { faCircleCheck, faCircleInfo, faCircleXmark, faTriangleExclamation } from '@fortawesome/free-solid-svg-icons';

const baseToastOptions: ToastOptions = {
  position: 'bottom-right',
  hideProgressBar: false,
  theme: 'light',
  transition: 'slide',
  autoClose: 3000,
  isLoading: false,
};

export function showLoadingToast(message?: string) {
  return toast(message ?? 'Es lädt...', {
    ...baseToastOptions,
    type: 'loading',
    isLoading: true,
  });
}

export function showErrorToast(message?: string) {
  return toast(message ?? 'Es ist ein Fehler aufgetreten...', {
    ...baseToastOptions,
    type: 'error',
  });
}

export function showSuccessToast(message?: string) {
  return toast(message ?? 'Es ist ein Fehler aufgetreten...', {
    ...baseToastOptions,
    type: 'success',
    icon: h(FontAwesomeIcon, { icon: faCircleCheck, style: 'font-size: 24px; color: var(--toastify-color-success)' }),
  });
}

export function updateToWarningToast(message: string, id: Id) {
  toast.update(id, {
    ...baseToastOptions,
    render: message,
    type: 'warning',
    icon: h(FontAwesomeIcon, {
      icon: faTriangleExclamation,
      style: 'font-size: 24px; color: var(--toastify-color-warning)',
    }),
  });
}

export function updateToSuccessToast(message: string, id: Id) {
  toast.update(id, {
    ...baseToastOptions,
    render: message,
    type: 'success',
    icon: h(FontAwesomeIcon, { icon: faCircleCheck, style: 'font-size: 24px; color: var(--toastify-color-success)' }),
  });
}

export function updateToErrorToast(message: string, id: Id) {
  toast.update(id, {
    ...baseToastOptions,
    render: message,
    type: 'error',
    icon: h(FontAwesomeIcon, {
      icon: faCircleXmark,
      style: 'font-size: 24px; color: var(--toastify-color-error);',
    }),
  });
}

export function updateToInfoToast(message: string, id: Id) {
  toast.update(id, {
    ...baseToastOptions,
    render: message,
    type: 'info',
    icon: h(FontAwesomeIcon, {
      icon: faCircleInfo,
      style: 'font-size: 24px; color: var(--toastify-color-info);',
    }),
  });
}
