import type { NavigationGuardWithThis } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';
import AppointmentRequestOverview from '@/views/AppointmentRequestOverview.vue';
import OrderOverview from '@/views/OrderOverview.vue';
import OrderDetailView from '@/views/OrderDetailView.vue';
import { userState } from '@/auth/auth.ts';
import Login from '@/views/Login.vue';
import User from '@/views/User.vue';
import PostedOrderOverview from '@/views/PostedOrderOverview.vue';
import PostedOrderDetailView from '@/views/PostedOrderDetailView.vue';
import StatisticsView from '@/views/StatisticsView.vue';
import SettingsView from '@/views/SettingsView.vue';

const redirectToLoginIfNotLoggedIn: NavigationGuardWithThis<undefined> = (to, from, next) => {
  if (userState.value === null) {
    next('/login');
  } else {
    next();
  }
};

const redirectToRootIfLoggedIn: NavigationGuardWithThis<undefined> = (to, from, next) => {
  if (userState.value !== null) {
    next('/');
  } else {
    next();
  }
};
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/orders',
    },
    {
      path: '/orders',
      name: 'orders',
      component: OrderOverview,
      beforeEnter: redirectToLoginIfNotLoggedIn,
    },
    {
      path: '/orders/:caseNumber',
      name: 'order-details',
      component: OrderDetailView,
      props: true,
      beforeEnter: redirectToLoginIfNotLoggedIn,
    },
    {
      path: '/posted-orders',
      name: 'posted-orders',
      component: PostedOrderOverview,
      beforeEnter: redirectToLoginIfNotLoggedIn,
    },
    {
      path: '/posted-orders/:caseNumber',
      name: 'posted-order-details',
      component: PostedOrderDetailView,
      props: true,
      beforeEnter: redirectToLoginIfNotLoggedIn,
    },
    {
      path: '/appointment-requests',
      name: 'appointment-requests', // This route is mentioned in a push notification, please keep the name consistent with the backend
      component: AppointmentRequestOverview,
      beforeEnter: redirectToLoginIfNotLoggedIn,
    },
    {
      path: '/statistics',
      name: 'statistics',
      component: StatisticsView,
      beforeEnter: redirectToLoginIfNotLoggedIn,
    },
    {
      path: '/settings',
      name: 'settings',
      component: SettingsView,
      beforeEnter: redirectToLoginIfNotLoggedIn,
    },
    {
      path: '/user',
      name: 'user',
      component: User,
      beforeEnter: redirectToLoginIfNotLoggedIn,
    },
    {
      path: '/login',
      name: 'login',
      component: Login,
      beforeEnter: redirectToRootIfLoggedIn,
    },
    {
      path: '/login-redirect',
      name: 'login-redirect',
      component: () => import('../views/LoginRedirect.vue'),
    },
  ],
});

export default router;
