<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { faEllipsisVertical } from '@fortawesome/free-solid-svg-icons';

defineProps<{
  fileName: string;
}>();
</script>

<template>
  <div
    :test-id="`document-button-${fileName}`"
    class="flex items-center justify-between p-4 bg-white rounded-xl shadow border border-gray-200 max-w-md"
  >
    <div class="flex items-center space-x-4">
      <div class="bg-bamboo-100 rounded-full p-2">
        <img alt="Rechnung Icon" class="w-4 h-4" src="../assets/img/file-invoice-dollar.svg" />
      </div>
      <span class="text-gray-950 font-medium">{{ fileName }}</span>
    </div>
    <div class="flex flex-col justify-center space-y-1">
      <FontAwesomeIcon :icon="faEllipsisVertical" class="mr-2 text-gray-950 text-xl" />
    </div>
  </div>
</template>
