<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { faBars } from '@fortawesome/free-solid-svg-icons';

const emit = defineEmits<{
  (e: 'toggleMenu'): void;
}>();

function toggleMenu() {
  emit('toggleMenu');
}
</script>
<template>
  <div
    class="fixed top-0 left-0 right-0 flex justify-between align-middle w-full box-border px-3 bg-cerulean-50 h-15 items-center mt-6"
  >
    <a id="logo" class="mr-6" href="/" test-id="header-logo">
      <img
        alt="Alleskoenner Logo"
        src="../assets/img/alleskoenner_logo.svg"
        style="width: auto; height: 31px; object-fit: contain"
      />
    </a>
    <button class="lg:hidden text-2xl" @click="toggleMenu">
      <FontAwesomeIcon :icon="faBars" />
    </button>
  </div>
</template>
