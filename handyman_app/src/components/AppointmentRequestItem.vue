<script lang="ts" setup>
import dayjs, { type Dayjs } from 'dayjs';
import { computed, ref } from 'vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import {
  faCheck,
  faClock,
  faHourglassEnd,
  faHourglassHalf,
  faHourglassStart,
  faTruck,
  faXmark,
} from '@fortawesome/free-solid-svg-icons';
import DefaultButton from '@/components/DefaultButton.vue';
import { useAppointmentConfirmationService } from '@/services/appointmentConfirmationService.ts';
import { showLoadingToast, updateToErrorToast, updateToSuccessToast, updateToWarningToast } from '@/Toaster.ts';
import { HttpStatusCode } from '@/types/HttpStatusCode.ts';
import router from '@/router/routes.ts';
import duration from 'dayjs/plugin/duration';
import utc from 'dayjs/plugin/utc';
import OpenInMapsWrapper from '@/components/OpenInMapsWrapper.vue';

dayjs.extend(duration);
dayjs.extend(utc);

const emit = defineEmits(['removeAppointmentRequest']);
export type AppointmentRequestItemProps = {
  appointmentRequestId: string;
  addressLine1: string;
  addressLine2: string;
  postcode: string;
  city: string;
  description: string;
  isAsap?: boolean;
  remainingSecondsToReply: number;
  scheduledDateTime?: Dayjs;
};

const props = defineProps<AppointmentRequestItemProps>();

const removeAppointmentRequest = () => {
  emit('removeAppointmentRequest', props.appointmentRequestId);
};

const isButtonDisabled = ref<boolean>(false);

const titleIcon = computed(() => (props.isAsap ? faTruck : faClock));
const titleTime = computed(() =>
  props.isAsap ? 'So schnell wie möglich!' : dayjs(props.scheduledDateTime).format(`DD.MM.YYYY, H [Uhr]`),
);
const remainingTimeFormatted = computed(() => {
  const remainingTime = dayjs.utc(Math.max(0, props.remainingSecondsToReply) * 1000);
  const isLessThanOneHour = props.remainingSecondsToReply < 3600;
  return remainingTime.format(isLessThanOneHour ? 'mm:ss' : 'HH:mm:ss');
});
const hourGlass = computed(() => {
  const halfTime = 2.5 * 60;
  if (props.remainingSecondsToReply > halfTime) {
    return faHourglassStart;
  }
  if (props.remainingSecondsToReply > 30) {
    return faHourglassHalf;
  }
  return faHourglassEnd;
});

const fullAddress = computed(() =>
  [props.addressLine1, props.addressLine2, `${props.postcode} ${props.city}`]
    .map((part) => part?.trim())
    .filter(Boolean)
    .join(', '),
);

function handleErrorCase(toastId: number | string) {
  updateToErrorToast('Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.', toastId);
  isButtonDisabled.value = false;
}

const handleConfirmation = async () => {
  isButtonDisabled.value = true;
  const toastId = showLoadingToast('Auftrag wird angenommen...');
  try {
    const response = await useAppointmentConfirmationService().confirmAppointmentRequest(props.appointmentRequestId);

    if (response.ok) {
      updateToSuccessToast('Anfrage angenommen.', toastId);
    } else {
      updateToWarningToast('Die gewählte Anfrage ist nicht mehr verfügbar.', toastId);
    }
    removeAppointmentRequest();
    await router.push('orders');
  } catch {
    handleErrorCase(toastId);
  }
};

const handleRejection = async () => {
  isButtonDisabled.value = true;
  const toastId = showLoadingToast();
  try {
    const response = await useAppointmentConfirmationService().rejectAppointmentRequest(
      props.appointmentRequestId,
      'fake reason',
    );

    if (response.ok || response.status === HttpStatusCode.GONE) {
      updateToSuccessToast('Anfrage abgelehnt.', toastId);
      removeAppointmentRequest();
    } else {
      handleErrorCase(toastId);
    }
  } catch {
    handleErrorCase(toastId);
  }
};
</script>

<template>
  <div
    :class="[
      isAsap ? 'bg-asap-card text-white' : 'text-cerulean-950 outline-1',
      'w-full px-6 py-5 rounded-lg flex flex-col gap-3',
    ]"
    test-id="appointment-request-item"
  >
    <div class="flex">
      <div class="grow flex gap-2 items-center">
        <FontAwesomeIcon :icon="titleIcon" />
        <span class="text-lg font-bold">{{ titleTime }}</span>
      </div>
      <div class="flex gap-2 items-center">
        <FontAwesomeIcon :icon="hourGlass" />
        <span>{{ remainingTimeFormatted }}</span>
      </div>
    </div>
    <span :class="[isAsap ? 'font-medium' : '', 'text-base break-words break-all whitespace-normal']">
      „{{ description }}“
    </span>
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
      <div class="flex flex-col sm:flex-row gap-2 md:flex-row md:flex-1">
        <DefaultButton
          :class="[
            isAsap
              ? 'bg-white text-bamboo-600 disabled:bg-bamboo-600 disabled:text-bamboo-700'
              : 'bg-bamboo-500 text-white disabled:bg-bamboo-100 disabled:text-bamboo-300',
            'min-w-[140px] w-full sm:w-auto md:max-w-[200px] md:w-auto',
          ]"
          :disabled="isButtonDisabled"
          :icon="faCheck"
          @click="handleConfirmation"
        >
          <span class="text-base">Auftrag annehmen</span>
        </DefaultButton>
        <DefaultButton
          :class="[
            isAsap
              ? 'border-white disabled:border-bamboo-600 disabled:text-bamboo-600'
              : 'border-bamboo-600 text-bamboo-600 disabled:border-bamboo-200 disabled:text-bamboo-200',
            'border min-w-[140px] w-full sm:w-auto md:max-w-[200px] md:w-auto',
          ]"
          :disabled="isButtonDisabled"
          :icon="faXmark"
          @click="handleRejection"
        >
          Auftrag ablehnen
        </DefaultButton>
      </div>

      <OpenInMapsWrapper
        :addressObject="props"
        :class="[isAsap ? '' : 'text-cerulean-600', 'w-full md:w-auto md:ml-4 cursor-pointer']"
      >
        {{ fullAddress }}
      </OpenInMapsWrapper>
    </div>
  </div>
</template>

<style scoped>
/*noinspection CssUnusedSymbol*/
.bg-asap-card {
  background: linear-gradient(126.61deg, #ff9232 0%, #ff5900 100%);
}
</style>
