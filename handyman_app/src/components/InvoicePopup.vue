<script lang="ts" setup>
import { computed, defineProps, onMounted, ref } from 'vue';
import DefaultButton from '@/components/DefaultButton.vue';
import type { Order } from '@/types/Order.type.ts';
import { faX } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import InvoiceTable from './InvoiceTable.vue';
import { useInvoice } from '@/services/useInvoice.ts';
import { showLoadingToast, updateToErrorToast, updateToSuccessToast } from '@/Toaster.ts';
import DefaultPopup from '@/components/DefaultPopup.vue';
import InvoiceSigning from '@/components/InvoiceSigning.vue';

const props = defineProps<{
  order: Order;
}>();

const emit = defineEmits(['close', 'edit', 'confirm']);
const isButtonDisabled = ref<boolean>(false);
const isCustomerConfirmationVisible = ref(false);
const signatureRef = ref<InstanceType<typeof InvoiceSigning> | null>(null);
const isConfirmable = computed(() => !!signatureRef.value?.hasSignature);

onMounted(async () => {
  // fetch the sales order line options here so that they are already available in the edit popup (for convenience)
  await useInvoice().getSalesOrderLineOptions();
});

function close() {
  emit('close');
}

async function handleConfirmation() {
  isCustomerConfirmationVisible.value = false;
  isButtonDisabled.value = true;
  const toastId = showLoadingToast('Rechnung wird bestätigt...');

  const signature = signatureRef.value?.getDataURL() ?? null;

  if (!signature) {
    updateToErrorToast('Bitte unterschreiben Sie die Rechnung, bevor Sie sie bestätigen.', toastId);
    isButtonDisabled.value = false;
    return;
  }

  try {
    const response = await useInvoice().postSalesOrder(props.order.caseNumber, props.order.systemId, signature);

    if (response.ok) {
      updateToSuccessToast('Rechnung erfolgreich bestätigt.', toastId);
      const body = await response.json();
      emit('confirm', body);
    } else {
      updateToErrorToast('Fehler beim Bestätigen der Rechnung. Bitte versuchen Sie es später erneut.', toastId);
    }
  } catch (error) {
    updateToErrorToast('Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.', toastId);
    console.error('Error confirming invoice:', error);
  } finally {
    isButtonDisabled.value = false;
  }
}
</script>

<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-gray-800/50 p-4" @click.prevent="close">
    <div class="bg-white rounded-2xl shadow-xl w-full max-w-3xl max-h-full p-6 overflow-y-auto" @click.stop>
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
          Rechnung <span class="font-normal text-lg">Auftragsnummer: {{ order.caseNumber }}</span>
        </h2>
        <FontAwesomeIcon :icon="faX" class="text-gray-400 p-2 -m-2 cursor-pointer" @click.prevent="close" />
      </div>
      <div class="border-t-2 border-gray-300 -mx-6 mb-2" />

      <InvoiceTable :order="order" />

      <div class="flex mt-6 gap-4 flex-col sm:gap-6 sm:flex-row justify-between">
        <DefaultButton
          :disabled="isButtonDisabled"
          class="bg-white border border-bamboo-600 text-bamboo-600 basis-1/3 disabled:border-bamboo-200 disabled:text-bamboo-200"
          @click.prevent="emit('edit')"
        >
          Positionen anpassen
        </DefaultButton>
        <DefaultButton
          :disabled="isButtonDisabled"
          class="bg-gradient-bamboo text-white basis-1/3 disabled:opacity-50"
          @click.prevent="isCustomerConfirmationVisible = true"
        >
          Rechnung bestätigen
        </DefaultButton>
      </div>
    </div>
    <DefaultPopup
      v-if="isCustomerConfirmationVisible"
      :isConfirmable="isConfirmable"
      confirmText="Verbindlich bestätigen"
      title="Kundenbestätigung"
      @close="isCustomerConfirmationVisible = false"
      @confirm="handleConfirmation"
    >
      <template #main-content>
        <p class="mb-4">Hiermit bestätige ich, dass der Handwerker die Arbeitsleistungen erbracht hat.</p>
        <InvoiceSigning ref="signatureRef" />
      </template>
    </DefaultPopup>
  </div>
</template>
