<script lang="ts" setup>
import { defineProps } from 'vue';
import DefaultButton from '@/components/DefaultButton.vue';
import OpenInMapsWrapper from '@/components/OpenInMapsWrapper.vue';

const props = defineProps<{
  addressLine1: string;
  postalCode: string;
  city: string;
}>();
</script>

<template>
  <OpenInMapsWrapper :addressObject="props">
    <DefaultButton :class="['border', $attrs.class]">
      <span class="text-base">Navigation starten</span>
    </DefaultButton>
  </OpenInMapsWrapper>
</template>
