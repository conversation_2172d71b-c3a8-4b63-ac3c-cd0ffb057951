<script lang="ts" setup>
import { defineProps, ref } from 'vue';
import { faX } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import DefaultButton from '@/components/DefaultButton.vue';
import InvoiceTable from './InvoiceTable.vue';
import type { Order } from '@/types/Order.type.ts';
import { downloadAndOpenPDF, downloadPDF } from '@/services/invoicePdfService.ts';
import { showLoadingToast, updateToErrorToast, updateToSuccessToast } from '@/Toaster.ts';

const props = defineProps<{
  order: Order;
}>();

const emit = defineEmits(['close']);

const isButtonDisabled = ref<boolean>(false);

async function handleDownload() {
  isButtonDisabled.value = true;
  const toastId = showLoadingToast('Download wird gestartet...');
  const fileName = `Rechnung-${props.order.caseNumber}.pdf`;
  try {
    await downloadPDF(props.order.systemId, fileName, document);
    updateToSuccessToast('Download ist fertig', toastId);
  } catch (error) {
    console.error(error);
    updateToErrorToast('Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.', toastId);
  } finally {
    isButtonDisabled.value = false;
  }
}

async function handleOpeningPdf() {
  isButtonDisabled.value = true;
  const toastId = showLoadingToast('Download wird gestartet...');
  const fileName = `Rechnung-${props.order.caseNumber}.pdf`;
  try {
    await downloadAndOpenPDF(props.order.systemId, fileName);
    updateToSuccessToast('Download ist fertig und Dokument wird geöffnet.', toastId);
  } catch (error) {
    console.error(error);
    updateToErrorToast('Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.', toastId);
  } finally {
    isButtonDisabled.value = false;
  }
}

function close() {
  emit('close');
}
</script>

<template>
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-gray-800/50 p-4" @click.prevent="close">
    <div class="bg-white rounded-2xl shadow-xl w-full max-w-3xl max-h-full p-6 overflow-y-auto" @click.stop>
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
          Rechnung <span class="font-normal text-lg">Auftragsnummer: {{ order.caseNumber }}</span>
        </h2>
        <FontAwesomeIcon :icon="faX" class="text-gray-400" @click.prevent="close" />
      </div>
      <div class="border-t-2 border-gray-300 -mx-6 mb-2" />

      <InvoiceTable :order="order" />

      <div class="flex mt-6 gap-4 flex-col sm:gap-6 sm:flex-row justify-between">
        <DefaultButton
          :disabled="isButtonDisabled"
          class="bg-white border border-bamboo-600 text-bamboo-600 basis-1/3 disabled:border-bamboo-200 disabled:text-bamboo-200"
          @click.prevent="handleDownload"
        >
          Herunterladen
        </DefaultButton>
        <DefaultButton
          :disabled="isButtonDisabled"
          class="bg-gradient-bamboo text-white basis-1/3 disabled:opacity-50"
          @click.prevent="handleOpeningPdf"
        >
          Öffnen
        </DefaultButton>
      </div>
    </div>
  </div>
</template>
