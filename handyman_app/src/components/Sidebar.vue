<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import {
  faChartPie,
  faClock,
  faGear,
  faScrewdriverWrench,
  faSquareCheck,
  faUser,
  faXmark,
  type IconDefinition,
} from '@fortawesome/free-solid-svg-icons';
import { onMounted, onUnmounted, type Ref, ref } from 'vue';
import { User } from 'oidc-client-ts';

const props = defineProps<{
  user: User;
  isMobileMenuOpen: boolean;
}>();

const emit = defineEmits<{
  (e: 'closeMenu'): void;
}>();

function closeMobileMenu() {
  emit('closeMenu');
}

const isMobile = ref(window.innerWidth < 1024);

function handleResize() {
  isMobile.value = window.innerWidth < 1024;
}

onMounted(() => {
  handleResize();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

type SidebarNavItem = {
  icon: IconDefinition;
  label: string;
  to: string;
  isItemBambooStyle?: boolean;
  customIconStyle?: string;
};

const items: Ref<SidebarNavItem[]> = ref([
  {
    icon: faClock,
    label: 'Offene Anfragen',
    to: '/appointment-requests',
    isItemBambooStyle: true,
  },
  {
    icon: faScrewdriverWrench,
    label: 'Meine Aufträge',
    to: '/orders',
  },
  {
    icon: faSquareCheck,
    label: 'Abgeschlossene Aufträge',
    to: '/posted-orders',
  },
  {
    icon: faChartPie,
    label: 'Statistiken',
    to: '/statistics',
  },
  {
    icon: faGear,
    label: 'Einstellungen',
    to: '/settings',
  },
  {
    icon: faUser,
    label: props.user.profile.name ?? 'Nicht gesetzter Name',
    to: '/user',
    customIconStyle: 'bg-cerulean-200 rounded-md p-3',
  },
]);

function isItemActive(item: SidebarNavItem) {
  return window.location.pathname.startsWith(item.to);
}
</script>

<template>
  <!-- Mobile Fullscreen Sidebar -->
  <transition name="fade">
    <aside v-if="props.isMobileMenuOpen" class="fixed inset-0 z-50 bg-white px-6 py-8 lg:hidden">
      <button class="text-right mb-6 text-2xl" @click="closeMobileMenu">
        <FontAwesomeIcon :icon="faXmark" />
      </button>

      <RouterLink
        v-for="(item, index) in items"
        :key="'mobile-' + index"
        :class="[
          'cursor-pointer px-4 py-3 rounded-lg flex items-center',
          isItemActive(item) ? (item.isItemBambooStyle ? 'bg-bamboo-100' : 'bg-cerulean-100') : '',
        ]"
        :to="item.to"
        @click="closeMobileMenu"
      >
        <FontAwesomeIcon
          :class="[
            item.isItemBambooStyle ? 'text-bamboo-600' : 'text-cerulean-600',
            'min-h-5 min-w-5',
            item.customIconStyle,
          ]"
          :icon="item.icon"
        />
        <span :class="[item.isItemBambooStyle ? 'text-bamboo-600 font-bold' : '', 'text-base ml-3']">
          {{ item.label }}
        </span>
      </RouterLink>
    </aside>
  </transition>

  <!-- Desktop Sidebar -->
  <aside v-if="!isMobile" class="w-60 px-4 py-8 flex flex-col gap-3 h-full overflow-y-auto">
    <RouterLink
      v-for="(item, index) in items"
      :key="index"
      :class="[
        'cursor-pointer px-4 py-3 rounded-lg flex items-center last:mt-auto',
        isItemActive(item) ? (item.isItemBambooStyle ? 'bg-bamboo-100' : 'bg-cerulean-100') : '',
      ]"
      :to="item.to"
    >
      <FontAwesomeIcon
        :class="[
          item.isItemBambooStyle ? 'text-bamboo-600' : 'text-cerulean-600',
          'min-h-5 min-w-5',
          item.customIconStyle,
        ]"
        :icon="item.icon"
      />
      <span :class="[item.isItemBambooStyle ? 'text-bamboo-600 font-bold' : '', 'text-base ml-3']">
        {{ item.label }}
      </span>
    </RouterLink>
  </aside>
</template>
