<script lang="ts" setup>
import type { Order } from '@/types/Order.type.ts';
import OpenInMapsWrapper from '@/components/OpenInMapsWrapper.vue';

defineProps<{
  order: Order;
}>();
</script>

<template>
  <OpenInMapsWrapper :address-object="order" class="flex flex-col">
    <span class="font-bold text-lg">{{ order.displayName }}</span>
    <span>{{ order.addressLine1 }}</span>
    <span>{{ order.postalCode }} {{ order.city }}</span>
  </OpenInMapsWrapper>

  <a v-if="order.phoneNumber" :href="`tel:${order.phoneNumber}`" class="mt-4" target="_blank">
    Telefon: {{ order.phoneNumber }}
  </a>
  <span v-else class="pt-4"> Telefon: <span class="px-2">–</span></span>

  <a v-if="order.email" :href="`mailto:${order.email}`" target="_blank">E-Mail: {{ order.email }}</a>
  <span v-else> E-Mail: <span class="px-2">–</span> </span>
</template>
