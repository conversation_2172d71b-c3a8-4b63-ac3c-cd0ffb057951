<script lang="ts" setup>
import AppointmentRequestItem, { type AppointmentRequestItemProps } from '@/components/AppointmentRequestItem.vue';
import { onMounted, onUnmounted, ref } from 'vue';
import { useAppointmentRequestsService } from '@/services/appointmentRequestsService.ts';
import OrderItemSkeleton from '@/components/OrderItemSkeleton.vue';
import { useNotificationRefresh } from '@/services/useNotificationRefresh.ts';

const { getPendingAppointmentRequests } = useAppointmentRequestsService();

const appointmentRequests = ref<AppointmentRequestItemProps[]>([]);
const isLoading = ref(true);

let intervalId: ReturnType<typeof setInterval>;

async function loadAppointmentRequests() {
  isLoading.value = true;
  return getPendingAppointmentRequests()
    .then((data) => {
      appointmentRequests.value = data;
    })
    .catch((err) => {
      console.error(err);
    })
    .finally(() => {
      isLoading.value = false;
    });
}

onMounted(async () => {
  await loadAppointmentRequests();
  countDownSecondsOfAppointmentRequestTimer();
});

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId);
  }
});

useNotificationRefresh(() => {
  loadAppointmentRequests();
});

function removeItem(appointmentRequestId: string) {
  appointmentRequests.value = appointmentRequests.value.filter(
    (appointmentRequest) => appointmentRequest.appointmentRequestId !== appointmentRequestId,
  );
}

function countDownSecondsOfAppointmentRequestTimer() {
  intervalId = setInterval(() => {
    appointmentRequests.value = appointmentRequests.value
      .map(
        (request: AppointmentRequestItemProps): AppointmentRequestItemProps => ({
          ...request,
          remainingSecondsToReply: request.remainingSecondsToReply ? request.remainingSecondsToReply - 1 : 0,
        }),
      )
      .filter((request) => request.remainingSecondsToReply > 0);
  }, 1000);
}
</script>

<template>
  <div class="flex flex-col">
    <h2 class="text-2xl font-bold mt-5 mb-3">Alle offenen Anfragen</h2>
    <OrderItemSkeleton v-if="isLoading" />
    <div v-else-if="appointmentRequests.length === 0">Keine offenen Anfragen</div>
    <div v-else class="gap-3 flex flex-col" test-id="appointment-request-container">
      <AppointmentRequestItem
        v-for="appointmentRequest in appointmentRequests"
        :key="appointmentRequest.appointmentRequestId"
        :address-line1="appointmentRequest.addressLine1"
        :address-line2="appointmentRequest.addressLine2"
        :appointment-request-id="appointmentRequest.appointmentRequestId"
        :city="appointmentRequest.city"
        :description="appointmentRequest.description"
        :is-asap="appointmentRequest.isAsap"
        :postcode="appointmentRequest.postcode"
        :remaining-seconds-to-reply="appointmentRequest.remainingSecondsToReply"
        :scheduled-date-time="appointmentRequest.scheduledDateTime"
        @remove-appointment-request="removeItem"
      />
    </div>
  </div>
</template>
