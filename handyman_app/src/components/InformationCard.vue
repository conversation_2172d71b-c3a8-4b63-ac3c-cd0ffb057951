<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import { faCheckCircle, faXmark } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { ref } from 'vue';

export type CardType = keyof typeof cardStyle;

defineProps<{
  cardType: CardType;
  message: string;
  hideHandymanImage?: boolean;
}>();

const cardStyle = {
  success: 'bg-akSuccessFlavor',
  information: 'bg-cerulean-100',
};

const iconStyle = {
  success: 'text-akSuccess',
  information: '',
};

const icon = {
  success: faCheckCircle,
  information: '',
};

const xStyle = {
  success: 'text-akSuccess',
  information: 'text-cerulean-700',
};

const isVisible = ref(true);
</script>

<template>
  <div
    v-if="isVisible"
    ref="topMostElement"
    :class="[cardStyle[cardType]]"
    class="flex w-full rounded-lg p-5 items-center"
    test-id="card"
  >
    <FontAwesomeIcon
      v-if="icon[cardType]"
      :class="[iconStyle[cardType]]"
      :icon="icon[cardType]"
      class="min-h-8 min-w-8"
    />
    <img
      v-else-if="!hideHandymanImage"
      alt="Smiling Handyman"
      class="h-11 w-auto"
      src="../assets/img/smiling_handyman.svg"
    />
    <span :class="[xStyle[cardType]]" class="text-base px-4 flex-grow font-semibold">{{ message }}</span>
    <slot name="custom-content" />
    <FontAwesomeIcon
      :class="[xStyle[cardType]]"
      :icon="faXmark"
      class="cursor-pointer h-3 w-3"
      @click="isVisible = false"
    />
  </div>
</template>
