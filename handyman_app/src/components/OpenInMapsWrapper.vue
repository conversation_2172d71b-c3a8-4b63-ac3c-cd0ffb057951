<script lang="ts" setup>
// If any of the simple string props is provided, it will take precedence.
// Otherwise, the addressObject will be used. If neither is available, an empty string will be returned.
export type OpenInMapsProps = {
  addressLine1?: string;
  city?: string;
  postalCode?: string;
  addressObject?: {
    addressLine1: string;
    city: string;
    postalCode?: string;
    postCode?: string;
  };
};

const props = defineProps<OpenInMapsProps>();

const openMap = () => {
  const { addressLine1, postalCode, city } = getAddressesWithFallback();
  openAddressInAppleMaps(addressLine1, postalCode, city);
};

function openAddressInAppleMaps(addressLine1: string, postalCode: string, city: string) {
  const address = `${addressLine1}, ${postalCode} ${city}`;
  const encodedAddress = encodeURIComponent(address);
  const url = `https://maps.apple.com/?q=${encodedAddress}`;
  window.open(url, '_blank');
}

function getAddressesWithFallback() {
  const addressLine1 = props.addressLine1 || props.addressObject?.addressLine1 || '';
  const postalCode = props.postalCode || props.addressObject?.postalCode || props.addressObject?.postCode || '';
  const city = props.city || props.addressObject?.city || '';

  if (!addressLine1 || !postalCode || !city) {
    console.warn('OpenInMaps: Missing address information.');
  }
  return { addressLine1, postalCode, city };
}
</script>

<template>
  <span class="cursor-pointer" @click="openMap">
    <slot />
  </span>
</template>
