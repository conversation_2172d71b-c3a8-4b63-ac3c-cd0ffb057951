<template>
  <div class="flex flex-col gap-3 animate-pulse">
    <div v-for="n in 3" :key="'skeleton-' + n" class="border border-gray-300 rounded-lg p-5 flex flex-col gap-5">
      <div class="flex justify-between items-center">
        <div class="flex gap-2 items-center">
          <div class="w-5 h-5 bg-gray-300 rounded-full"></div>
          <div class="h-4 bg-gray-300 rounded w-32"></div>
        </div>
        <div class="h-4 bg-gray-300 rounded w-24"></div>
      </div>
      <div class="h-4 bg-gray-300 rounded w-1/2"></div>
      <div class="flex gap-3">
        <div class="h-10 w-32 bg-gray-300 rounded"></div>
        <div class="h-10 w-32 bg-gray-300 rounded"></div>
      </div>
    </div>
  </div>
</template>
