<script lang="ts" setup>
import { defineEmits, defineProps } from 'vue';

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  step: {
    type: Number,
    default: 1,
  },
  min: {
    type: Number,
    default: 0,
  },
});
const emit = defineEmits(['update:modelValue']);
const increment = () => {
  const next = +(props.modelValue + props.step).toFixed(2);
  emit('update:modelValue', next);
};
const decrement = () => {
  const next = +(props.modelValue - props.step).toFixed(2);
  if (next < props.min) return;
  emit('update:modelValue', next);
};
const updateValue = (event: Event) => {
  const target = event.target as HTMLInputElement | null;
  if (target && target.value !== undefined) {
    const number = parseFloat(target.value.replace(',', '.'));
    if (!isNaN(number) && number >= props.min) {
      emit('update:modelValue', +number.toFixed(2));
    }
  }
};
</script>

<template>
  <div :class="{ 'opacity-60': disabled }" class="inline-flex items-center h-full w-full px-1">
    <template v-if="!(disabled || modelValue <= min)">
      <button
        class="flex-grow bg-transparent border-none text-xl py-1 px-3 rounded hover:bg-gray-100 active:bg-gray-200 cursor-pointer"
        @click="decrement"
      >
        -
      </button>
    </template>
    <template v-else>
      <div class="flex-grow py-1 px-3 text-xl invisible select-none">-</div>
    </template>

    <input
      :disabled="disabled"
      :min="min"
      :step="step"
      :value="modelValue"
      class="flex-grow-2 text-center border-none bg-transparent text-base w-12 focus:outline-none disabled:opacity-70 disabled:cursor-not-allowed [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
      type="number"
      @input="updateValue"
    />
    <button
      :disabled="disabled"
      class="flex-grow bg-transparent border-none text-xl py-1 px-3 cursor-pointer rounded hover:bg-gray-100 active:bg-gray-200 disabled:opacity-70 disabled:cursor-not-allowed"
      @click="increment"
    >
      +
    </button>
  </div>
</template>
