import { beforeEach, describe, expect, it, vi } from 'vitest';
import { flushPromises, mount } from '@vue/test-utils';
import dayjs from 'dayjs';
import type { Order } from '@/types/Order.type.ts';
import { defineComponent, ref } from 'vue';

let postSalesOrderMock: ReturnType<typeof vi.fn>;
let showLoadingToastMock: ReturnType<typeof vi.fn>;
let updateToSuccessToastMock: ReturnType<typeof vi.fn>;
let wrapper: ReturnType<typeof mount>;

const mockOrder: Order = {
  caseNumber: '100000',
  description: 'Verstopfter Abfluss / Abwasser',
  displayName: '<PERSON>',
  email: '<EMAIL>',
  phoneNumber: '+491711234567',
  addressLine1: 'Herschelstraße 1, 10589 Berlin, Deutschland',
  city: 'Berlin',
  postalCode: '10589',
  amountExcludingVAT: 116.15,
  amountIncludingVAT: 138.22,
  expirationDateTime: dayjs('2025-05-13T11:55:34+02:00'),
  scheduledDateTime: dayjs().add(2, 'days'),
  isAsap: true,
  systemId: '5a0a954f-114f-a111-9b7a-6258bc746214',
  orderLines: [
    {
      description: 'Anfahrtskosten',
      quantity: 1,
      unitPrice: 29.3,
      unitOfMeasure: 'Pauschale',
      systemId: 'OL-1000',
      type: 'Item',
      lineObjectNumber: '1000',
    },
    {
      description: 'Handwerkerleistung',
      quantity: 1,
      unitPrice: 81.9,
      unitOfMeasure: 'Stunde',
      systemId: 'OL-1001',
      type: 'Resource',
      lineObjectNumber: 'R0020',
    },
    {
      description: 'Kleinteile-Pauschale',
      quantity: 1,
      unitPrice: 4.95,
      unitOfMeasure: 'Pauschale',
      systemId: 'OL-1002',
      type: 'Item',
      lineObjectNumber: '1003',
    },
  ],
  billToName: 'Mara Mustermann',
  billToAddress: 'Herschelstraße 1, 10589 Berlin, Deutschland',
  billToCity: 'Berlin',
  billToPostCode: '10589',
};

describe('InvoicePopup.vue', () => {
  beforeEach(async () => {
    postSalesOrderMock = vi.fn().mockResolvedValue({
      ok: true,
      json: async () => ({ mocked: 'response' }),
    });

    showLoadingToastMock = vi.fn(() => 'mock-toast-id');
    updateToSuccessToastMock = vi.fn();

    vi.doMock('@/services/useInvoice.ts', () => ({
      useInvoice: () => ({
        getSalesOrderLineOptions: vi.fn(),
        postSalesOrder: postSalesOrderMock,
      }),
    }));

    vi.doMock('@/Toaster.ts', () => ({
      showLoadingToast: showLoadingToastMock,
      updateToSuccessToast: updateToSuccessToastMock,
      updateToErrorToast: vi.fn(),
    }));

    const InvoiceSigningStub = defineComponent({
      name: 'InvoiceSigning',
      setup(_, { expose }) {
        const hasSignature = ref(true);
        const getDataURL = () => 'data:image/png;base64,iVB...II=';
        expose({ hasSignature, getDataURL });
        return () => null;
      },
    });

    const mod = await import('@/components/InvoicePopup.vue');
    wrapper = mount(mod.default, {
      props: { order: mockOrder },
      global: {
        stubs: { FontAwesomeIcon: true, InvoiceSigning: InvoiceSigningStub },
      },
    });

    await flushPromises();
  });

  it('renders the correct title with case number', () => {
    const text = wrapper.text();

    const titleIncludes = text.includes('Rechnung');
    const caseNumberIncludes = text.includes(mockOrder.caseNumber);

    expect(titleIncludes).toBe(true);
    expect(caseNumberIncludes).toBe(true);
  });

  it('opens confirmation popup when clicking "Rechnung bestätigen"', async () => {
    const buttons = wrapper.findAllComponents({ name: 'DefaultButton' });
    const confirmButton = buttons[1];
    await confirmButton.trigger('click');
    const popup = wrapper.findComponent({ name: 'DefaultPopup' });

    const popupExists = popup.exists();
    const containsText = popup.text().includes('Kundenbestätigung');

    expect(popupExists).toBe(true);
    expect(containsText).toBe(true);
  });

  it('confirms invoice and emits confirm event with API response', async () => {
    const confirmButton = wrapper.findAllComponents({ name: 'DefaultButton' })[1];
    await confirmButton.trigger('click');
    const popup = wrapper.findComponent({ name: 'DefaultPopup' });
    const popupConfirmButton = popup.findAllComponents({ name: 'DefaultButton' })[1];
    await popupConfirmButton.trigger('click');

    const emitted = wrapper.emitted('confirm');

    expect(postSalesOrderMock).toHaveBeenCalledWith(
      mockOrder.caseNumber,
      mockOrder.systemId,
      'data:image/png;base64,iVB...II=',
    );
    expect(emitted).toBeTruthy();
    expect(emitted![0][0]).toEqual({ mocked: 'response' });
    expect(wrapper.findComponent({ name: 'DefaultPopup' }).exists()).toBe(false);
  });
});
