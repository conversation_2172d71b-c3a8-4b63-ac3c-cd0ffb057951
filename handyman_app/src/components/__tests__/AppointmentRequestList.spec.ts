import { flushPromises, mount } from '@vue/test-utils';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { createTestingPinia } from '@pinia/testing';
import AppointmentRequestList from '@/components/AppointmentRequestList.vue';
import AppointmentRequestElement from '../AppointmentRequestItem.vue';
import dayjs from 'dayjs';
import type { AppointmentRequest } from '@/types/AppointmentRequest.type.ts';

let mockFetchAppointmentRequests: () => AppointmentRequest[];

vi.mock('@/services/appointmentRequestsService.ts', () => ({
  useAppointmentRequestsService: () => ({
    getPendingAppointmentRequests: mockFetchAppointmentRequests,
  }),
}));

beforeEach(() => {
  mockFetchAppointmentRequests = vi.fn().mockResolvedValue([
    { isAsap: true, expirationDateTime: dayjs().add(5, 'minutes'), description: 'Verstopfter Abfluss/Abwasser' },
    { scheduledDateTime: dayjs('2024-11-02 14:00'), description: 'Heizung defekt' },
    { isAsap: true, expirationDateTime: dayjs().add(5, 'minutes'), description: 'Wasserschaden im Badezimmer' },
  ] as AppointmentRequest[]);
});

describe('AppointmentRequestList.vue', () => {
  it('renders three AppointmentRequestElements with correct content', async () => {
    const wrapper = mount(AppointmentRequestList, {
      global: {
        plugins: [createTestingPinia({ createSpy: vi.fn })],
      },
    });

    await flushPromises();

    const elements = wrapper.findAllComponents(AppointmentRequestElement);
    expect(elements).toHaveLength(3);

    expect(elements[0].html()).toContain('So schnell wie möglich');
    expect(elements[0].props('isAsap')).toBe(true);
    expect(elements[0].props('description')).toBe('Verstopfter Abfluss/Abwasser');

    expect(elements[1].html()).toContain('02.11.2024, 14 Uhr');
    expect(elements[1].props('isAsap')).toBeFalsy();
    expect(elements[1].props('description')).toBe('Heizung defekt');

    expect(elements[2].html()).toContain('So schnell wie möglich');
    expect(elements[2].props('isAsap')).toBe(true);
    expect(elements[2].props('description')).toBe('Wasserschaden im Badezimmer');
  });
});
