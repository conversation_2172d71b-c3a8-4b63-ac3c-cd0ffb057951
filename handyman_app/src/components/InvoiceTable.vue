<script lang="ts" setup>
import type { Order } from '@/types/Order.type.ts';
import { formatPrice, formatUnitOfMeasure } from '../utils/invoice.ts';

defineProps<{
  order: Order;
}>();
</script>

<template>
  <div class="overflow-x-auto">
    <table class="min-w-full border-separate border-spacing-y-2">
      <thead>
        <tr class="text-left">
          <th class="font-bold">Bezeichnung</th>
          <th class="font-bold text-right">Menge</th>
          <th class="font-bold text-right">Preis</th>
          <th class="font-bold text-right">Netto</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="line in order.orderLines" :key="line.systemId">
          <td>{{ line.description }}</td>
          <td class="text-right">{{ line.quantity }} {{ formatUnitOfMeasure(line.unitOfMeasure) }}</td>
          <td class="text-right whitespace-nowrap">{{ formatPrice(line.unitPrice) }}</td>
          <td class="text-right whitespace-nowrap">{{ formatPrice(line.unitPrice * line.quantity) }}</td>
        </tr>
        <tr>
          <td colspan="4">
            <div class="border-t-2 border-gray-300 my-2" />
          </td>
        </tr>
        <tr>
          <td class="font-semibold" colspan="3">Zwischensumme</td>
          <td class="text-right">{{ formatPrice(order.amountExcludingVAT) }}</td>
        </tr>
        <tr>
          <td class="font-semibold" colspan="3">19% MwSt.</td>
          <td class="text-right">{{ formatPrice(order.amountIncludingVAT - order.amountExcludingVAT) }}</td>
        </tr>
        <tr class="font-semibold">
          <td colspan="3">Gesamtbetrag</td>
          <td class="text-right">{{ formatPrice(order.amountIncludingVAT) }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
