<script lang="ts" setup>
import AsapOrderItem from '@/components/orders/AsapOrderItem.vue';
import ScheduledOrderItem from '@/components/orders/ScheduledOrderItem.vue';
import type { Order } from '@/types/Order.type.ts';
import OrderItemSkeleton from '@/components/OrderItemSkeleton.vue';

defineProps<{
  asapOrders: Order[];
  scheduledOrders: Order[];
  isLoading: boolean;
}>();
</script>

<template>
  <div class="flex flex-col">
    <h2 class="text-2xl font-bold mt-5 mb-3">Meine Aufträge</h2>
    <OrderItemSkeleton v-if="isLoading" />
    <div v-else-if="asapOrders.length === 0 && scheduledOrders.length === 0">Derzeit keine Aufträge vorhanden</div>
    <div v-else class="gap-3 flex flex-col" test-id="order-container">
      <AsapOrderItem
        v-for="order in asapOrders"
        :key="order.caseNumber"
        :address-line1="order.addressLine1"
        :case-number="order.caseNumber"
        :city="order.city"
        :description="order.description"
        :expiration-date-time="order.expirationDateTime"
        :postal-code="order.postalCode"
      />
      <ScheduledOrderItem
        v-for="(order, index) in scheduledOrders"
        :key="index"
        :address-line1="order.addressLine1"
        :case-number="order.caseNumber"
        :city="order.city"
        :description="order.description"
        :postal-code="order.postalCode"
        :scheduled-date-time="order.scheduledDateTime"
      ></ScheduledOrderItem>
    </div>
  </div>
</template>
