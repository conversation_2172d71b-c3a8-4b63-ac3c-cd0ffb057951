<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { faHourglassHalf, faTruck } from '@fortawesome/free-solid-svg-icons';
import DefaultButton from '@/components/DefaultButton.vue';
import type { Dayjs } from 'dayjs';
import CountdownTimer from '@/components/CountdownTimer.vue';
import NavigationButton from '@/components/NavigationButton.vue';
import router from '@/router/routes.ts';
import OpenInMapsWrapper from '@/components/OpenInMapsWrapper.vue';

export type AsapOrderItemProps = {
  addressLine1: string;
  city: string;
  description: string;
  expirationDateTime: Dayjs;
  caseNumber: string;
  postalCode: string;
};

const props = defineProps<AsapOrderItemProps>();

const navigateToDetails = () => {
  router.push({ name: 'order-details', params: { caseNumber: props.caseNumber } });
};
</script>

<template>
  <div class="bg-gradient-bamboo text-white w-full px-6 py-5 rounded-lg flex flex-col gap-3" test-id="asap-order-item">
    <div class="flex">
      <div class="grow flex gap-2 items-center">
        <FontAwesomeIcon :icon="faTruck" />
        <span class="text-lg font-bold">So schnell wie möglich!</span>
      </div>
      <div class="flex gap-2 items-center">
        <FontAwesomeIcon :icon="faHourglassHalf" />
        <CountdownTimer :to="props.expirationDateTime" class="font-bold" />
      </div>
    </div>
    <span class="font-medium text-base break-words break-all whitespace-normal overflow-hidden">
      „{{ props.description }}“
    </span>
    <div class="flex flex-col md:flex-row md:items-center gap-2 md:gap-0">
      <div class="flex flex-col sm:flex-row gap-2 flex-1">
        <DefaultButton
          class="bg-white text-bamboo-600 w-full md:w-auto md:max-w-[200px] justify-center text-center"
          @click="navigateToDetails()"
        >
          <span class="text-base">Details ansehen</span>
        </DefaultButton>
        <NavigationButton
          :addressLine1="props.addressLine1"
          :city="props.city"
          :postalCode="props.postalCode"
          class="text-white w-full md:w-auto md:max-w-[200px] justify-center text-center"
        />
      </div>
      <OpenInMapsWrapper :addressObject="props" class="mt-1 sm:mt-2 md:mt-0 md:ml-4 w-full md:w-auto">
        {{ props.addressLine1 }}, {{ props.postalCode }} {{ props.city }}
      </OpenInMapsWrapper>
    </div>
  </div>
</template>
