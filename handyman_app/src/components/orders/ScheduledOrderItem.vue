<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { faClock } from '@fortawesome/free-solid-svg-icons';
import DefaultButton from '@/components/DefaultButton.vue';
import dayjs, { type Dayjs } from 'dayjs';
import NavigationButton from '@/components/NavigationButton.vue';
import router from '@/router/routes.ts';
import OpenInMapsWrapper from '@/components/OpenInMapsWrapper.vue';

export type ScheduledOrderItemProps = {
  addressLine1: string;
  city: string;
  description: string;
  scheduledDateTime?: Dayjs;
  caseNumber: string;
  postalCode: string;
};

const props = defineProps<ScheduledOrderItemProps>();

const navigateToDetails = () => {
  router.push({ name: 'order-details', params: { caseNumber: props.caseNumber } });
};
</script>

<template>
  <div
    class="text-cerulean-950 outline-1 w-full px-6 py-5 rounded-lg flex flex-col gap-3"
    test-id="scheduled-order-item"
  >
    <div class="flex">
      <div class="grow flex gap-2 items-center">
        <FontAwesomeIcon :icon="faClock" />
        <span class="text-lg font-bold">{{ dayjs(props.scheduledDateTime).format(`DD.MM.YYYY, H [Uhr]`) }}</span>
      </div>
    </div>
    <span class="font-medium text-base break-words break-all whitespace-normal overflow-hidden">
      „{{ props.description }}“
    </span>
    <div class="flex flex-col md:flex-row md:items-center gap-2 md:gap-0">
      <div class="flex flex-col sm:flex-row gap-2 flex-1">
        <DefaultButton
          class="w-full md:w-auto md:max-w-[200px] justify-center text-center bg-gradient-bamboo text-white"
          @click="navigateToDetails()"
        >
          <span class="text-base">Details ansehen</span>
        </DefaultButton>
        <NavigationButton
          :addressLine1="props.addressLine1"
          :city="props.city"
          :postalCode="props.postalCode"
          class="text-bamboo-500 w-full md:w-auto md:max-w-[200px] justify-center text-center"
        />
      </div>
      <OpenInMapsWrapper :addressObject="props" class="mt-1 sm:mt-2 md:mt-0 md:ml-4 w-full md:w-auto">
        {{ props.addressLine1 }}, {{ props.postalCode }} {{ props.city }}
      </OpenInMapsWrapper>
    </div>
  </div>
</template>
