<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useOrderService } from '@/services/orderService.ts';
import { faArrowLeft, faClock, faHourglassHalf, faTruck } from '@fortawesome/free-solid-svg-icons';
import CountdownTimer from '@/components/CountdownTimer.vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import dayjs from 'dayjs';
import router from '@/router/routes.ts';
import type { Order } from '@/types/Order.type.ts';
import InvoicePopup from '@/components/InvoicePopup.vue';
import InvoiceEditPopup from '@/components/InvoiceEditPopup.vue';
import DocumentButton from '@/components/DocumentButton.vue';
import OrderDetailsAddress from '@/components/OrderDetailsAddress.vue';
import OrderDetailsBillingAddress from '@/components/OrderDetailsBillingAddress.vue';
import { usePostedOrderService } from '@/services/postedOrderService.ts';
import { useOrdersStore } from '@/stores/ordersStore.ts';

const props = defineProps<{
  caseNumber: string;
}>();

const { getSingleOrder } = useOrderService();

const order = ref<Order>();
const isLoading = ref(true);
const showPopup = ref(false);
const showEditPopup = ref(false);

onMounted(async () => {
  order.value = await getSingleOrder(props.caseNumber);

  isLoading.value = false;
});

const navigateToOverview = async () => {
  await router.push({ name: 'orders' });
};

const navigateToPostedOrderDetails = async (caseNumber: string) => {
  await router.push({ name: 'posted-order-details', params: { caseNumber } });
};

function handleSave() {
  showEditPopup.value = false;
  refetchAllOrdersAndGetSingleUpdatedOrder();
}

function handleConfirmation(postedOrder: Order) {
  usePostedOrderService().setSinglePostedOrder(postedOrder);
  navigateToPostedOrderDetails(postedOrder.caseNumber);
}

function refetchAllOrdersAndGetSingleUpdatedOrder() {
  useOrdersStore().$reset();
  getSingleOrder(props.caseNumber).then((updatedOrder) => (order.value = updatedOrder));
}
</script>

<template>
  <div class="flex flex-col">
    <button class="text-cerulean-600 py-2 mb-3 self-start" @click.prevent="navigateToOverview">
      <FontAwesomeIcon :icon="faArrowLeft" class="mr-2" />
      <span>Zurück zur Übersicht</span>
    </button>
    <span v-if="isLoading">Auftrag wird geladen...</span>
    <span v-else-if="!order">Auftrag konnte nicht geladen werden.</span>

    <template v-else>
      <div v-if="order.isAsap" class="flex text-white bg-gradient-bamboo w-full px-4 py-3 rounded-lg">
        <div class="grow flex gap-2 items-center text-lg">
          <FontAwesomeIcon :icon="faTruck" />
          <span class="font-bold">So schnell wie möglich!</span>
        </div>
        <div class="flex gap-2 items-center">
          <FontAwesomeIcon :icon="faHourglassHalf" />
          <CountdownTimer :to="order.expirationDateTime" class="font-bold" />
        </div>
      </div>

      <div v-else class="flex text-white bg-gradient-cerulean w-full px-4 py-3 rounded-lg gap-2 items-center">
        <FontAwesomeIcon :icon="faClock" />
        <span class="text-lg font-bold">
          {{ dayjs(order.scheduledDateTime).format(`DD.MM.YYYY, H [Uhr]`) }}
        </span>
      </div>

      <div class="pt-5 flex flex-wrap gap-x-5 gap-y-5 text-base text-cerulean-950">
        <div class="flex-4 min-w-xs">
          <span>Vorgangsnummer: {{ order.caseNumber }}</span>
          <p class="mt-2 font-bold text-lg mb-4 break-words whitespace-normal overflow-hidden hyphens-auto">
            {{ order.description }}
          </p>
        </div>

        <div class="pr-4 flex-2 flex flex-col">
          <OrderDetailsAddress :order="order" />
          <span class="border border-gray-200 mt-4 mb-4" />
          <OrderDetailsBillingAddress :order="order" />
          <span class="border border-gray-200 mt-4 mb-8" />
          <DocumentButton
            :file-name="`Rechnung_${order.caseNumber}`"
            class="w-full"
            @click.prevent="showPopup = true"
          />
        </div>
      </div>
    </template>
  </div>
  <!--  TODO: implement confirm or print before story is done -->
  <InvoicePopup
    v-if="showPopup && order"
    :order="order"
    @close="showPopup = false"
    @confirm="handleConfirmation"
    @edit="showEditPopup = true"
  />
  <InvoiceEditPopup v-if="showEditPopup && order" :order="order" @close="showEditPopup = false" @save="handleSave" />
</template>
