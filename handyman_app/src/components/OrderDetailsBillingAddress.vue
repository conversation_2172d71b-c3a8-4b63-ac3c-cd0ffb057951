<script lang="ts" setup>
import type { Order } from '@/types/Order.type.ts';
import OpenInMapsWrapper from '@/components/OpenInMapsWrapper.vue';

defineProps<{
  order: Order;
}>();
</script>

<template v-if="order && order.billToName">
  <OpenInMapsWrapper :address-object="order" class="flex flex-col">
    <span class="text-lg mb-2">Rechnungsadresse:</span>

    <span>{{ order.billToName }}</span>
    <span>{{ order.billToAddress }}</span>
    <span>{{ order.billToPostCode }} {{ order.billToCity }}</span>
  </OpenInMapsWrapper>
</template>
