<script lang="ts" setup>
import { computed, defineEmits, defineProps, ref } from 'vue';
import DefaultButton from '@/components/DefaultButton.vue';
import type { Order } from '@/types/Order.type.ts';
import { faPlus, faX } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { formatPrice } from '../utils/invoice.ts';
import { useInvoice } from '@/services/useInvoice.ts';
import { updateSalesOrderLines } from '@/services/invoiceService.ts';
import LoadingSpinner from '@/components/LoadingSpinner.vue';
import NumberInput from '@/components/NumberInput.vue';
import type { SalesOrderLineUpdateRequest } from '@/types/SalesOrderLineUpdateRequest.type.ts';
import { useSalesOrderLineOptionsStore } from '@/stores/salesOrderLineOptionsStore.ts';

const props = defineProps<{
  order: Order;
}>();

const emit = defineEmits(['close', 'save']);

const orderLines = ref<SalesOrderLineUpdateRequest[]>(JSON.parse(JSON.stringify(props.order.orderLines)));
const isSavingInProgress = ref<boolean>(false);

const salesLineOptions = useSalesOrderLineOptionsStore().options;
const optionsByLineObject = computed(() => Object.fromEntries(salesLineOptions.map((o) => [o.lineObjectNumber, o])));
const getOption = (lineObjectNumber: string) => optionsByLineObject.value[lineObjectNumber];

const isNonDeletable = (line: SalesOrderLineUpdateRequest) => {
  const defaultLineObjectNumbers = props.order.orderLines.map((l) => l.lineObjectNumber);
  const option = getOption(line.lineObjectNumber);
  return option?.rules?.deletable === false && defaultLineObjectNumbers.includes(line.lineObjectNumber);
};

const isQuantityImmutable = (line: SalesOrderLineUpdateRequest) => {
  return getOption(line.lineObjectNumber)?.rules?.quantityEditable === false;
};

const isDuplicateAllowed = (lineObjectNumber: string) => {
  return getOption(lineObjectNumber)?.rules?.duplicateAllowed === true;
};

const availableOptions = computed(() =>
  salesLineOptions.filter((option) => {
    if (option.rules?.duplicateAllowed) return true;

    const alreadyUsed = orderLines.value.some((line) => line.lineObjectNumber === option.lineObjectNumber);

    return !alreadyUsed;
  }),
);

const addOrderLine = async () => {
  const remainingOptions = availableOptions.value;

  const newEmptyLine: SalesOrderLineUpdateRequest = {
    description: '',
    quantity: 1,
    unitPrice: 0,
    lineObjectNumber: '',
    type: '',
  };

  if (remainingOptions.length === 1) {
    orderLines.value.push(await automaticallySetTheLastRemainingOption());
  } else {
    orderLines.value.push(newEmptyLine);
  }

  async function automaticallySetTheLastRemainingOption() {
    const option = remainingOptions[0];
    const unitPrice = await useInvoice().getUnitPrices({ ...option, quantity: 1 });

    return { ...option, quantity: 1, unitPrice };
  }
};
const removeOrderLine = (index: number) => {
  const line = orderLines.value[index];
  if (isNonDeletable(line)) return;
  orderLines.value.splice(index, 1);
};

const subtotal = computed(() => calculateSubtotal(orderLines.value));
const vat = computed(() => calculateVat(subtotal.value, 0.19));
const total = computed(() => calculateTotal(subtotal.value, vat.value));

function calculateSubtotal(orderLines: SalesOrderLineUpdateRequest[]): number {
  return orderLines.reduce((sum, line) => sum + line.quantity * line.unitPrice, 0);
}

function calculateVat(subtotal: number, vatPercentage: number): number {
  return subtotal * vatPercentage;
}

function calculateTotal(subtotal: number, vat: number): number {
  return subtotal + vat;
}

function isDuplicateSelectionDisabled(lineObjectNumber: string, currentIndex: number): boolean {
  return orderLines.value.some(
    (line, index) =>
      index !== currentIndex && line.lineObjectNumber === lineObjectNumber && !isDuplicateAllowed(lineObjectNumber),
  );
}

function selectableOptions(currentIndex: number) {
  return salesLineOptions.filter((option) => {
    if (option.rules?.duplicateAllowed) return true;

    const alreadyUsed = orderLines.value.some(
      (line, index) => index !== currentIndex && line.lineObjectNumber === option.lineObjectNumber,
    );

    return !alreadyUsed;
  });
}

function resetSelection(line: SalesOrderLineUpdateRequest) {
  line.lineObjectNumber = '';
}

async function updateSalesOrderLineOnChange(line: SalesOrderLineUpdateRequest, index: number) {
  const selectedOption = getOption(line.lineObjectNumber);
  if (!selectedOption) return;

  if (isDuplicateSelectionDisabled(selectedOption.lineObjectNumber, index)) {
    resetSelection(line);
    return;
  }

  const newQuantity = selectedOption.rules?.minQuantity ?? 1;

  const updatedLine = {
    ...selectedOption,
    quantity: newQuantity,
    unitPrice: await useInvoice().getUnitPrices({ ...selectedOption, quantity: newQuantity }),
  };

  orderLines.value.splice(index, 1, updatedLine);
}

function close() {
  emit('close');
}

async function save() {
  isSavingInProgress.value = true;
  try {
    await updateSalesOrderLines(props.order.systemId, orderLines.value);
    emit('save', orderLines.value);
  } finally {
    isSavingInProgress.value = false;
  }
}
</script>

<template>
  <div class="fixed inset-0 z-99 flex items-center justify-center p-4" @click.prevent="close">
    <div class="bg-white rounded-2xl shadow-xl w-full max-w-4xl max-h-full p-6 overflow-y-auto" @click.stop>
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">
          Rechnung <span class="font-normal text-lg">Auftragsnummer: {{ props.order.caseNumber }}</span>
        </h2>
        <FontAwesomeIcon :icon="faX" class="text-gray-400 p-2 -m-2 cursor-pointer" @click.prevent="close" />
      </div>
      <div class="border-t-2 border-gray-300 -mx-6 mb-2"></div>

      <div class="overflow-x-auto -mx-2">
        <table class="min-w-full border-separate border-spacing-y-2 border-spacing-x-2">
          <thead>
            <tr class="text-left">
              <th class="font-bold w-5/10">Bezeichnung</th>
              <th class="font-bold text-right w-1/10">Menge</th>
              <th class="font-bold text-right w-1/10">Preis</th>
              <th class="font-bold text-right w-1/10">Netto</th>
              <th class="font-bold text-center w-1/20"></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(line, index) in orderLines" :key="line.lineObjectNumber + index">
              <td>
                <div v-if="isNonDeletable(line)" class="h-12 flex items-center rounded-md p-1 px-4 w-full text-base">
                  {{ getOption(line.lineObjectNumber)?.description }}
                </div>

                <div v-else class="h-12 flex items-center rounded-md px-2 border border-gray-300 w-full">
                  <select
                    v-model="line.lineObjectNumber"
                    class="w-full bg-transparent border-none text-base p-1"
                    @change="updateSalesOrderLineOnChange(line, index)"
                  >
                    <option disabled value="">Bitte wählen</option>
                    <option
                      v-for="option in selectableOptions(index)"
                      :key="option.lineObjectNumber"
                      :value="option.lineObjectNumber"
                    >
                      {{ option.description }}
                    </option>
                  </select>
                </div>
              </td>

              <td>
                <template v-if="isQuantityImmutable(line)">
                  <div class="h-12 px-1 flex items-center justify-center text-base text-right text-gray-800 w-full">
                    {{ line.quantity }}
                  </div>
                </template>
                <template v-else>
                  <div class="h-12 rounded-md border border-gray-300">
                    <NumberInput
                      v-model="line.quantity"
                      :min="getOption(line.lineObjectNumber)?.rules?.minQuantity ?? 1"
                      :step="getOption(line.lineObjectNumber)?.rules?.stepSize ?? 1"
                    />
                  </div>
                </template>
              </td>
              <td class="text-right whitespace-nowrap">
                {{ formatPrice(line.unitPrice) }}
              </td>
              <td class="text-right whitespace-nowrap">
                {{ formatPrice(line.quantity * line.unitPrice) }}
              </td>
              <td>
                <div class="h-12 flex align-center w-full">
                  <FontAwesomeIcon
                    v-if="!isNonDeletable(line)"
                    :icon="faX"
                    class="p-3 text-gray-500 m-auto text-xs cursor-pointer"
                    @click.prevent="removeOrderLine(index)"
                  />
                </div>
              </td>
            </tr>

            <tr v-if="availableOptions.length > 0">
              <td colspan="5">
                <div class="flex items-center gap-2 pt-2">
                  <div
                    class="text-bamboo-600 cursor-pointer"
                    test-id="add-sales-order-line"
                    @click.prevent="addOrderLine"
                  >
                    <FontAwesomeIcon :icon="faPlus" class="ml-1 mr-3" />
                    <span>Weitere Position hinzufügen</span>
                  </div>
                </div>
              </td>
            </tr>

            <tr>
              <td colspan="5">
                <div class="border-t-2 border-gray-300 my-2"></div>
              </td>
            </tr>
            <tr>
              <td class="font-semibold" colspan="3">Zwischensumme</td>
              <td class="text-right whitespace-nowrap" colspan="2">{{ formatPrice(subtotal) }}</td>
            </tr>
            <tr>
              <td class="font-semibold" colspan="3">19% MwSt.</td>
              <td class="text-right whitespace-nowrap" colspan="2">{{ formatPrice(vat) }}</td>
            </tr>
            <tr class="font-semibold">
              <td colspan="3">Gesamtbetrag</td>
              <td class="text-right whitespace-nowrap" colspan="2">{{ formatPrice(total) }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="flex mt-6 gap-4 flex-col sm:gap-6 sm:flex-row justify-between">
        <DefaultButton
          :disabled="isSavingInProgress"
          class="bg-white border border-bamboo-600 text-bamboo-600 disabled:border-bamboo-200 disabled:text-bamboo-200 basis-1/3"
          @click.prevent="close"
        >
          Verwerfen
        </DefaultButton>
        <LoadingSpinner v-if="isSavingInProgress" class="basis-1/3 flex justify-center" />
        <DefaultButton v-if="!isSavingInProgress" class="bg-gradient-bamboo text-white basis-1/3" @click.prevent="save">
          Speichern
        </DefaultButton>
      </div>
    </div>
  </div>
</template>
