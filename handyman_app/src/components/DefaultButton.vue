<script lang="ts" setup>
import type { IconDefinition } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

defineProps<{
  icon?: IconDefinition;
}>();
</script>

<template>
  <button
    :class="['rounded-lg text-center items-center px-4 py-2 cursor-pointer disabled:cursor-not-allowed']"
    v-bind="$attrs"
  >
    <FontAwesomeIcon v-if="icon" :icon="icon" class="mr-2" />
    <slot />
  </button>
</template>
