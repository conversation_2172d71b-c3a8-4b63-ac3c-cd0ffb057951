<script lang="ts" setup>
import { computed } from 'vue';
import { useGlobalTime } from '@/services/useGlobalTime';
import dayjs, { Dayjs } from 'dayjs';
import duration from 'dayjs/plugin/duration';
import utc from 'dayjs/plugin/utc';

dayjs.extend(duration);
dayjs.extend(utc);

const props = defineProps<{
  to: Dayjs;
  hideHours?: boolean;
  customClasses?: string[];
}>();

const now = useGlobalTime();

const remainingTimeFormatted = computed(() => {
  const remainingTime = dayjs.utc(Math.max(0, props.to.diff(now.value, 'millisecond')));
  return props.hideHours ? remainingTime.format('mm:ss') : remainingTime.format('HH:mm:ss');
});
</script>

<template>
  <span :class="$attrs.class">{{ remainingTimeFormatted }}</span>
</template>
