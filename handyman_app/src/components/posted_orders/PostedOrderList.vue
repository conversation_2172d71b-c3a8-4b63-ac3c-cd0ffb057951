<script lang="ts" setup>
import PostedOrderItem from '@/components/posted_orders/PostedOrderItem.vue';
import type { Order } from '@/types/Order.type.ts';
import OrderItemSkeleton from '@/components/OrderItemSkeleton.vue';

defineProps<{
  postedOrders: Order[];
  isLoading: boolean;
}>();
</script>

<template>
  <div class="flex flex-col">
    <h2 class="text-2xl font-bold mt-5 mb-3">Meine abgeschlossenen Aufträge</h2>
    <OrderItemSkeleton v-if="isLoading" />
    <div v-else-if="postedOrders.length === 0">Keine abgeschlossenen Aufträge vorhanden</div>
    <div v-else class="gap-3 flex flex-col" test-id="order-container">
      <PostedOrderItem v-for="(order, index) in postedOrders" :key="index" :order="order" />
    </div>
  </div>
</template>
