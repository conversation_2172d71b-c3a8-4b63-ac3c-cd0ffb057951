<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { faArrowLeft, faClock, faTruck } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import router from '@/router/routes.ts';
import type { Order } from '@/types/Order.type.ts';
import { usePostedOrderService } from '@/services/postedOrderService.ts';
import DocumentButton from '@/components/DocumentButton.vue';
import dayjs from 'dayjs';
import PostedInvoicePopup from '@/components/PostedInvoicePopup.vue';
import OrderDetailsAddress from '@/components/OrderDetailsAddress.vue';
import OrderDetailsBillingAddress from '@/components/OrderDetailsBillingAddress.vue';

const props = defineProps<{
  caseNumber: string;
}>();

const order = ref<Order>();
const isLoading = ref(true);
const showPopup = ref(false);

onMounted(async () => {
  order.value = await usePostedOrderService().getSinglePostedOrder(props.caseNumber);
  isLoading.value = false;
});

const navigateToOverview = async () => {
  await router.push({ name: 'posted-orders' });
};

function notImplementedYet() {
  alert('not implemented yet');
}
</script>

<template>
  <div class="flex flex-col">
    <button class="text-cerulean-600 py-2 mb-3 self-start" @click.prevent="navigateToOverview">
      <FontAwesomeIcon :icon="faArrowLeft" class="mr-2" />
      <span>Zurück zur Übersicht</span>
    </button>
    <span v-if="isLoading">Auftrag wird geladen...</span>
    <span v-else-if="!order">Auftrag konnte nicht geladen werden.</span>

    <template v-else>
      <div
        :class="order.isAsap ? 'bg-gradient-bamboo' : 'bg-gradient-cerulean'"
        class="flex text-white w-full px-4 py-3 rounded-lg gap-2 items-center"
      >
        <FontAwesomeIcon :icon="order.isAsap ? faTruck : faClock" />
        <span class="text-lg font-bold">
          {{ dayjs(order.scheduledDateTime || order.expirationDateTime).format(`DD.MM.YYYY, H [Uhr]`) }}
        </span>
      </div>

      <div class="pt-5 flex flex-wrap gap-x-5 gap-y-5 text-base text-cerulean-950">
        <div class="flex-4 min-w-xs">
          <span>Vorgangsnummer: {{ order.caseNumber }}</span>
          <p class="mt-2 font-bold text-lg mb-4 break-words whitespace-normal overflow-hidden">
            {{ order.description }}
          </p>
        </div>

        <div class="pr-4 flex-2 flex flex-col">
          <OrderDetailsAddress :order="order" />
          <span class="border border-gray-200 mt-4 mb-4" />
          <OrderDetailsBillingAddress :order="order" />
          <span class="border border-gray-200 mt-4 mb-8" />
          <DocumentButton
            :file-name="`Rechnung_${order.caseNumber}`"
            class="w-full"
            @click.prevent="showPopup = true"
          />
        </div>
      </div>
    </template>
  </div>
  <PostedInvoicePopup
    v-if="showPopup && order"
    :order="order"
    @close="showPopup = false"
    @download="notImplementedYet"
    @print="notImplementedYet"
  />
</template>
