<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { faClock } from '@fortawesome/free-solid-svg-icons';
import dayjs from 'dayjs';
import DefaultButton from '@/components/DefaultButton.vue';
import type { Order } from '@/types/Order.type.ts';
import router from '@/router/routes.ts';
import OpenInMapsWrapper from '@/components/OpenInMapsWrapper.vue';

defineProps<{ order: Order }>();

const navigateToPostedOrderDetails = async (caseNumber: string) => {
  await router.push({ name: 'posted-order-details', params: { caseNumber } });
};
</script>

<template>
  <div class="text-cerulean-950 outline-1 w-full px-6 py-5 rounded-lg flex flex-col gap-3" test-id="posted-order-item">
    <div class="flex">
      <div class="grow flex gap-2 items-center">
        <FontAwesomeIcon :icon="faClock" />
        <span class="text-lg font-bold">
          {{ dayjs(order.scheduledDateTime || order.expirationDateTime).format('DD.MM.YYYY, H [Uhr]') }}
        </span>
      </div>
    </div>
    <span class="text-base break-words whitespace-normal overflow-hidden"> „{{ order.description }}“ </span>
    <div class="flex items-center justify-between gap-4 flex-wrap">
      <DefaultButton
        class="bg-gradient-bamboo w-full sm:w-auto text-white"
        @click="navigateToPostedOrderDetails(order.caseNumber)"
      >
        <span class="text-base">Details ansehen</span>
      </DefaultButton>

      <OpenInMapsWrapper :addressObject="order" class="text-cerulean-600">
        {{ order.addressLine1 }}, {{ order.postalCode }} {{ order.city }}
      </OpenInMapsWrapper>
    </div>
  </div>
</template>
