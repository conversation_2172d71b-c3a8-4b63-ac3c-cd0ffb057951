<script lang="ts" setup>
import { defineExpose, onBeforeUnmount, onMounted, ref } from 'vue';
import SignaturePad from 'signature_pad';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';

const canvas = ref<HTMLCanvasElement | null>(null);
let signaturePad: SignaturePad | null = null;
const hasSignature = ref(false);

onMounted(() => {
  if (!canvas.value) return;
  signaturePad = new SignaturePad(canvas.value, { minWidth: 0.9, maxWidth: 2, backgroundColor: 'white' });

  resizeCanvas();
  window.addEventListener('resize', resizeCanvas);

  signaturePad.addEventListener('endStroke', () => {
    hasSignature.value = !signaturePad!.isEmpty();
  });
});

function clear(): void {
  signaturePad?.clear();
  hasSignature.value = false;
}

function getDataURL(): string | null {
  return signaturePad?.isEmpty() ? null : signaturePad!.toDataURL('image/jpeg', 0.7);
}

// Keeps strokes sharp on retina & on resize
function resizeCanvas(): void {
  if (!canvas.value) return;

  const ratio = Math.max(window.devicePixelRatio || 1, 1);
  const ctx = canvas.value.getContext('2d');
  const cachedData = signaturePad?.toData(); // preserve content

  canvas.value.width = canvas.value.offsetWidth * ratio;
  canvas.value.height = canvas.value.offsetHeight * ratio;
  ctx?.scale(ratio, ratio);

  if (signaturePad) {
    signaturePad.clear();
    if (cachedData?.length) signaturePad.fromData(cachedData);
  }
}

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeCanvas);
});

defineExpose({ clear, hasSignature, getDataURL });
</script>

<template>
  <div class="relative w-full">
    <canvas ref="canvas" class="w-full h-32 sm:h-40 rounded-md border-1 border-gray-700 bg-white" />
    <FontAwesomeIcon
      v-if="hasSignature"
      :icon="faTrash"
      class="absolute top-2 right-2 p-2 cursor-pointer text-gray-500"
      @click.prevent="clear"
    />
  </div>
</template>
