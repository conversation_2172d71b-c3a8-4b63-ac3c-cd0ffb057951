import { Log, User, UserManager, WebStorageStateStore } from 'oidc-client-ts';
import { ref } from 'vue';
import router from '@/router/routes.ts';
import { Capacitor } from '@capacitor/core';
import { App as CapacitorApp } from '@capacitor/app';

Log.setLevel(Log.NONE);
Log.setLogger(console);

export const userState = ref<User | null>(null);

const isNative = Capacitor.getPlatform() !== 'web';

const redirectUri = isNative
  ? 'com.alleskoenner24.handyman://login-redirect'
  : window.location.origin + '/login-redirect';

const logoutRedirectUri = isNative ? 'com.alleskoenner24.handyman://logout' : window.location.origin;

const userStore = new WebStorageStateStore({ store: window.localStorage });

function createUserManager() {
  return new UserManager({
    userStore,
    authority: 'https://oidc.klosebrothers.de/realms/alleskoenner24-handyman',
    client_id: import.meta.env.VITE_ALLESKOENNER_CLIENT_ID,
    redirect_uri: redirectUri,
    response_type: 'code',
    scope: 'openid profile email',
    post_logout_redirect_uri: logoutRedirectUri,
    silent_redirect_uri: redirectUri,
    accessTokenExpiringNotificationTimeInSeconds: 10,
    automaticSilentRenew: true,
    filterProtocolClaims: true,
    loadUserInfo: true,
  });
}

export const userManager = createUserManager();

userManager.events.addUserLoaded((user) => {
  console.log('User loaded:', user);
});

userManager.events.addUserUnloaded(() => {
  console.log('User unloaded!');
  userState.value = null;
});

userManager.events.addUserSignedIn(() => {
  console.log('User signed in!');
});

userManager.events.addUserSignedOut(() => {
  console.log('User signed out!');
});

userManager.events.addSilentRenewError((e) => {
  console.log('addSilentRenewError', e);
});

userManager.events.addUserSessionChanged(() => {
  console.log('addUserSessionChanged');
});

userManager.events.addAccessTokenExpiring(async () => {
  console.log('Access token will expire');
  userState.value = await userManager.getUser();
});

userManager.events.addAccessTokenExpired(async () => {
  console.log('Access token expired!');
  await renewToken();
});

async function renewToken() {
  try {
    const user = await userManager.signinSilent();
    console.log('Token refreshed');
    userState.value = user;
  } catch (error) {
    console.error('Silent token renewal failed', error);
    await login();
  }
}

export function authTokenHeader(): { Authorization: string } {
  if (userState.value) {
    return {
      Authorization: `Bearer ${userState.value.access_token}`,
    };
  }
  throw new Error('User not logged in');
}

export async function login(redirectPath: string = window.location.pathname): Promise<void> {
  console.log('Logging in... with redirect_uri:', redirectPath);
  await userManager.signinRedirect({
    url_state: redirectPath,
  });
}

export async function handleAppLoginRedirectCallback(redirectUrl: string): Promise<void> {
  await handleLoginRedirectCallback(redirectUrl);
}

export async function handleWebLoginRedirectCallback(): Promise<void> {
  await handleLoginRedirectCallback(window.location.href);
}

export async function handleLoginRedirectCallback(redirectUrl: string): Promise<void> {
  console.log('Handling login redirect callback to url: ', redirectUrl);

  try {
    const user = await userManager.signinRedirectCallback(redirectUrl);

    userState.value = user;
    console.log('User signed in via redirect');
    await router.replace(user.url_state ?? '/');
  } catch (e) {
    console.error('❌ Error processing OIDC response', e);
  }
}

export async function logout(): Promise<void> {
  await userManager.signoutRedirect();
}

CapacitorApp.addListener('appUrlOpen', async ({ url }) => {
  console.log('appUrlOpen', url);
  if (url && url.startsWith('com.alleskoenner24.handyman://login-redirect')) {
    console.log('🔁 Received redirect from login:', url);

    await handleAppLoginRedirectCallback(url);
  }
  if (url && url.startsWith('com.alleskoenner24.handyman://logout')) {
    console.log('🔁 Received redirect from logout:', url);

    userState.value = null;
    await router.replace('/login');
  }
});

userManager.getUser().then((user) => {
  console.log('Getting User.');
  userState.value = user;
});
