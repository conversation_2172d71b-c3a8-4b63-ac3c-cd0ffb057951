import { roundToTwoDecimals } from '@/utils/invoice.ts';
import type { SalesOrderLineOption } from '@/types/SalesOrderLineOption.type.ts';
import type { SalesOrderLineUpdateRequest } from '@/types/SalesOrderLineUpdateRequest.type.ts';
import { authTokenHeader } from '@/auth/auth.ts';
import { useSalesOrderLineOptionsStore } from '@/stores/salesOrderLineOptionsStore.ts';

const baseUrl = import.meta.env.VITE_ALLESKOENNER_BACKEND_URL;

export function useInvoice() {
  const salesOrderLineOptionsStore = useSalesOrderLineOptionsStore();

  async function getUnitPrices(line: SalesOrderLineUpdateRequest): Promise<number> {
    if (!salesOrderLineOptionsStore.options) {
      await getSalesOrderLineOptions();
    }
    const option = salesOrderLineOptionsStore.options?.find((option) => option.description === line.description);
    const newUnitPrice = option?.unitPrice ?? 0;
    return roundToTwoDecimals(newUnitPrice);
  }

  async function fetchSalesOrderLineOptions(): Promise<void> {
    try {
      const response = await fetch(`${baseUrl}/handyman-orders-sales-lines-options`, {
        headers: {
          ...authTokenHeader(),
        },
      });
      if (!response.ok) {
        throw new Error(`Error loading the sales order lines options: ${response.statusText}`);
      }
      salesOrderLineOptionsStore.options = await response.json();
    } catch (error) {
      console.error('Error loading the sales order lines options:', error);
      throw error;
    }
  }

  async function getSalesOrderLineOptions(): Promise<SalesOrderLineOption[]> {
    if (!salesOrderLineOptionsStore.options || salesOrderLineOptionsStore.options.length === 0) {
      await fetchSalesOrderLineOptions();
    }
    return salesOrderLineOptionsStore.options ?? [];
  }

  async function postSalesOrder(caseNumber: string, systemId: string, signature: string): Promise<Response> {
    try {
      const body: Record<string, unknown> = {
        salesOrderSystemId: systemId,
        customerSignature: signature,
      };

      return await fetch(`${baseUrl}/handyman-orders/${caseNumber}/post`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authTokenHeader(),
        },
        body: JSON.stringify(body),
      });
    } catch (error) {
      console.error('Error confirming invoice:', error);
      throw error;
    }
  }

  return {
    getUnitPrices,
    getSalesOrderLineOptions,
    postSalesOrder,
  };
}
