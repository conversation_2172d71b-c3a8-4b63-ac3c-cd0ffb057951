import type { Order } from '@/types/Order.type.ts';
import dayjs from 'dayjs';
import { showErrorToast } from '@/Toaster.ts';
import { authTokenHeader } from '@/auth/auth.ts';
import { usePostedOrdersStore } from '@/stores/postedOrdersStore.ts';

export function usePostedOrderService() {
  async function getAllPostedOrders(): Promise<{ postedOrders: Order[] }> {
    const ordersStore = usePostedOrdersStore();
    const allOrders: Order[] = (await fetchAllPostedOrders()).map((order) => ({
      ...order,
      expirationDateTime: dayjs(order.expirationDateTime),
      scheduledDateTime: order.scheduledDateTime ? dayjs(order.scheduledDateTime) : undefined,
    }));

    ordersStore.postedOrders = allOrders
      .map((order) => ({
        order,
        dateTimeToSortBy: order.scheduledDateTime ? order.scheduledDateTime : order.expirationDateTime,
      }))
      .sort((a, b) => a.dateTimeToSortBy.diff(b.dateTimeToSortBy))
      .map(({ order }) => order);

    return ordersStore;
  }

  async function fetchAllPostedOrders(): Promise<Order[]> {
    try {
      const baseUrl = import.meta.env.VITE_ALLESKOENNER_BACKEND_URL;

      const allOrder = await fetch(baseUrl + '/handyman-orders/posted', {
        method: 'GET',
        headers: {
          ...authTokenHeader(),
          Accept: 'application/json',
        },
      });

      if (!allOrder.ok) {
        throw new Error(`Error loading the posted orders: ${allOrder.statusText}`);
      }

      return await allOrder.json();
    } catch (error) {
      console.error('Error fetching orders:', error);
      showErrorToast('Fehler beim Laden der Aufträge. Bitte versuche es später erneut.');
    }
    return [];
  }

  async function getSinglePostedOrder(caseNumber: string): Promise<Order | undefined> {
    const postedOrdersStore = usePostedOrdersStore();
    const order = findOrderInStore();

    function findOrderInStore(): Order | undefined {
      return postedOrdersStore.postedOrders.find((order) => order.caseNumber === caseNumber);
    }

    if (order) {
      return order;
    }
    await getAllPostedOrders();

    return findOrderInStore();
  }

  function setSinglePostedOrder(order: Order): void {
    function removeOrderFromStore(order: Order): void {
      postedOrdersStore.$patch({
        postedOrders: postedOrdersStore.postedOrders.filter((o) => o.caseNumber != order.caseNumber),
      } as { postedOrders: Order[] });
    }

    const postedOrdersStore = usePostedOrdersStore();
    removeOrderFromStore(order);
    postedOrdersStore.postedOrders.push(order);
  }

  return {
    setSinglePostedOrder,
    getSinglePostedOrder,
    getAllPostedOrders,
  };
}
