import { type ActionPerformed, type DeliveredNotifications, PushNotifications } from '@capacitor/push-notifications';
import router from '@/router/routes.ts';
import { useDeviceStore } from '@/stores/deviceStore.ts';
import { useNotificationSignalStore } from '@/stores/notificationSignalStore.ts';

export const setupNotifications = () => {
  addNotificationListeners()
    .then(() => {
      registerNotifications()
        .then(() => {
          console.log('App notifications were successfully registered.');
          // Get delivered notifications for emptying the notification queue
          getDeliveredNotifications()
            .then(() => {
              console.log('Delivered notifications were successfully retrieved.');
            })
            .catch((reason) => {
              console.error(reason);
            });
        })
        .catch((reason) => {
          console.error(reason);
        });
    })
    .catch((reason) => {
      console.error(reason);
    });
};

export const addNotificationListeners = async () => {
  await PushNotifications.addListener('registration', (token) => {
    console.info('Registration token: ', token.value);
    useDeviceStore().deviceToken = token.value;
  });

  await PushNotifications.addListener('registrationError', (err) => {
    console.error('Registration error: ', err.error);
  });

  await PushNotifications.addListener('pushNotificationReceived', (notification) => {
    console.log('Push notification received: ', notification);

    const store = useNotificationSignalStore();
    store.triggerRefresh();
  });

  await PushNotifications.addListener('pushNotificationActionPerformed', async (notification: ActionPerformed) => {
    console.log('Push notification action performed!', notification);
    if (notification.notification?.data?.route) {
      const targetRoute = notification.notification.data.route;
      await router.push({ name: targetRoute });
    }
  });
};

export const registerNotifications = async () => {
  let permStatus = await PushNotifications.checkPermissions();

  if (permStatus.receive === 'prompt') {
    permStatus = await PushNotifications.requestPermissions();
  }

  if (permStatus.receive !== 'granted') {
    throw new Error('User denied permissions!');
  }

  await PushNotifications.register();
};

export const getDeliveredNotifications: () => Promise<DeliveredNotifications> = async () => {
  return PushNotifications.getDeliveredNotifications();
};
