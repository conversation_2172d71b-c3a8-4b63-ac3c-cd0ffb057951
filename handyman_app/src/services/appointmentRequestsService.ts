import type { AppointmentRequest } from '@/types/AppointmentRequest.type.ts';
import dayjs from 'dayjs';
import type { AppointmentRequestItemProps } from '@/components/AppointmentRequestItem.vue';
import { authTokenHeader } from '@/auth/auth.ts';

export function useAppointmentRequestsService() {
  function toItemProps() {
    const now = dayjs();
    return (request: AppointmentRequest): AppointmentRequestItemProps => ({
      ...request,
      remainingSecondsToReply: dayjs(request.expirationDateTime)?.diff(now, 'seconds'),
    });
  }

  function byMostUrgentAsapFirstThenByScheduledDate() {
    return (a: AppointmentRequestItemProps, b: AppointmentRequestItemProps) => {
      if (a.isAsap && b.isAsap) {
        return a.remainingSecondsToReply - b.remainingSecondsToReply;
      } else if (a.isAsap) {
        return -1;
      } else if (b.isAsap) {
        return 1;
      } else {
        return dayjs(a.scheduledDateTime).diff(dayjs(b.scheduledDateTime));
      }
    };
  }

  function sortByUrgency(appointmentRequests: AppointmentRequest[]) {
    return appointmentRequests.map(toItemProps()).sort(byMostUrgentAsapFirstThenByScheduledDate());
  }

  async function getPendingAppointmentRequests(): Promise<AppointmentRequestItemProps[]> {
    const pendingAppointmentRequests = await fetchPendingAppointmentRequests();
    return sortByUrgency(pendingAppointmentRequests);
  }

  async function fetchPendingAppointmentRequests() {
    try {
      const baseUrl = import.meta.env.VITE_ALLESKOENNER_BACKEND_URL;
      const pendingAppointmentRequests = '/appointment-request/pending';

      const response = await fetch(baseUrl + pendingAppointmentRequests, {
        method: 'GET',
        headers: {
          ...authTokenHeader(),
        },
      });
      if (!response.ok) {
        throw 'Error fetching appointment requests: ' + response.statusText;
      }
      return await response.json();
    } catch (error) {
      console.error('Error getting list of appointment requests:', error);
      throw error;
    }
  }

  return {
    getPendingAppointmentRequests,
  };
}
