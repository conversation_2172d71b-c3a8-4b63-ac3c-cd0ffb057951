import { authTokenHeader } from '@/auth/auth.ts';
import { Directory, Filesystem } from '@capacitor/filesystem';
import { FileOpener } from '@capawesome-team/capacitor-file-opener';
import { Capacitor } from '@capacitor/core';

export async function downloadInvoicePdf(systemId: string): Promise<Blob> {
  const baseUrl = import.meta.env.VITE_ALLESKOENNER_BACKEND_URL;
  const response = await fetch(baseUrl + `/handyman-orders/${systemId}/pdf`, {
    method: 'GET',
    headers: {
      ...authTokenHeader(),
      Accept: 'application/pdf',
    },
  });

  if (!response.ok) {
    throw new Error(`<PERSON><PERSON> beim <PERSON>laden der PDF: HTTP ${response.status}`);
  }

  return await response.blob();
}

export async function downloadPDF(systemId: string, fileName: string, document: Document) {
  const blob = await downloadInvoicePdf(systemId);

  if (Capacitor.getPlatform() === 'web') {
    const url = URL.createObjectURL(blob);
    const anchor = document.createElement('a');
    anchor.href = url;
    anchor.download = fileName;
    document.body.appendChild(anchor);
    anchor.click();
    document.body.removeChild(anchor);
    URL.revokeObjectURL(url);
  } else {
    await writeBlobToFilesystem(fileName, blob);
  }
}

export async function downloadAndOpenPDF(systemId: string, fileName: string) {
  const blob = await downloadInvoicePdf(systemId);

  if (Capacitor.getPlatform() === 'web') {
    window.open(URL.createObjectURL(blob), '_blank');
  } else {
    const result = await writeBlobToFilesystem(fileName, blob);

    await FileOpener.openFile({
      path: result.uri,
      mimeType: 'application/pdf',
    });
  }
}

async function writeBlobToFilesystem(fileName: string, blob: Blob) {
  const result = await Filesystem.writeFile({
    path: fileName,
    data: await convertBlobToBase64(blob),
    directory: Directory.Documents,
  });
  console.log('Saved pdf at path:', result.uri);
  return result;
}

function convertBlobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onerror = reject;
    reader.onload = () => {
      const base64 = (reader.result as string).split(',')[1];
      resolve(base64);
    };
    reader.readAsDataURL(blob);
  });
}
