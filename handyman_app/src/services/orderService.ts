import type { Order } from '@/types/Order.type.ts';
import dayjs from 'dayjs';
import { showErrorToast } from '@/Toaster.ts';
import { authTokenHeader } from '@/auth/auth.ts';
import { useOrdersStore } from '@/stores/ordersStore.ts';

export function useOrderService() {
  async function getAllOrders(): Promise<{ asapOrders: Order[]; scheduledOrders: Order[] }> {
    const ordersStore = useOrdersStore();
    const allOrders: Order[] = (await fetchAllOrders()).map((order) => ({
      ...order,
      expirationDateTime: dayjs(order.expirationDateTime),
      scheduledDateTime: order.scheduledDateTime ? dayjs(order.scheduledDateTime) : undefined,
    }));
    ordersStore.$patch({ asapOrders: getAsapOrders(allOrders), scheduledOrders: getScheduledOrders(allOrders) });
    return ordersStore;
  }

  async function fetchAllOrders(): Promise<Order[]> {
    try {
      const baseUrl = import.meta.env.VITE_ALLESKOENNER_BACKEND_URL;

      const allOrder = await fetch(baseUrl + '/handyman-orders', {
        method: 'GET',
        headers: {
          ...authTokenHeader(),
          Accept: 'application/json',
        },
      });

      return await allOrder.json();
    } catch (error) {
      console.error('Error fetching orders:', error);
      showErrorToast('Fehler beim Laden der Aufträge. Bitte versuche es später erneut.');
    }
    return [];
  }

  const getAsapOrders = (orders: Order[]) => orders.filter((order) => order.isAsap).sort(byMostUrgentAsapOrder);

  const byMostUrgentAsapOrder = (a: Order, b: Order) => dayjs(a.expirationDateTime).diff(dayjs(b.expirationDateTime));

  const getScheduledOrders = (orders: Order[]) =>
    orders.filter((order) => !order.isAsap).sort(byMostUrgentScheduledOrder);

  const byMostUrgentScheduledOrder = (a: Order, b: Order) =>
    dayjs(a.scheduledDateTime).diff(dayjs(b.scheduledDateTime));

  async function getSingleOrder(caseNumber: string): Promise<Order | undefined> {
    function findOrderInStore(): Order | undefined {
      const allOrders = [...ordersStore.asapOrders, ...ordersStore.scheduledOrders];
      return allOrders.find((order) => order.caseNumber === caseNumber);
    }

    const ordersStore = useOrdersStore();
    const order = findOrderInStore();
    if (order) {
      return order;
    }
    await getAllOrders();
    return findOrderInStore();
  }

  return {
    getAllOrders,
    getSingleOrder,
  };
}
