import { authTokenHeader } from '@/auth/auth.ts';
import type { SalesOrderLineUpdateRequest } from '@/types/SalesOrderLineUpdateRequest.type.ts';

export async function updateSalesOrderLines(salesOrderId: string, salesOrderLines: SalesOrderLineUpdateRequest[]) {
  const baseUrl = import.meta.env.VITE_ALLESKOENNER_BACKEND_URL;
  const endpoint = '/handyman-orders-sales-lines';

  const requestData = {
    salesOrderId,
    salesOrderLines,
  };

  const response = await fetch(baseUrl + endpoint, {
    method: 'PUT',
    headers: {
      ...authTokenHeader(),
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestData),
  });

  if (!response.ok) {
    throw new Error(`Error saving sales order lines: HTTP error! Status: ${response.status}`);
  }
}
