import { authTokenHeader } from '@/auth/auth.ts';
import dayjs, { Dayjs } from 'dayjs';

const baseUrl = import.meta.env.VITE_ALLESKOENNER_BACKEND_URL;

export function useAppointmentConfirmationService() {
  async function confirmAppointmentRequest(appointmentRequestId: string): Promise<Response> {
    const url = `${baseUrl}/appointment-request/${appointmentRequestId}/confirm`;
    try {
      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          ...authTokenHeader(),
          'Content-Type': 'application/json',
        },
      });
      sessionStorage.setItem('mostRecentConfirmationDateTime', dayjs().toString());
      return response;
    } catch (error) {
      console.error('Error confirming appointment request:', error);
      throw error;
    }
  }

  async function rejectAppointmentRequest(appointmentRequestId: string, reason: string): Promise<Response> {
    const url = `${baseUrl}/appointment-request/${appointmentRequestId}/reject`;
    try {
      return await fetch(url, {
        method: 'PUT',
        headers: {
          ...authTokenHeader(),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: reason }),
      });
    } catch (error) {
      console.error('Error rejecting appointment request:', error);
      throw error;
    }
  }

  function getMostRecentConfirmationDateTime(): Dayjs | null {
    const dateTimeString = sessionStorage.getItem('mostRecentConfirmationDateTime');
    return dateTimeString ? dayjs(dateTimeString) : null;
  }

  return {
    confirmAppointmentRequest,
    rejectAppointmentRequest,
    getMostRecentConfirmationDateTime,
  };
}
