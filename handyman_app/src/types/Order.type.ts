import type { Dayjs } from 'dayjs';

export type OrderLine = {
  description: string;
  quantity: number;
  unitPrice: number;
  unitOfMeasure: string;
  systemId: string;
  type: string;
  lineObjectNumber: string;
};

export type Order = {
  addressLine1: string;
  amountExcludingVAT: number;
  amountIncludingVAT: number;
  caseNumber: string;
  city: string;
  description: string;
  displayName: string;
  email: string;
  expirationDateTime: Dayjs;
  isAsap: boolean;
  orderLines: OrderLine[];
  phoneNumber: string;
  postalCode: string;
  scheduledDateTime?: Dayjs;
  systemId: string;
  billToName?: string;
  billToAddress?: string;
  billToCity?: string;
  billToPostCode?: string;
};
