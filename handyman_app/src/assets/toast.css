/*
This overwrites the standard vue3-toastify css properties.
See: https://vue3-toastify.js-bridge.com/usage/how-to-style.html
*/
:root {
    --toastify-color-success: var(--color-success-500);
    --toastify-color-error: var(--color-persian-red-600);
    --toastify-color-warning: var(--color-tree-poppy-500);
    --toastify-color-info: var(--color-matisse-500);
    --toastify-toast-width: 600px;
    --toastify-toast-min-height: 64px;
    --toastify-toast-max-height: 800px;
    --toastify-font-family: sans-serif;
    --toastify-z-index: 9999;
}
