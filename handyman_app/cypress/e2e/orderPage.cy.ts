import { loginWithUsernameAndPassword } from '../support/login.ts';

describe('Order overview and details', () => {
  beforeEach(() => {
    loginWithUsernameAndPassword();
    cy.intercept('GET', 'http://localhost:8080/handyman-orders', {
      fixture: 'handyman-orders.json',
    });
  });

  it('should render OrderList and navigate to and back from OrderDetails pages', () => {
    cy.visit('/orders');

    cy.get('h2').contains('Meine Aufträge');
    cy.get('div').contains('Aufträge werden geladen...').should('not.exist');

    cy.get('[test-id="order-container"]').should('exist');
    cy.get('[test-id="asap-order-item"]').should('have.length.at.least', 1);

    cy.get('[test-id="asap-order-item"]').first().as('asap-item');
    cy.get('@asap-item').contains('So schnell wie möglich!');
    cy.get('@asap-item').contains('Wasserleitung geplatzt, Hilfe dringend nötig!');
    cy.get('@asap-item').find('button').contains('Navigation starten');
    cy.get('@asap-item').find('button').contains('Details ansehen').click();

    cy.url().should('include', '/orders/101001');
    cy.contains('Zurück zur Übersicht').click();

    cy.url().should('include', '/orders');
    cy.url().should('not.include', '/101001');

    cy.get('[test-id="scheduled-order-item"]').first().as('scheduled-item');
    cy.get('@scheduled-item').contains('05.04.2025, 9 Uhr');
    cy.get('@scheduled-item').contains('Wir ziehen in eine neue Wohnung und benötigen Unterstützung');
    cy.get('@scheduled-item').find('button').contains('Navigation starten');
    cy.get('@scheduled-item').find('button').contains('Details ansehen').click();

    cy.url().should('include', '/orders/101007');
    cy.contains('Zurück zur Übersicht').click();

    cy.url().should('include', '/orders');
    cy.url().should('not.include', '/101007');
  });

  it('should show ASAP OrderDetails correctly', () => {
    cy.visit('/orders/101001');
    cy.get('div').contains('Auftrag wird geladen...').should('not.exist');

    cy.contains('So schnell wie möglich!');
    cy.contains('Vorgangsnummer: 101001');
    cy.contains('Wasserleitung geplatzt, Hilfe dringend nötig!');

    cy.contains('Mario Müstermän');
    cy.contains('Industriestraße 45');
    cy.contains('12099 Berlin');
    cy.contains('Telefon: 030 3 12 34 00');
    cy.contains('E-Mail: <EMAIL>');

    cy.contains('Wario Wüstermän');
    cy.contains('Wüstenstraße 5');
    cy.contains('12089 Berlin');

    cy.get('[test-id="document-button-Rechnung_101001"]').first().as('invoice-button');
    cy.get('@invoice-button').contains('Rechnung_101001');
  });

  it('should show scheduled OrderDetails correctly', () => {
    cy.visit('/orders/101007');
    cy.get('div').contains('Auftrag wird geladen...').should('not.exist');

    cy.contains('05.04.2025, 9 Uhr');
    cy.contains('Vorgangsnummer: 101007');
    cy.contains('Wir ziehen in eine neue Wohnung und benötigen Unterstützung');

    cy.contains('Norma Friedrichsen');
    cy.contains('Am Stadtpark 8');
    cy.contains('10555 Berlin');
    cy.contains('Telefon: 030 3 12 34 01');
    cy.contains('E-Mail: <EMAIL>');

    cy.get('[test-id="document-button-Rechnung_101007"]').first().as('invoice-button');
    cy.get('@invoice-button').contains('Rechnung_101007');
  });
});

describe('Order overview and details shows error message', () => {
  beforeEach(() => {
    loginWithUsernameAndPassword();
  });

  it('fetching error', () => {
    cy.visit('/orders');
    cy.get('[data-testid="toast-body"]').contains('Fehler beim Laden der Aufträge.');
    cy.contains('Derzeit keine Aufträge vorhanden');
  });

  it('unknown id', () => {
    cy.visit('/orders/asdf');
    cy.get('[data-testid="toast-body"]').contains('Fehler beim Laden der Aufträge.');
    cy.contains('Auftrag konnte nicht geladen werden');
  });
});
