import { loginWithUsernameAndPassword } from '../support/login.ts';

describe('Pending Requests Page', () => {
  beforeEach(() => {
    cy.intercept('GET', 'http://localhost:8080/appointment-request/pending', {
      fixture: 'pending-appointment-requests.json',
    });

    loginWithUsernameAndPassword();

    cy.visit('/appointment-requests');
  });

  it('should render InformationCard and AppointmentRequestList correctly', () => {
    cy.get('[test-id="card"]').contains(
      'Wählen Sie eine Anfrage aus, um Details zu erhalten und sich den Auftrag zu sichern!',
    );

    cy.get('[test-id="appointment-request-container"]').children().should('have.length', 3);
    cy.get('[test-id="appointment-request-item"]')
      .eq(0)
      .should('include.text', 'So schnell wie möglich')
      .should('include.text', 'Verstopfter Abfluss / Abwasser');
    cy.get('[test-id="appointment-request-item"]')
      .eq(1)
      .should('include.text', '02.11.2024, 14 Uhr')
      .should('include.text', 'Wir ziehen in eine neue Wohnung');
  });
});
