function loginWithUsernameAndPassword() {
  cy.visit('/');
  cy.get('button').contains('Mit Microsoft anmelden').click();
  cy.origin('https://oidc.klosebrothers.de', () => {
    cy.get('input[id="username"]').type('cypress-test-user1');
    cy.get('input[id="password"]').type('2Ap8dxbFCX4vHEj');
    cy.get('[id="kc-login"]').click();
  });

  cy.url().should('eq', window.origin + '/orders');
}

export { loginWithUsernameAndPassword };
