<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="handyman app install and run dev" type="js.build_tools.npm">
    <package-json value="$PROJECT_DIR$/handyman_app/package.json" />
    <command value="run" />
    <scripts>
      <script value="dev" />
    </scripts>
    <node-interpreter value="project" />
    <envs />
    <method v="2">
      <option name="NpmBeforeRunTask" enabled="true">
        <package-json value="$PROJECT_DIR$/handyman_app/package.json" />
        <command value="install" />
        <node-interpreter value="project" />
        <envs />
      </option>
    </method>
  </configuration>
</component>