#!/bin/bash

# Definiere die Pfade, die überwacht werden sollen
WATCH_PATHS=("customer_website" "customer_app" "local-development")

# <PERSON><PERSON>prü<PERSON>, ob Dateien in den überwachten Pfaden geändert wurden
CHANGED_FILES=$(git diff --cached --name-only --diff-filter=ACM)

for path in "${WATCH_PATHS[@]}"; do
  if echo "$CHANGED_FILES" | grep -q "^$path/"; then
    MATCH_FOUND=true
    break
  fi
done

# Wenn keine Dateien in den überwachten Pfaden geändert wurden, beenden
if [ -z "$MATCH_FOUND" ]; then
  exit 0
fi

# Hole die Commit-Nachricht aus der Temporärdatei
COMMIT_MSG_FILE=$(git rev-parse --git-path COMMIT_EDITMSG)
COMMIT_MSG=$(head -n1 "$COMMIT_MSG_FILE")

# <PERSON><PERSON><PERSON>r<PERSON><PERSON>, ob die Nachricht mit "dev: " beginnt
if ! echo "$COMMIT_MSG" | grep -q "^dev: "; then
  echo "Fehler: Commit-Nachrichten für Änderungen in den überwachten Ordnern müssen mit 'dev: ' beginnen."
  exit 1
fi

# Alles in Ordnung, Commit kann erfolgen
exit 0
