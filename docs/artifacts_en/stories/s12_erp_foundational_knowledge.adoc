===== Basic Setup of BC (TODO: SP=3)

====== Description
* investigate which entities we need in BC
* collect the data we want to store in the main instance and the handyman instances

====== Acceptance Criteria
* We need to gather information on the following:
** Authentication & Registration
** Meeting
** Calendar
** Setup a Lead
** Who has time to fulfill the request
** Set a Meetíng for the Handyman
** Setup a Customer
** Cancellation Period
** Invoice e.g. 1H. plus travel
** We need to be able to add Material cost as a single line in the invoice
*** Photo of Receipt from the Hardware Store
** Send Invoice to Customer
** We need to see this in the main ERP system in case of queries etc.
** Need to be able to say that it has been paid after someone has checked in the Bank Account
** Which format does BC deliver by default? *PDF* / Zugferd / X Rechnung (only data format)?

* Outcome
** Documentation & Knowledge Sharing