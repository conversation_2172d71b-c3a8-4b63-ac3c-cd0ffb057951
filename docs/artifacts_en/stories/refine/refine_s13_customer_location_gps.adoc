==== s13: Customer Sets Location - GPS (TODO, SP=5)

===== Description

* *As* a handyman customer,
* *I want* to set my location automatically using GPS
* *so that* the app can provide me with options
** *when* I describe my problem / submit an order request

===== Acceptance Criteria/Tests

* [ ] When I click on the text box, it suggests my location based on GPS when enabled


===== Implementation Hints

* Consider using a library to autocomplete existing addresses

===== Notes

* Design can be found here:
** https://miro.com/app/board/uXjVLDW9Zr0=/?moveToWidget=3458764607461738770&cot=14[Link to the miro board]
