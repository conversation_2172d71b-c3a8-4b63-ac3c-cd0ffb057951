===== Request Success and Failure via Website (DONE, SP=3)
[id=s6_request_response]

====== Description

* *As* a handyman customer,
* *I want* to receive a notification in the web app confirming that my request was successfully sent,
* *so that* I can be confident that my request has been acknowledged or received.

====== Acceptance Criteria

* [x] After pressing the "Submit" button...

* [x] Positive response from the backend:
** 200 OK
** Text input fields are no longer visible.
** A message is displayed: Dear [Customer Name]...
** Useful information is displayed, e.g., a mock order number (similar to an Amazon confirmation).
*** The order number will later be supplemented by Business Central.

* [x] Negative response from the backend:
** 4XX or 5XX
** Text input fields remain filled.
** A short info box prompts the customer to resend the request or call the hotline for support.
*** For example, 555 0001 0001

