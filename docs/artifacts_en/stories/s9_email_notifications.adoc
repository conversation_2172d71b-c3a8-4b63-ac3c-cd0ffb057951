==== s9: Successful Request - Email Notification (TODO, SP=5)

===== Description

* *As* a tradesperson customer,
* *I want* to receive an email confirmation that my request has been successfully sent,
* *so that* I can be sure that my request has been received and a tradesperson will be organized for me.

===== Acceptance Criteria

* [x] The previously configured email server is used.
* [x] Clarification on where the email is "triggered" from:
** Triggered from Nuxt
** Document design decision
* [x] Example trigger is activated
** e.g., when creating a new contact
** Test is written to "mock" the email server
* [x] Authentication via API key or auth token is implemented.
* [x] Example customer receives an email with example text
** Customer number is included in the email
** Email content is in minimal HTML & CSS

