==== s14: Customer describes their problem (TODO, SP=2)

===== Description

* *As* a handyman customer,
** *having* set my location on the landing page,
* *I want* to provide alleskönner with a short description of the problem I am facing,
* *and* do it in such a way that it makes sense / it is easy for alleskönner to understand,
* *so that* alleskönner can check if it offers this service near me.

===== Acceptance Criteria

* [ ] Simple text bar that allows a customer to enter the problem they are facing.
* [ ] We can provide a few text suggestions for problems they may be facing

===== Notes
* Assumes have already provided your location in the text box above
* Doesn't require a connection to the back end to fulfill the functionality
* Only after submission of the form at the end, the backend will transform this user input "magically" into craftsman-skill

> Maybe we can avoid having to map the user input "magically" to craftsman skill, because the craftsman can see what skill is needed and can reject if he can't satisfy that required skill. Maximizing what we don't have to do right now.

* Design can be found here:
** https://miro.com/app/board/uXjVLDW9Zr0=/?moveToWidget=3458764607461738770&cot=14[Link to the miro board]
