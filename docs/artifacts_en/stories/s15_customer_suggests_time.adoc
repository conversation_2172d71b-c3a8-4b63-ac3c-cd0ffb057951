==== s15: Customer suggests an appointment time (TODO, SP=3)

===== Description

* *As* a customer,
** *having* described my problem,
* *I want to* suggest an appointment time,
* *so that* the handyman knows when to be at my place

===== Acceptance Criteria

* [ ] when previous step was finished (location was selected) the screen forwards to appointment time
* [ ] Previously selected options are visible (Dienstleistung; Standort) - see Figma
* [ ] button to select ASAP - "So schnell wie möglich!"
* [ ] option to select any full hour in the next 14 days
** [ ] Show the time starting at 06:00 until 20:00
* [ ] default appointment times
** first day (today.
If after 20:00 -> tomorrow) is selected by default
** first selectable time is >= 3.5 hours from now
*** 12:30 -> 16:00 = 16:00
*** 12:31 -> 16:01 = 17:00
*** 12:59 -> 16:29 = 17:00
*** 13:00 -> 16:30 = 17:00
*** 13:15 -> 16:45 = 17:00
*** 13:29 -> 16:59 = 17:00
*** 13:30 -> 17:00 = 17:00
*** 13:31 -> 17:01 = 18:00
* [ ] Public holidays for Berlin / sundays should be shown as greyed out / disabled
** If there is a *quick* and reasonably time-boxed solution to get the holidays for the specified location.
Do it.
* [ ] Looks like in https://www.figma.com/design/ucCTm9biMvqmTVfn8QWNz8/Allesk%C3%B6nner24-Web-App?node-id=269-2453[https://xxx][Figma].
Here you can see it https://www.figma.com/proto/ucCTm9biMvqmTVfn8QWNz8/Allesk%C3%B6nner24-Web-App?node-id=269-2453&scaling=scale-down&content-scaling=fixed&page-id=0%3A1&starting-point-node-id=101%3A618[in action].
* [ ] As soon as hour was clicked: Navigation to registration page + overview of input
** registration page is not part of this story.

===== Notes

* __
* __

image::../../../images/4_AK24_termin.jpeg[]
