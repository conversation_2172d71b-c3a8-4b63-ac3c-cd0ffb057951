===== s3: Customer Request via Website (DONE, SP=5)
[id=s3_customer_request_website]

====== Description

* **As** a handyman customer,
* **I want** to request a handyman,
* **so that** I can solve a domestic issue at home, e.g., repair my washing machine.

====== Acceptance Criteria

* [x] The customer can fill out a request form to request a handyman.
* [x] The request form includes fields for name, address, phone number, and a description of the problem.

====== Notes

* Ensure that customer data is stored securely.
* The user interface of the request form should be user-friendly.

////
====== Acceptance Tests

* [x] Link to test whether the request form can be filled out correctly:
** link:../../../customer_website/cypress/e2e/requestForm.cy.ts[]
////

