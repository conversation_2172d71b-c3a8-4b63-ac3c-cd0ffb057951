===== Login: Email & Password (TODO, SP=5)
[id=s2_login_email]

====== Description

* **As** a back-office employee,
* **I want** to log in with my email address and password,\
* **so that** I can access my personal dashboard and the features of the ERP system.

====== Acceptance Criteria

* [ ] The administrative employee can log in with a valid email address and password.
* [ ] An error message is displayed for incorrect login credentials.
* [ ] After successful login, the administrative employee is redirected to the homepage.

====== Notes

* TODO

////
====== Acceptance Tests

* [ ] Link to test whether login is successful with valid email credentials.
* [ ] Link to test whether an error message is displayed when the administrative employee enters incorrect credentials.
* [ ] Link to test whether the administrative employee is redirected to the correct page after login.
////

