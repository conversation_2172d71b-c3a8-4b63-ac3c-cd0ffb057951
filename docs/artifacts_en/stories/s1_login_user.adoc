===== s1: Login - Social Media Username & Password (TODO, SP=5)
[id=s1_login_user]

====== Description

* **As** a back-office employee,
* **I want** to log in with my username and password,
* **so that** I can access my personal dashboard and the features of the ERP system.

====== Acceptance Criteria
* No need to create a personal profile
** Simply uses your Google / other social media account to authenticate.

* [ ] The administrative employee can log in with a valid username and password.
* [ ] An error message is displayed for incorrect login credentials.
* [ ] After successful login, the administrative employee is redirected to the homepage.

====== Notes

* TODO

////
====== Acceptance Tests

* [ ] Link to test whether login is successful with valid credentials.
* [ ] Link to test whether an error message is displayed when the administrative employee enters incorrect credentials.
* [ ] Link to test whether the administrative employee is redirected to the correct page after login.
////

