==== s10: Customer Sets Location (TODO, SP=5)

===== Description

* *As* a handyman customer,
* *I want* to set my location
* *so that* the app can provide me with options
** *when* I describe my problem / submit an order request

===== Acceptance Criteria/Tests

* [ ] Simple text bar that allows me to enter my location
* [ ] It should suggest a pre-formatted location as I begin to type in the search bar
* [ ] Should also allow the one only the city or Zip code or just the street as input
** People may not want to share their specific details initially
* [ ] Please check for strange characters or emojis and notify the customer if used


===== Implementation Hints
* Consider using a library to autocomplete existing addresses


====== Next story
* [ ] Initially regions we don't support should show a warning + ask if the user wants to get an email when the region is available
* [ ] Add a GPS Location Button to search for current location
** No Browser Pop-Up: should only ask for permission when you press the Location Button
* [ ] Save the location temporarily


===== Notes

* Design can be found here:
** https://miro.com/app/board/uXjVLDW9Zr0=/?moveToWidget=3458764607461738770&cot=14[Link to the miro board]
