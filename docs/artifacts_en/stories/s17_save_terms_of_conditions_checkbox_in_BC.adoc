==== s17: Save terms of conditions checkbox in BC (TODO, SP=2)

===== Important Note:
* Ask IT Vision to do it for us (and then let them explain to us the result)

===== Description

* *As* a call center worker,
* *I want to* see if the checked terms of conditions checkbox was saved in BC,
* *so that* I can confirm that the request was legally okay

===== Acceptance Criteria

* [ ] In the BC customer screen it is visible if the checkbox was checked after customer was created
* [ ] Set up extension in git repository and document setup

===== Dependencies

IT Vision workshop

===== Notes

* Ask IT Vision to do it for us (and then let them explain to us the result)
* The BC API customer request must be adjusted via an extension of the Customer Endpoint in BC
** We need to understand how to set the custom value via API request
* With the existing extension from fabian it is already visible in BC.
