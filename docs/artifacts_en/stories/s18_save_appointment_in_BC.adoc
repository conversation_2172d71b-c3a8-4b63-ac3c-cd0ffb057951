==== s18: Save appointment request in BC (TODO, SP=3)

===== Important Note:

* Ask IT Vision to do it for us (and then let them explain to us the result)

===== Description

* *As* a call center worker,
* *I want to* see in BC what the status of a customer appointment request is,
* *so that* I can better support the customer

===== Acceptance Criteria

* [ ] A table of appointment requests is visible on a BC page on the Main instance
* [ ] The table shows following information:
- customer
- sales quote
- sales quote lines (description only)
- date (date + time)
- the status of the appointment request (pending, confirmed, canceled)
* [ ] Via API an appointment request is created
- this can be done as a separate endpoint (for appointment request) or as a bundled endpoint (for customer + sales quote + appointment request)

===== Dependencies

IT Vision workshop

===== Notes

* Ask IT Vision to do it for us (and then let them explain to us the result)
* Discuss with IT Vision if it makes sense to create a custom endpoint for an order request (creating customer, sales quote and appointment request)?
Also ask for performance
* This story is supposed to teach us how to set up custom UI pages and tables in BC
* The required skill will be added later
