==== s16: <PERSON><PERSON> accepts appointment (TODO, SP=3)

===== Description

* *As* a handyman,
* *I want to* get respond to an appointment request,
* *so that* the customer can get an appointment confirmation

===== Acceptance Criteria

* [ ] Basic handyman nuxt app is set up and runs on an iPhone
* [ ] Basic navigation between home and "appointment" screen
* [ ] <PERSON><PERSON> gets notification and is redirected to handyman "appointment" screen
* [ ] Appointment screen shows Accept / Deny Dummy-Buttons without functionality
* [ ] Design decision why nuxt app was used was documented (also document PoC)
* [ ] Home page is empty with welcome text


===== Dependencies
* Depends on successful PoC (spk2)

===== Notes
* Appointment requests are saved in BC
* Later the appointment screen can fetch/poll pending appointment requests
