== Produkt Sprints

=== 2024 Q4

[cols="^8%, ^12%, 40%, 40%"]
|===
|Sprint|KW|Ziel|Features

|1.
|42-43 a|
* Kunden-App - Erstelle eine einfache Webseite, auf der Kunden / Call-Center-Agenten in Vertretung, einen Handwerker anfordern können a|
* <<s3_kundenanfrage_webseite, Kundenanfrage über die Webseite>>
* <<s4_kundenanfrage_telefon, Neuer Kunde fordert per Telefon einen Handwerker>>
* <<t1_ci_cd_pipeline, CI/CD Pipeline einrichten>>

|2.
|44-45 a|
* Kunden-App - Erstelle eine einfache Webseite, auf der Kunden / Call-Center-Agenten in Vertretung, einen Handwerker anfordern können a|
* <<s5_form_validierung, Formularvalidierung>>
* <<s6_anfrage_antwort, Anfrage erfolgreich und fehlgeschlagen – Web App>>

|*3.*
|46-47 a|
* Kunden-App - Lead Erstellen a|
* <<s7_lead_erp_erstellen, Lead in ERP erstellen>>
* Setup CI/CD (Continuous Integration / Continuous Delivery)
* Setup Mac for making iPhone App

|4.
|*48-49* a|
* Kunden-App
** Email Nachrichten
** Authentifizierung
** Termin erstellen a|
* Email nachrichten

* Social Authentifizierung

* Email / Passwort Auth.
** Registrierung
*** Profil
* Anmeldung
* Abmeldung

* Kunden-App: Design & Click Dummy

|5.
|50-52 a|
* *ZIEL: ERP (Business Central) Foundation*
* *ANMERKUNG: AKTUELLER SPRINT*
* Anmerkung: Aufgrund der Weihnachtsfeiertage habe ich den Weihnachtssprint verlängert a|
* * Vermittlung von Grundkenntnissen über Business Central durch IT Partner
* Gestaltete E-Mail-Benachrichtigungen
* Grundlegende MVP-App, die für die Handyman-App verwendet werden soll
* Design: Kunden App Click Dummy

|===
