== Produkt Roadmap

=== 2025 Q1 - "Integration package"

==== Milestones

* *Customer App*
** Request a Handyman
** Get appointment confirmation
** Running in Production
** Filtering out orders we can't deliver (e.g. work for electrician)

* *ERP / Backend*
** Main company and handyman companies are set up
** Invoicing of handyman
** Appointment coordination based on postal code and availability

* *Handyman App* <- No UI/UX-Design yet implemented
** Proof of Concept is completed
** Clarification of how handyman logs into app (e.g. existing BC Login)
** Receive and respond to appointment request
** Shows customer details and order request
** Customer mock signs off the job via simple button
** Manually finalize invoice and send / print it

* *Call-Center App*
** Clarification of Licensing for Login

* *Webseite*
** Kontaktformular
*** Melde eine Problem...

===== Finished use cases

Appointment Confirmation

1. customer makes order via website
2. BC or backend determines available and near handymen and sends appointment request
3. handyman accepts appointment request
4. customer gets appointment confirmation

Invoice

1. handyman finishes job
2. customer signs the job off (simulation)
3. handyman triggers invoice creation
4. handyman prints out invoice (and sends it)

// ==== Sprint 1 (KW 02 - 03)
//
// [cols="^8%, ^12%, 40%, 40%"]
// |===
// |Sprint|KW|Ziel|Features
//
// |1.
// |02-03 a|
// * Kunden-App Anfrage verfolgen a|
// * Status ID Sehen
// * Status in automatisierte kanban verfolgen
// * Call-Center-App Design & Click Dummy
// |===

// ==== Sprint 2 (KW 04 - 05)
// ==== Sprint 3 (KW 06 - 07)
// ==== Sprint 4 (KW 08 - 09)
// ==== Sprint 5 (KW 10 - 11)
// ==== Sprint 6 (KW 12 - 13)

<<<

=== 2025 Q2 - "Basic business package"

==== Milestones

* *Customer App*
** Register / Login / Logout
** Improve UI (also for Desktop-Version)

* *ERP / Backend*
** Credit Check / Bonitätsprüfung (COSMO CrefoDynamics) (with cache)
** Purchasing material
** Reconciliation of accounting books with bank account
** Rate limiting on backend API

* *Handyman App*
** Implementation Login usw. (if not already done in Q1)
*** Sync device token (for iphone) of handyman with BC on Login
** Purchase material and add to invoice
** Customer signs off the job via signature
** Works with iPad Pro (+ keyboard)
** Add worked time to invoice

* *Call-Center App*
** Login
** Create customer and see credit rating result
** Search for customers by phone number

===== Finished use cases

Customer Login

* Customer registers and logs into customer website

Handyman Login

* Handyman logs into app with BC account

Purchase of material

* Handyman purchases material
* Adds material positions into app
* Invoice is changed accordingly

Credit rating

* Customer order request comes or call center worker creates customer
* Credit rating is fetched from provider and saved in BC
* Based on credit rating order is accepted or denied
* Call center worker can view credit rating

Accounting

* Customer pays bill / transfers money to handyman bank account
* BC syncs with bank account
* BC reconciles transfers with accounting

Call center

* Call center worker logs in
* Creates customers
* Can see all customers
* Sees credit rating

////
==== Sprint 1 (KW 14 - 15)
==== Sprint 2 (KW 16 - 17)
==== Sprint 3 (KW 18 - 19)
==== Sprint 4 (KW 20 - 21)
==== Sprint 5 (KW 22 - 23)
==== Sprint 6 (KW 24 - 25)
==== Sprint 7 (KW 26 - 27)
////

<<<

=== 2025 Q3 - "Advanced business package"

==== Milestones

* *Customer App*
** Supporting Business Customers
** FAQ
** (not essential) Customer can give Feedback
** (not essential) Customer sees history of orders and invoices + UI/UX

* *ERP / Backend*
** Save unique & consecutive "Vorgangsnummer" to sales order
** Supporting Business Customers
** Save the handyman work report
** Petrol card / charging card purchases
** VAT notification in advance
** Payment of provision
** Reschedule appointments (e.g. when sick leave)
** E-Rechnung for B2B

* *Call-Center App*
** View customer history and invoices
** Search for customer by Vorgangsnummer
** Send appointment requests manually to handymen
** Cancel appointments
** Reschedule appointments
** Send sms and email to customer for key events

* *Handyman App*
** Cost estimate & customer approval
** Create work report for documenting the result (text only)
** Printing invoice and cost estimate
** Add additional handyman to appointment -> Invoicing to another handyman
** Handle different rates for sundays and night shift
** Create appointments, block time, cancel, reschedule, sick leave, holiday
** Supporting Business Customers
** Improved design for UI/UX

===== Finished use cases

Customer history

* Customer logs into website
* Sees his order and invoice history

Reschedule appointment - call center

* Call center worker logs into app
* Searches for customer and sees customer history
* Cancels appointment
* Creates new appointment

Cost estimate

* Handyman creates cost estimate at the first contact point / cost estimate is automatically generated?
* Customer signs estimate off
* Is saved in BC for legal purposes

Work report

* Handyman finishes work
* Documents the work
* Document is saved in BC

E-Rechnung

* BC sends invoice with E-Rechnung

Manage appointments - handyman

* Handyman goes to app
* Sees his appointments
* Can cancel appointment
* Can reschedule appointment
* Can block hours

Time tracking

* Handyman finishes work or pauses work
* Enters his worked hours into app
* --- Why?
--- Isn't it enough at the beginning to only save worked hours in invoice?


////
==== Sprint 1 (KW 28 - 29)
==== Sprint 2 (KW 30 - 31)
==== Sprint 3 (KW 32 - 33)
==== Sprint 4 (KW 34 - 35)
==== Sprint 5 (KW 36 - 37)
==== Sprint 6 (KW 38 - 39)
==== Sprint 7 (KW 40 - 41) <- 10. Oktober = Launch
////

<<<

=== 2025 Q4

==== Milestones

* *Customer App*
** Android & iOS App
** Payment via App (Paypal, https://en.wikipedia.org/wiki/EPC_QR_code[EPC-QR-Code])
** Upload images in customer requests & display them in handyman app notifications

* *Handyman App*
** Show customer address
** Payment via EC card
** Scan receipt (perhaps via OCR)
** Document work with images and videos
** Show uploaded images in request notification

* *Customer App & Call-Center App*
** Manually initiate debt collection

===== Finished use cases

Customer Image Upload

* Customer logs into app
* Makes an order and uploads photo


////
==== Sprint 1 (KW 42 - 43)
==== Sprint 2 (KW 44 - 45)
==== Sprint 3 (KW 46 - 47)
==== Sprint 4 (KW 48 - 49)
==== Sprint 5 (KW 50 - 51)
////

<<<

=== Future

* *Customer App & Call-Center App*
** Support bigger requests (B2B)
** Customer support
** ChatBot
** DACH region
** Select a specific handyman

* *Handyman App*
** Show Calendar
** Navigate to customer
** Tell customer delay

* *ERP / Backend*
** Create Franchisee
*** Order car, tools, training, contracts, accounts, etc.
*** Invoice by mail if no email address

* Company Würth
** purchase of small material

* Company "Ela" - Inspection camera for pipes provide photos / videos on an SD card.
** We need to be able to send those to the customer.
** We want to make it easy for the customer to get help and pass on the info.
