==== Stories: ToDo
:toc:

===== Personas Liste - Reference

Handwerker
Handwerkerkunde
Helpdesk-Mitarbeiter
Backoffice-Mitarbeiter
Entwickler

===== Kunden App Click Dummy

====== Beschreibung

====== Akzeptanz-kriterien/tests
* [ ] Mobile - Web App
* [ ] Desktop - Webseite



===== SPIKE: How do you do Credit Checks in BC?

====== Description
* How on earth do u do this???!!!!
* We need to do this with Creditreform
** Credit reform provides a soap api!?
* Hypothesis
** Rest API's?
** BC needs to trigger an async request
*** Should receive a quick response

====== Acceptance Criteria
* [ ] Automated test that shows we can send a request and receive a response


===== Helpdesk-Mitarbeiter führt Bonitätsprüfung durch

====== Beschreibung

* *Als* Helpdesk-Mitarbeiter,
* *möchte ich* ein neues Handwerker-Anfrageticket aus Microsoft Business Central abrufen und eine Bonitätsprüfung des Kunden durchführen,
* *damit* ich sicherstellen kann, dass der Kunde zahlungsfähig ist, bevor der Auftrag angenommen wird.

====== Acceptance Criteria

* [ ] Credit Check response saved in BC
* [ ] Linked to a customer contact in BC

===== Helpdesk-/Backoffice-Mitarbeiter Bonitätsprüfung Check Status

====== Beschreibung

* *Als* Helpdesk-/Backoffice-Mitarbeiter
* *möchte ich* den Status einer Bonitätsprüfung für einen neuen Kunden überprüfen
* *damit ich* sicherstellen kann, dass sie in der Lage sind, für die Handwerkerleistungen zu bezahlen, die wir ihnen anbieten

===== Helpdesk-Mitarbeiter akzeptiert oder lehnt Handwerkeranfrage ab und benachrichtigt den Kunden

====== Beschreibung

* *Als* Helpdesk-/Backoffice-Mitarbeiter
* *möchte ich* eine Handwerkeranfrage akzeptieren oder ablehnen und den Kunden benachrichtigen
* *damit ich* sicherstellen kann, dass die Kunden wissen, dass wir ihre Anfrage bearbeiten
** *oder* dass wir ihre Anfrage storniert haben (einschließlich des Grundes).

===== Bestehender Kunde fordert Handwerker vom Call Center an

====== Beschreibung

* *Als* Helpdesk-Mitarbeiter
* *möchte ich* eine Anfrage für einen Handwerker im Namen eines bestehenden Kunden protokollieren
* *damit ich* einem Kunden bei seinen Handwerkerbedürfnissen helfen kann

===== SMS-Benachrichtigung

====== Beschreibung

* *Als* Handwerkerkunde,
* *möchte ich* eine SMS-Benachrichtigung erhalten, wenn ein Handwerker unterwegs ist und die geplante Ankunftszeit,\
* *damit* ich besser vorbereitet bin und die Ankunft des Handwerkers einplanen kann.

* *Als* Helpdesk-Mitarbeiter,
* *möchte ich* in der Lage sein, mit dem Handwerkerkunden zu kommunizieren,\
* *damit* ich sicherstellen kann, dass der Kunde über die Ankunft des Handwerkers informiert ist und mögliche Fragen klären kann.

====== Akzeptanz-kriterien/tests

* [ ] Kunde erhält eine SMS-Benachrichtigung, sobald der Handwerker auf dem Weg ist.
* [ ] SMS enthält die geschätzte Ankunftszeit des Handwerkers.
* [ ] Benachrichtigung ist einfach und klar formuliert.
* [ ] SMS wird rechtzeitig versendet, damit der Kunde sich vorbereiten kann.
* [ ] Helpdesk-Mitarbeiter kann den Handwerkerkunden per Telefon oder E-Mail kontaktieren.
* [ ] Helpdesk-Mitarbeiter kann die Ankunftszeit des Handwerkers bestätigen und dem Kunden mitteilen.
* [ ] Es gibt ein Protokoll der Kommunikation, das festhält, dass der Kunde informiert wurde.
* [ ] TODO?? Relevant?? Kundenrückfragen können im ERP System dokumentiert und weiterverfolgt werden.

===== Backlog

* Spike: Investigate customer properties
** correct default information as part of their contact details
** e.g. Type "Person" (done)
** Add new customers automatically into categories using labels?
** Value: able to use more default features from BC

* Spike: Better understand what a lead is in BC
** What happens with leads? Easily convert to a contract?
** What is the general customer flow in BC?

* Spike: Log Events
** We should investigate if BC can do this well?
*** How does logging work in BC? What does it log?
** Or we should integrate an event logging system
** We also need to be able to check what exactly happened with a customer if something goes wrong

* Sign a contract with a Handyman, for him to become an "alleskönne"

* Add a Handyman to BC

* Der Handwerker erhält eine Benachrichtigung über die neue Anfrage.

* Handwerker App
** [ ] Email + PW mit BC User + 2FA
** sends photo to BC endpoint
** [ ] Soll Offline funktionieren (Was genau muss offline funktionieren)


* Handyman can agree or not agree to take on the job
** Der Kunde kann den Status seiner Anfrage online einsehen.

* Notification that a handyman has been arranged

** Link zu Test, ob eine Bestätigung nach dem Absenden der Anfrage angezeigt wird.
** Link zu Test, ob der Handwerker die Benachrichtigung über die neue Anfrage erhält.
** Link zu Test, ob der Kunde den Status seiner Anfrage online einsehen kann.

* Auth. & Berecht. Konzept
* erp:  login email
* erp:  login u/p
* SEO Text Optimierung (L&D)
* Datenanalyse -> Data-Events triggern, sammeln, analysieren
* Input-Validierung: Noch werden alle Eingaben direkt an BC weitergeleitet.
** D.h. jeder Müll wird weitergeleitet.
* Barrier-Freiheit
** Blinde Menschen sollte sehr einfach unsere UI Benutzen
* Should check email address hasn't been regularly used (avoid spammers)
* Helpdesk-Mitarbeiter needs to be notified there is a new Lead
* Kanban Dashboard das zeigt wie die Verträge durch Fließt
* Remove Duplicates from the customer sections
* Avoid creation of duplicates

* [ ] Pipeline einrichten für die Handwerker App (z.B. Github Actions)
* [ ] Analysieren/lernen, wie man Updates in der ERP testet
* [ ] Lernen, wie man pipeline updates der ERP aufspielt
* [ ] Deployment auf einem webserver einrichten
* [ ] Request Form: Umsatzsteuer Nummer eintragen lassen können
* [ ] Request Form: Differenzierung zwischen Rechnungs- und Lieferadresse - Klären
