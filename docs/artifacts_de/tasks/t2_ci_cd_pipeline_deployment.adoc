===== t2: CI/CD Pipeline Deployment (DONE, SP=3)
[id=t2_ci_cd_pipeline_deployment.adoc]

====== Beschreibung

* *Als* Entwickler,
* *möchte* ich die CI/CD-Pipeline aktualisieren
* *damit* ich sicherstellen kann, dass nach erfolgreicher Erstellung und Prüfung die neueste Version der Software in der Produktion eingesetzt wird

====== Akzeptanzkriterien

* [ ] Bei erfolgreichem Build wird ein Artefakt erstellt.
* [x] Das Artefakt wird automatisch in die Testumgebung deployed.
*** Artifact wird im Docker Container Registry gespeichert.
* [x] Die Schritte zur Einrichtung der CI/CD-Pipeline sind in der Projektdokumentation festgehalten.
** link:../../tech/modules/customer_website/pages/kubernetes.adoc[]