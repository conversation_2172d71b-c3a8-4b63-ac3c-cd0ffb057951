===== t3: CI/CD Pipeline Code Quality (TODO, SP=5)
[id=t1_ci_cd_pipeline]

====== Beschreibung

* *Als* Entwickler,
* *möchte ich* Werkzeuge zur Überprüfung von Qualitäts- und Kodierungsstandards einrichten,
* *damit* wird automatisch sichergestellt, dass Änderungen am System die Qualität des Systems nicht beeinträchtigen.

====== Akzeptanzkriterien
* [ ] Ein Smoke-Test wird nach dem Deployment durchgeführt, um die Funktionalität zu überprüfen.
* [ ] sonarqube eingerichtet
* [ ] eslint / sonarlint eingerichtet
