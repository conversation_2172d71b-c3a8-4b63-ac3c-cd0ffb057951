===== t1: CI/CD Build & Test Pipeline einrichten (DONE, SP=3)
[id=t1_ci_cd_build_pipeline]

====== Beschreibung

* **Als* Entwickler,
* *möchte ich* eine CI/CD-Pipeline einrichten,
* *damit* ich automatisierte Tests durchführen kann, um die Qualität des Codes zu verbessern

====== Akzeptanzkriterien

* [x] Die Pipeline ist mit dem Git-Repository des Projekts verbunden.
* [x] Änderungen im Hauptbranch lösen automatisch einen Build-Prozess aus.
* [x] Automatisierte Tests werden bei jedem Build ausgeführt.
* [x] Bei fehlerhaften Tests wird eine Benachrichtigung an das Entwicklerteam gesendet.
* [x] Der Build-Prozess installiert alle Abhängigkeiten und kompiliert den Code.

====== Anmerkungen

* Die Pipeline sollte skalierbar sein, um zukünftige Anforderungen zu berücksichtigen.
* Sicherheitsaspekte sind bei der Einrichtung der Pipeline zu beachten, insbesondere bei der Handhabung von Zugangsdaten und sensiblen Informationen.

