===== s2: Login - Email & Passwort (TODO, SP=5)
[id=s2_login_email]

====== Beschreibung

* **Als** Backoffice-Mitarbeiter,
* **möchte ich** mich mit meiner E-Mail-Adresse und meinem Passwort anmelden,\
* **damit** ich auf mein persönliches Dashboard und die Funktionen des ERP-Systems zugreifen kann.

====== Akzeptanzkriterien

* [ ] Der Verwaltungsmitarbeiter kann sich mit einer gültigen E-Mail-Adresse und Passwort anmelden.
* [ ] Bei falschen Anmeldedaten wird eine Fehlermeldung angezeigt.
* [ ] Nach erfolgreichem Login wird der Verwaltungsmitarbeiter auf die Startseite weitergeleitet.

====== Anmerkungen

* TODO

////
====== Akzeptanztests

* [ ] <PERSON> zu Test, ob der Login mit gültigen E-Mail-Daten erfolgreich ist.
* [ ] <PERSON> zu <PERSON>, ob eine Fehlermeldung angezeigt wird, wenn der Verwaltungsmitarbeiter falsche Anmeldedaten eingibt.
* [ ] <PERSON> zu <PERSON>, ob der Verwaltungsmitarbeiter nach dem Login auf die richtige Seite weitergeleitet wird.
////

