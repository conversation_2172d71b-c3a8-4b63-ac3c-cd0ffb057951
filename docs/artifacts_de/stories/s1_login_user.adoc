===== s1: Login - Social Media Username & Passwort (TODO, SP=5)
[id=s1_login_user]

====== Beschreibung

* **Als** Backoffice-Mitarbeiter,
* **möchte ich** mich mit meinem Benutzernamen und Passwort anmelden,
* **damit** ich auf mein persönliches Dashboard und die Funktionen des ERP-Systems zugreifen kann.

====== Akzeptanzkriterien
* No need to create a personal profile
** It simply uses your google / ... to authenticate


* [ ] Der Verwaltungsmitarbeiter kann sich mit einem gültigen Benutzernamen und Passwort anmelden.
* [ ] Bei falschen Anmeldedaten wird eine Fehlermeldung angezeigt.
* [ ] Nach erfolgreichem Login wird der Verwaltungsmitarbeiter auf die Startseite weitergeleitet.


====== Anmerkungen

* TODO

////
====== Akzeptanztests

* [ ] <PERSON> zu <PERSON>, ob der Login mit gültigen Anmeldedaten erfolgreich ist.
* [ ] <PERSON> zu <PERSON>, ob eine Fehlermeldung angezeigt wird, wenn der Verwaltungsmitarbeiter falsche Anmeldedaten eingibt.
* [ ] Link zu Test, ob der Verwaltungsmitarbeiter nach dem Login auf die richtige Seite weitergeleitet wird.
////

