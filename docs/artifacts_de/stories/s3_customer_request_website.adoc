===== s3: Kundenanfrage über die Webseite (DONE, SP=5)
[id=s3_kundenanfrage_webseite]

====== Beschreibung

* **Als** Handwerker-Kunden,
* **möchte ich** einen Handwerker anforden
* **damit** ich ein häusliches Problem zu Hause lösen kann, z.B. meine Waschmaschine reparieren

====== Akzeptanzkriterien

* [x] Der Kunde kann ein Anfrageformular ausfüllen, um einen Handwerker anzufordern.
* [x] Das Anfrageformular enthält Felder für Name, Adresse, Telefonnummer und Beschreibung des Problems.

====== Anmerkungen

* Es sollte sichergestellt werden, dass die Daten des Kunden sicher gespeichert werden.
* Die Benutzeroberfläche des Anfrageformulars sollte benutzerfreundlich gestaltet sein.

////
====== Akzeptanztests

* [x] <PERSON> zu <PERSON>, ob das Anfrageformular korrekt ausgefüllt werden kann:
** link:../../../customer_website/cypress/e2e/requestForm.cy.ts[]
////

