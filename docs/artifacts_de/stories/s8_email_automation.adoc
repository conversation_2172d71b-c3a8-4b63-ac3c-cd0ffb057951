===== Programmatischer Versand von E-Mail-Benachrichtigungen (TODO, SP=5)

====== Beschreibung

* *Als* Entwickler,
* *möchte ich* E-Mail-Benachrichtigungen programmgesteuert von unserer Plattform aus versenden können,
* *damit* ich Kunden und Stakeholder über wichtige System- oder Prozessereignisse informieren kann.

====== Akzeptanz-kriterien/tests

* [ ] E-Mail-Server (inkl. Konfiguration) ist eingerichtet & gespeichert.
** Authentifizierung wird beachtet
* [ ] REST API ist für den Versand verfügbar.
** E-Mail kann über HTTP-Request versendet werden

