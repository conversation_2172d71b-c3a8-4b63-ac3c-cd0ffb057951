===== Grundlegende Einrichtung von BC (TODO: SP=3)

====== Beschreibung
* Untersuchen, welche Entitäten wir in BC benötigen
* Sammeln der Daten, die wir in der Hauptinstanz und den Handwerkerinstanzen speichern möchten

====== Akzeptanzkriterien
* Wir müssen Informationen zu folgenden Punkten sammeln:
** Authentifizierung & Registrierung
** Meeting
** Kalender
** Ein Lead erstellen
** Wer hat Zeit, die Anfrage zu erfüllen
** Ein Meeting für den Handwerker planen
** Einen Kunden einrichten
** Stornierungsfrist
** Rechnung, z. B. 1 Std. plus Fahrtkosten
** Wir müssen Materialkosten als einzelne Zeile in der Rechnung hinzufügen können
*** Foto des Belegs aus dem Baumarkt
** Rechnung an den Kunden senden
** Wir müssen dies im Haupt-ERP-System sehen können, falls es Rückfragen gibt etc.
** Wir müssen angeben können, dass die Rechnung bezahlt wurde, nachdem jemand das Bankkonto geprüft hat
** Welches Format liefert BC standardmäßig? *PDF* / Zugferd / X Rechnung (nur Datenformat)?

* Ergebnis
** Dokumentation & Wissensaustausch

