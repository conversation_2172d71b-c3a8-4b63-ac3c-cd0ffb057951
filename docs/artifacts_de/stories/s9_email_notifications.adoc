===== Erfolgreiche Anfrage - E-Mail-Benachrichtigung (TODO, SP=5)

====== Beschreibung

* *Als* Handwerkerkunde,
* *möchte ich* eine E-Mail-Bestätigung erhalten, dass meine Anfrage erfolgreich versendet wurde,
* *damit* ich sicher sein kann, dass meine Anfrage eingegangen ist und ein Handwerker für mich organisiert wird.

====== Akzeptanz-kriterien/tests

* [x] Der zuvor konfigurierte E-Mail-Server wird verwendet.
* [x] Kl<PERSON><PERSON>, von wo die E-Mail „getriggert“ wird:
** in Nuxt
** Design-Entscheidung dokumentieren
* [x] Beispiel Trigger wird ausgelöst
** z.B. beim Erstellen eines neuen Kontakts
** Test wird geschrieben, um E-Mail-Server "wegzumocken"
* [x] Authentifizierung über API-Schlüssel oder Auth-Token ist implementiert.
* [x] Beispielkunde bekommt eine E-Mail mit Beispieltext
** Kundenummer steht in der E-Mail
** E-Mail Inhalt ist in minimalem HTML & CSS

