==== s10: Kunde legt Standort fest (TODO, SP=5)

===== Beschreibung

* *Als* Handwerkerkunde,
* *möchte ich* meinen Standort festlegen,
* *damit* die App mir Optionen anbieten kann,
** *wenn* ich mein Problem beschreibe / eine Bestellanfrage stelle.

===== Akzeptanzkriterien/Tests

* [ ] Einfache Textleiste, die es mir ermöglicht, meinen Standort einzugeben
* [ ] Es sollte einen vorformatierten Standort vorschlagen, sobald ich anfange, in die Suchleiste zu tippen
* [ ] Sollte auch nur die Eingabe von Stadt, Postleitzahl oder nur Straße erlauben
** Menschen möchten möglicherweise nicht sofort ihre spezifischen Details teilen
* [ ] Bitte auf ungewöhnliche Zeichen oder Emojis prüfen und den Kunden informieren, falls solche verwendet werden

===== Hinweise zur Implementierung
* Überleg<PERSON> Si<PERSON>, eine Bibliothek zu verwenden, um vorhandene Adressen automatisch zu vervollständigen.

====== Nächste Story
* [ ] Regionen, die wir aktuell nicht unterstützen, sollten zunächst eine Warnung anzeigen + fragen, ob der Nutzer eine E-Mail erhalten möchte, wenn die Region verfügbar wird
* [ ] Einen GPS-Standort-Button hinzufügen, um den aktuellen Standort zu suchen
** Kein Browser-Popup: sollte nur um Erlaubnis fragen, wenn der Standort-Button gedrückt wird
* [ ] Den Standort vorübergehend speichern

===== Notizen

* Das Design ist hier zu finden:
** https://miro.com/app/board/uXjVLDW9Zr0=/?moveToWidget=3458764607461738770&cot=14[Link zum Miro-Board]
