@startuml

actor Kunde
participant <PERSON><PERSON><PERSON><PERSON><PERSON> as "BC Hauptinstanz"
participant <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  as "BC Handwerkerinstanz"

Kunde -> Hauptinstanz : Anfrage senden
Hauptinstanz -> Hauptinstanz : Kunde gespeichert
Hauptinstanz -> Hauptinstanz : Sales Quote gespeichert (als Repräsentation der Anfrage)
Hauptinstanz -> Hauptinstanz : Bonitätsprüfung findet statt

== Handwerker nimmt an ==
Hauptinstanz -> Handwerkerinstanz : Kunde angelegt
Hauptinstanz -> Handwerkerinstanz : Sales Order aus Sales Quote angelegt

Handwerkerinstanz -> Handwerkerinstanz : Fügt Inventar hinzu
Handwerkerinstanz -> Handwerkerinstanz : Schließt Arbeiten ab

== Rechnungserstellung ==
Handwerkerinstanz -> Handwerkerinstanz : Sales Invoice wird aus der Sales Order erstellt
Handwerkerinstanz -> Kunde : Rechnung wird geschickt

== Bezahlung ==
Kunde -> Handwerkerinstanz : Rechnung bezahlen
Handwerkerinstanz -> Handwerkerinstanz : Rechnung wird der Sales Order zugewiesen
Handwerkerinstanz -> Handwerkerinstanz : Sales Order wird geschlossen

== Nebenläufige Synchronisation ==
Handwerkerinstanz -> Handwerkerinstanz : Rechnung & Belege werden an Datev \nfür Vorsteueranmeldung geschickt

@enduml
