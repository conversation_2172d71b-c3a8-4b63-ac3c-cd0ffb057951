@startuml
' Bestellvorgang, Neukunde, Telefon, ASAP, Handwerker hat Zeit

title Neukunde möchte einen Handwerker so bald wie möglich

actor <PERSON><PERSON> as "Neukunde"
' entity "KundenApp" as "Kunden-App"
' entity "Webseite" as Webseite
actor <PERSON><PERSON><PERSON><PERSON><PERSON> as "Telefon-Mann"
participant CallCenter as "Call-Center Frontend"
database BC as "Business Central"
' database "BC Handwerker Instanz" as BCHI
participant <PERSON><PERSON><PERSON><PERSON><PERSON> as "Handwerker App"
actor <PERSON><PERSON><PERSON>

== Kunde ruft an ==

Kunde -> TelefonMann : ruft an als Neukunde
TelefonMann -> CallCenter : pflegt Kundendaten ein

group Neukunde anlegen
    CallCenter <-> BC : synchronisiert Kundendaten
    BC -> BC : führt Bonitätsprüfung aus
end

CallCenter -> TelefonMann : zeigt <PERSON> (inkl. Bonität) an
TelefonMann -> Kunde : Auftrag angenommen, wir melden uns.

== Terminvergabe ==

TelefonMann -> CallCenter : fragt verfügbare AK an

group AK auswählen
    CallCenter -> BC : getAvailableWorkers
    BC -> BC : sucht passende Handwerker aus
    BC -> HandwerkerApp : getAvailability
    HandwerkerApp -> Handwerker : zeigt Notification an
    Handwerker -> HandwerkerApp : stimmt Termin zu
    HandwerkerApp -> BC : agreeAppointment
    BC -> CallCenter : addAppointmentToCallbackQueue
end

CallCenter -> TelefonMann : Kunden anzeigen, der zurückgerufen werden muss
TelefonMann -> Kunde : ruft an und bestätigt Termin

@enduml