<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent; color-scheme: light dark;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="746px" height="1082px" viewBox="-0.5 -0.5 746 1082" content="&lt;mxfile host=&quot;Electron&quot; agent=&quot;Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.0.4 Chrome/128.0.6613.186 Electron/32.2.5 Safari/537.36&quot; version=&quot;26.0.4&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;f9TByYDd4HNxBojRknDI&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;1857&quot; dy=&quot;1275&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;827&quot; pageHeight=&quot;1169&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-9&quot; value=&quot;Handwerker&quot; style=&quot;swimlane;whiteSpace=wrap;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;80&quot; y=&quot;40&quot; width=&quot;226.67&quot; height=&quot;1080&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-18&quot; value=&quot;Kunden&quot; style=&quot;swimlane;whiteSpace=wrap;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;306.67&quot; y=&quot;40&quot; width=&quot;226.67&quot; height=&quot;1080&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-37&quot; value=&quot;System&quot; style=&quot;swimlane;whiteSpace=wrap;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;533.33&quot; y=&quot;40&quot; width=&quot;226.67&quot; height=&quot;1080&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-44&quot; value=&quot;&amp;lt;p&amp;gt;&amp;lt;font face=&amp;quot;Helvetica&amp;quot;&amp;gt;BC sendet Anfrage an Handwerker-App(s)&amp;lt;/font&amp;gt;&amp;lt;/p&amp;gt;&quot; style=&quot;shape=callout;whiteSpace=wrap;html=1;perimeter=calloutPerimeter;hachureGap=4;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;container=0;swimlaneLine=1;shadow=0;rounded=0;&quot; parent=&quot;YmUvfYEzopszHFRx7z3P-37&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;64.76142857142838&quot; y=&quot;195&quot; width=&quot;97.14285714285714&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-43&quot; value=&quot;&quot; style=&quot;endArrow=open;strokeColor=#FF0000;endFill=1;rounded=0;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-15&quot; target=&quot;YmUvfYEzopszHFRx7z3P-24&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-51&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;hachureGap=4;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-10&quot; target=&quot;YmUvfYEzopszHFRx7z3P-46&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;169.04761904761904&quot; y=&quot;160&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-13&quot; value=&quot;user action&quot; style=&quot;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;193.33333333333331&quot; y=&quot;290&quot; width=&quot;56.66666666666666&quot; height=&quot;49.99999999999999&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-15&quot; value=&quot;post command&quot; style=&quot;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;128.57142857142856&quot; y=&quot;375&quot; width=&quot;89.04761904761904&quot; height=&quot;49.99999999999999&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-16&quot; value=&quot;&quot; style=&quot;endArrow=open;strokeColor=#FF0000;endFill=1;rounded=0;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-13&quot; target=&quot;YmUvfYEzopszHFRx7z3P-15&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-50&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;hachureGap=4;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-46&quot; target=&quot;YmUvfYEzopszHFRx7z3P-13&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-46&quot; value=&quot;Auftrag anehmen&quot; style=&quot;rhombus;whiteSpace=wrap;html=1;hachureGap=4;fontFamily=Helvetica;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;140.71428571428572&quot; y=&quot;160&quot; width=&quot;64.76190476190474&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-47&quot; value=&quot;Schnellste gewinnt den Auftrag&quot; style=&quot;shape=callout;whiteSpace=wrap;html=1;perimeter=calloutPerimeter;hachureGap=4;fontFamily=Helvetica;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;15.238095238095255&quot; y=&quot;110&quot; width=&quot;97.14285714285714&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-21&quot; value=&quot;idle&quot; style=&quot;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;322.85714285714283&quot; y=&quot;160&quot; width=&quot;89.04761904761904&quot; height=&quot;49.99999999999999&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-22&quot; value=&quot;check for &amp;#xa;new commands&quot; style=&quot;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;322.85714285714283&quot; y=&quot;270&quot; width=&quot;89.04761904761904&quot; height=&quot;49.99999999999999&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-23&quot; value=&quot;&quot; style=&quot;endArrow=open;strokeColor=#FF0000;endFill=1;rounded=0;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-21&quot; target=&quot;YmUvfYEzopszHFRx7z3P-22&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-24&quot; value=&quot;command queue&quot; style=&quot;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;322.85714285714283&quot; y=&quot;375&quot; width=&quot;89.04761904761904&quot; height=&quot;49.99999999999999&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-25&quot; value=&quot;&quot; style=&quot;endArrow=open;strokeColor=#FF0000;endFill=1;rounded=0;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-22&quot; target=&quot;YmUvfYEzopszHFRx7z3P-24&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-26&quot; value=&quot;queue empty&quot; style=&quot;rhombus;fillColor=#ffffc0;strokeColor=#ff0000;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;428.0952380952381&quot; y=&quot;275&quot; width=&quot;64.76190476190474&quot; height=&quot;40&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-27&quot; value=&quot;yes&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=horizontal;align=left;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#FF0000;exitX=0.5;exitY=0;endFill=1;rounded=0;entryX=0.75;entryY=0.5;entryPerimeter=0;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-26&quot; target=&quot;YmUvfYEzopszHFRx7z3P-30&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;436.19047619047615&quot; y=&quot;200&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;460.4761904761904&quot; y=&quot;229.99999999999997&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-28&quot; value=&quot;no&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=horizontal;align=left;verticalAlign=top;endArrow=open;endSize=8;strokeColor=#FF0000;endFill=1;rounded=0;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-26&quot; target=&quot;YmUvfYEzopszHFRx7z3P-32&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-1&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;460.4761904761904&quot; y=&quot;355&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-29&quot; value=&quot;&quot; style=&quot;endArrow=open;strokeColor=#FF0000;endFill=1;rounded=0;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-22&quot; target=&quot;YmUvfYEzopszHFRx7z3P-26&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-30&quot; value=&quot;&quot; style=&quot;shape=line;strokeWidth=6;strokeColor=#ff0000;rotation=90;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;411.9047619047618&quot; y=&quot;177.5&quot; width=&quot;40.47619047619047&quot; height=&quot;15&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-31&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=horizontal;verticalAlign=bottom;endArrow=open;endSize=8;strokeColor=#FF0000;endFill=1;rounded=0;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-30&quot; target=&quot;YmUvfYEzopszHFRx7z3P-21&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;411.9047619047618&quot; y=&quot;140&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;492.8571428571428&quot; y=&quot;190&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-32&quot; value=&quot;dispatch&amp;#xa;command&amp;#xa;worker thread&quot; style=&quot;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;419.99999999999994&quot; y=&quot;375&quot; width=&quot;89.04761904761904&quot; height=&quot;49.99999999999999&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-33&quot; value=&quot;critical&amp;#xa;section&quot; style=&quot;shape=note;whiteSpace=wrap;size=17;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;391.66666666666663&quot; y=&quot;540&quot; width=&quot;80.95238095238093&quot; height=&quot;49.99999999999999&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-34&quot; value=&quot;&quot; style=&quot;endArrow=none;strokeColor=#FF0000;endFill=0;rounded=0;dashed=1;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-24&quot; target=&quot;YmUvfYEzopszHFRx7z3P-33&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-35&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;strokeColor=#FF0000;fillColor=#FF0000;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;504.9999999999999&quot; y=&quot;445&quot; width=&quot;4.047619047619047&quot; height=&quot;44.99999999999999&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-36&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=horizontal;entryX=0;entryY=0.5;strokeColor=#FF0000;endArrow=open;endFill=1;rounded=0;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-32&quot; target=&quot;YmUvfYEzopszHFRx7z3P-35&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;100&quot; height=&quot;100&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;355.2380952380952&quot; y=&quot;530&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;436.19047619047615&quot; y=&quot;430&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;464.52380952380946&quot; y=&quot;449.99999999999994&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-38&quot; value=&quot;process&amp;#xa;command&quot; style=&quot;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;606.1904761904761&quot; y=&quot;455&quot; width=&quot;89.04761904761904&quot; height=&quot;49.99999999999999&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-39&quot; value=&quot;&quot; style=&quot;edgeStyle=elbowEdgeStyle;elbow=horizontal;strokeColor=#FF0000;endArrow=open;endFill=1;rounded=0;entryX=0.25;entryY=0.5;entryPerimeter=0;swimlaneLine=1;&quot; parent=&quot;1&quot; target=&quot;YmUvfYEzopszHFRx7z3P-30&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;100&quot; height=&quot;100&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;509.047619047619&quot; y=&quot;459.99999999999994&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;436.19047619047615&quot; y=&quot;170&quot; as=&quot;targetPoint&quot; /&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;525.2380952380952&quot; y=&quot;184.99999999999997&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-40&quot; value=&quot;&quot; style=&quot;edgeStyle=none;strokeColor=#FF0000;endArrow=open;endFill=1;rounded=0;swimlaneLine=1;&quot; parent=&quot;1&quot; target=&quot;YmUvfYEzopszHFRx7z3P-38&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;100&quot; height=&quot;100&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;509.047619047619&quot; y=&quot;479.49999999999994&quot; as=&quot;sourcePoint&quot; /&gt;&#10;            &lt;mxPoint x=&quot;606.1904761904761&quot; y=&quot;479.49999999999994&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-41&quot; value=&quot;&quot; style=&quot;ellipse;shape=endState;fillColor=#000000;strokeColor=#ff0000;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;638.57&quot; y=&quot;550&quot; width=&quot;21.43&quot; height=&quot;20&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-42&quot; value=&quot;&quot; style=&quot;endArrow=open;strokeColor=#FF0000;endFill=1;rounded=0;swimlaneLine=1;&quot; parent=&quot;1&quot; source=&quot;YmUvfYEzopszHFRx7z3P-38&quot; target=&quot;YmUvfYEzopszHFRx7z3P-41&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;YmUvfYEzopszHFRx7z3P-10&quot; value=&quot;&quot; style=&quot;ellipse;shape=startState;fillColor=#000000;strokeColor=#ff0000;container=0;swimlaneLine=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;630.48&quot; y=&quot;100&quot; width=&quot;29.52&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;"><defs/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="YmUvfYEzopszHFRx7z3P-9"><g><path d="M 65 23 L 65 0 L 291.67 0 L 291.67 23" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 65 23 L 65 1080 L 291.67 1080 L 291.67 23" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 65 23 L 291.67 23" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 225px; height: 1px; padding-top: 12px; margin-left: 66px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">Handwerker</div></div></div></foreignObject><text x="178" y="15" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">Handwerker</text></switch></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-18"><g><path d="M 291.67 23 L 291.67 0 L 518.34 0 L 518.34 23" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 291.67 23 L 291.67 1080 L 518.34 1080 L 518.34 23" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 291.67 23 L 518.34 23" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 225px; height: 1px; padding-top: 12px; margin-left: 293px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">Kunden</div></div></div></foreignObject><text x="405" y="15" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">Kunden</text></switch></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-37"><g><path d="M 518.33 23 L 518.33 0 L 745 0 L 745 23" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 518.33 23 L 518.33 1080 L 745 1080 L 745 23" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 518.33 23 L 745 23" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 225px; height: 1px; padding-top: 12px; margin-left: 519px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">System</div></div></div></foreignObject><text x="632" y="15" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle" font-weight="bold">System</text></switch></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-44"><g><path d="M 583.09 195 L 680.23 195 L 680.23 245 L 651.66 245 L 631.66 275 L 631.66 245 L 583.09 245 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 95px; height: 1px; padding-top: 220px; margin-left: 584px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Architects Daughter&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; "><p><font face="Helvetica">BC sendet Anfrage an Handwerker-App(s)</font></p></div></div></div></foreignObject><text x="632" y="224" fill="light-dark(#000000, #ffffff)" font-family="&quot;Architects Daughter&quot;" font-size="12px" text-anchor="middle">BC sendet Anfrag...</text></switch></g></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-43"><g><path d="M 202.62 360 L 305.62 360" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 299.74 363.5 L 306.74 360 L 299.74 356.5" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-51"><g><path d="M 615.48 75 L 158.17 75 L 158.17 113.72" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 158.17 118.97 L 154.67 111.97 L 158.17 113.72 L 161.67 111.97 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-13"><g><rect x="178.33" y="250" width="56.67" height="50" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"><text x="206.17" y="279.5">user action</text></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-15"><g><rect x="113.57" y="335" width="89.05" height="50" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"><text x="157.6" y="364.5">post command</text></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-16"><g><path d="M 192.38 300 L 173.49 333.06" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 173.37 326.22 L 172.94 334.03 L 179.45 329.69" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-50"><g><path d="M 190.48 160 L 210.5 160 L 210.5 225 L 206.67 225 L 206.67 243.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 206.67 248.88 L 203.17 241.88 L 206.67 243.63 L 210.17 241.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-46"><g><path d="M 158.1 120 L 190.48 160 L 158.1 200 L 125.71 160 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 63px; height: 1px; padding-top: 160px; margin-left: 127px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Auftrag anehmen</div></div></div></foreignObject><text x="158" y="164" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Auftrag ane...</text></switch></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-47"><g><path d="M 0.24 70 L 97.38 70 L 97.38 120 L 68.81 120 L 48.81 150 L 48.81 120 L 0.24 120 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 95px; height: 1px; padding-top: 95px; margin-left: 1px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">Schnellste gewinnt den Auftrag</div></div></div></foreignObject><text x="49" y="99" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">Schnellste gewin...</text></switch></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-21"><g><rect x="307.86" y="120" width="89.05" height="50" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"><text x="351.88" y="149.5">idle</text></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-22"><g><rect x="307.86" y="230" width="89.05" height="50" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"><text x="351.88" y="252.5">check for </text><text x="351.88" y="266.5">new commands</text></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-23"><g><path d="M 352.38 170 L 352.38 227.76" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 348.88 221.88 L 352.38 228.88 L 355.88 221.88" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-24"><g><rect x="307.86" y="335" width="89.05" height="50" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"><text x="351.88" y="364.5">command queue</text></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-25"><g><path d="M 352.38 280 L 352.38 332.76" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 348.88 326.88 L 352.38 333.88 L 355.88 326.88" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-26"><g><path d="M 445.48 235 L 477.86 255 L 445.48 275 L 413.1 255 Z" fill="#ffffc0" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 255, 192), rgb(255, 255, 192)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g><g><g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"><text x="444.98" y="259.5">queue empty</text></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-27"><g><path d="M 445.48 235 L 445.48 155.12 L 419.38 155.12" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 427.26 150.62 L 418.26 155.12 L 427.26 159.62" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g><g><g fill="#000000" font-family="&quot;Helvetica&quot;" font-size="11px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"><rect fill="#ffffff" stroke="none" x="447" y="220" width="19" height="14" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><text x="446.98" y="229.5">yes</text></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-28"><g><path d="M 444.67 274.5 L 444.67 305 L 444.67 332.76" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 440.17 324.88 L 444.67 333.88 L 449.17 324.88" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g><g><g fill="#000000" font-family="&quot;Helvetica&quot;" font-size="11px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"><rect fill="#ffffff" stroke="none" x="446" y="282" width="14" height="14" stroke-width="0" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212));"/><text x="446.17" y="291">no</text></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-29"><g><path d="M 396.9 255 L 410.86 255" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 404.98 258.5 L 411.98 255 L 404.98 251.5" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-30"><g><path d="M 396.9 145 L 437.38 145" fill="none" stroke="#ff0000" stroke-width="6" stroke-miterlimit="10" transform="rotate(90,417.14,145)" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-31"><g><path d="M 409.64 145 L 398.57 145" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 406.45 140.5 L 397.45 145 L 406.45 149.5" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-32"><g><rect x="405" y="335" width="89.05" height="50" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"><text x="449.02" y="350.5">dispatch</text><text x="449.02" y="364.5">command</text><text x="449.02" y="378.5">worker thread</text></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-33"><g><path d="M 376.67 500 L 440.62 500 L 457.62 517 L 457.62 550 L 376.67 550 L 376.67 500 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/><path d="M 440.62 500 L 440.62 517 L 457.62 517" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 79px; height: 1px; padding-top: 525px; margin-left: 378px;"><div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; "><div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">critical<br />section</div></div></div></foreignObject><text x="417" y="529" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">critical...</text></switch></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-34"><g><path d="M 362.19 385 L 407.33 500" fill="none" stroke="#ff0000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-35"><g><rect x="490" y="405" width="4.05" height="45" fill="#ff0000" stroke="#ff0000" pointer-events="all" style="fill: light-dark(rgb(255, 0, 0), rgb(255, 0, 0)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-36"><g><path d="M 449.52 385 L 449.52 427.5 L 487.76 427.5" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 481.88 431 L 488.88 427.5 L 481.88 424" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-38"><g><rect x="591.19" y="415" width="89.05" height="50" fill="#ffffff" stroke="#000000" pointer-events="all" style="fill: light-dark(#ffffff, var(--ge-dark-color, #121212)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/></g><g><g fill="#000000" font-family="&quot;Helvetica&quot;" text-anchor="middle" font-size="12px" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"><text x="635.21" y="437.5">process</text><text x="635.21" y="451.5">command</text></g></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-39"><g><path d="M 494.05 420 L 510.24 420 L 510.24 134.88 L 419.38 134.88" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 425.26 131.38 L 418.26 134.88 L 425.26 138.38" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-40"><g><path d="M 494.05 439.5 L 588.95 439.83" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 583.06 443.31 L 590.07 439.84 L 583.08 436.31" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-41"><g><ellipse cx="634.29" cy="520" rx="6.715" ry="6" fill="#000000" stroke="#ff0000" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(0, 0, 0)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><ellipse cx="634.29" cy="520" rx="10.715" ry="10" fill="none" stroke="#ff0000" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-42"><g><path d="M 635.2 465 L 634.33 507.76" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/><path d="M 630.95 501.81 L 634.31 508.88 L 637.95 501.96" fill="none" stroke="#ff0000" stroke-miterlimit="10" pointer-events="all" style="stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g><g data-cell-id="YmUvfYEzopszHFRx7z3P-10"><g><ellipse cx="630.24" cy="75" rx="10.76" ry="11" fill="#000000" stroke="#ff0000" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(0, 0, 0)); stroke: light-dark(rgb(255, 0, 0), rgb(255, 0, 0));"/></g></g></g></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>