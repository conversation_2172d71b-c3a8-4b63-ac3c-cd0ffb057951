@startuml
skinparam defaultFontName "Arial"

'v1 = breakthrough/Durchstich
'v2 = in naher Zukunft lösen
'v3 = in ferner Zukunft lösen (bis Oktober 2025),
'v4 = irgendwann mal
'grey = default = nicht Teil des Durchstichs
!define v1 #a8f0b1
!define v2 #f0e6a8
!define v3 #f0b4a8
!define v4 #a172b3
!define default #f1f1f1


start

v2:Kunde legt seine Login-Daten fest oder nutzt Provider (z.B. Google);
v1:Kunde gibt seine Kundendaten ein;
v2:<PERSON>nde gibt seine Kontodaten an;
v1:Webseite sendet die Daten jeweils an BC;
v2:BC führt Bonitätsprüfung aus;

stop

@enduml