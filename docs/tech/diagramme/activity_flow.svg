<svg contentStyleType='text/css' height='5378px' preserveAspectRatio='none' style='width:3440px;height:5378px;background:#FFFFFF;' version='1.1' viewBox='0 0 3440 5378' width='3440px' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns='http://www.w3.org/2000/svg' zoomAndPan='magnify'><defs/><g><ellipse cx='2156.262' cy='20' fill='#222222' rx='10' ry='10' style='stroke:#222222;stroke-width:1.0;'/><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='230.7734' x='2040.8752' y='50'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='210.7734' x='2050.8752' y='71.2559'>Handwerker &#246;ffnet die Handwerker-App</text><line style='stroke:#181818;stroke-width:1.5;' x1='1875.842' x2='2213.4753' y1='103.7988' y2='103.7988'/><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='227.457' x='1762.1135' y='123.7988'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='207.457' x='1772.1135' y='145.0547'>Handwerker gibt seine Login-Daten ein</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='252.1074' x='1749.7883' y='177.5977'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='232.1074' x='1759.7883' y='198.8535'>Webseite authentifiziert Handwerker bei BC</text><polygon fill='#F1F1F1' points='1825.0906,246.3965,1926.5935,246.3965,1938.5935,258.3965,1926.5935,270.3965,1825.0906,270.3965,1813.0906,258.3965,1825.0906,246.3965' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='8.5615' x='1879.842' y='280.7144'>ja</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='101.5029' x='1825.0906' y='262.3899'>ist Login erfolgreich?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='1938.5935' y='256.0654'>nein</text><ellipse cx='1981.3904' cy='258.3965' fill='none' rx='11' ry='11' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='1981.3904' cy='258.3965' fill='#222222' rx='6' ry='6' style='stroke:#222222;stroke-width:1.0;'/><path d='M2414.5603,201.4478 L2414.5603,209.9221 L2394.5603,213.9221 L2414.5603,217.9221 L2414.5603,226.3965 A0,0 0 0 0 2414.5603,226.3965 L2562.7356,226.3965 A0,0 0 0 0 2562.7356,226.3965 L2562.7356,211.4478 L2552.7356,201.4478 L2414.5603,201.4478 A0,0 0 0 0 2414.5603,201.4478 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M2552.7356,201.4478 L2552.7356,211.4478 L2562.7356,211.4478 L2552.7356,201.4478 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='127.1753' x='2420.5603' y='218.6416'>soll das m&#246;glich sein?</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='362.1699' x='2032.3904' y='197.0227'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='342.1699' x='2042.3904' y='218.2786'>Handwerker meldet sich &#252;ber anderen Provider an (z.B. Google)</text><line style='stroke:#181818;stroke-width:1.5;' x1='1875.842' x2='2213.4753' y1='302.2144' y2='302.2144'/><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='166.0742' x='2073.2249' y='322.2144'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='146.0742' x='2083.2249' y='343.4702'>Handwerker ist angemeldet</text><ellipse cx='2156.262' cy='387.0132' fill='none' rx='11' ry='11' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='2156.262' cy='387.0132' fill='#222222' rx='6' ry='6' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='2156.262' cy='418.0132' fill='#222222' rx='10' ry='10' style='stroke:#222222;stroke-width:1.0;'/><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='201.459' x='2055.5325' y='448.0132'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='181.459' x='2065.5325' y='469.269'>Kunde ben&#246;tigt einen Handwerker</text><line style='stroke:#181818;stroke-width:1.5;' x1='1603.6006' x2='2504.6493' y1='501.812' y2='501.812'/><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='92.0527' x='1557.5742' y='642.4353'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='72.0527' x='1567.5742' y='663.6912'>Kunde ruft an</text><polygon fill='#F1F1F1' points='1532.3772,711.2341,1674.824,711.2341,1686.824,723.2341,1674.824,735.2341,1532.3772,735.2341,1520.3772,723.2341,1532.3772,711.2341' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='142.4468' x='1532.3772' y='727.2275'>ist Kunde bereits im System?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='8.5615' x='1511.8157' y='720.9031'>ja</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='1686.824' y='720.9031'>nein</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='262.8242' x='1333.4424' y='745.2341'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='242.8242' x='1343.4424' y='766.49'>CallCenter App fragt Kundendaten von BC ab</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='193.4258' x='1368.1416' y='799.033'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='173.4258' x='1378.1416' y='820.2888'>Telefon-Mann legitimiert Kunden</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='208.1211' x='1638.2861' y='745.2341'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='188.1211' x='1648.2861' y='766.49'>Telefon-Mann erfasst Kundendaten</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='252.1602' x='1616.2666' y='799.033'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='232.1602' x='1626.2666' y='820.2888'>CallCenter App sendet Kundendaten an BC</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='157.4434' x='1663.625' y='867.8318'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='137.4434' x='1673.625' y='889.0876'>BC legt neuen Kunden an</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='174.0898' x='1655.3018' y='936.6306'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='154.0898' x='1665.3018' y='957.8865'>BC f&#252;hrt Bonit&#228;tspr&#252;fung aus</text><polygon fill='#F1F1F1' points='1603.6006,976.4294,1615.6006,988.4294,1603.6006,1000.4294,1591.6006,988.4294,1603.6006,976.4294' style='stroke:#181818;stroke-width:0.5;'/><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='260.1523' x='1473.5244' y='1035.4294'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='240.1523' x='1483.5244' y='1056.6853'>Kunde sagt welche Dienstleistung er ben&#246;tigt</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='321.5176' x='1442.8418' y='1104.2283'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='301.5176' x='1452.8418' y='1125.4841'>Telefon-Mann pr&#252;ft ob die Dienstleistung angeboten wird</text><polygon fill='#F1F1F1' points='1526.8557,1173.0271,1680.3455,1173.0271,1692.3455,1185.0271,1680.3455,1197.0271,1526.8557,1197.0271,1514.8557,1185.0271,1526.8557,1173.0271' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='153.4897' x='1526.8557' y='1189.0205'>wird Dienstleistung angeboten?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='1692.3455' y='1182.696'>nein</text><ellipse cx='1735.1423' cy='1185.0271' fill='none' rx='11' ry='11' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='1735.1423' cy='1185.0271' fill='#222222' rx='6' ry='6' style='stroke:#222222;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.5;' x1='2387.5848' x2='2605.0321' y1='521.812' y2='521.812'/><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='172.7656' x='2301.202' y='541.812'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='152.7656' x='2311.202' y='563.0679'>Kunde besucht die Webseite</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='206.1289' x='2501.9677' y='541.812'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='186.1289' x='2511.9677' y='563.0679'>Kunde installiert und &#246;ffnet die App</text><line style='stroke:#181818;stroke-width:1.5;' x1='2387.5848' x2='2605.0321' y1='595.6108' y2='595.6108'/><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='194.7793' x='2407.2596' y='615.6108'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='174.7793' x='2417.2596' y='636.8667'>Kunde sucht nach Dienstleistung</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='187.4375' x='2410.9305' y='684.4097'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='167.4375' x='2420.9305' y='705.6655'>Kunde w&#228;hlt Dienstleistung aus</text><polygon fill='#F0E6A8' points='2427.9044,753.2085,2581.3942,753.2085,2593.3942,765.2085,2581.3942,777.2085,2427.9044,777.2085,2415.9044,765.2085,2427.9044,753.2085' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='153.4897' x='2427.9044' y='769.2019'>wird Dienstleistung angeboten?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='2593.3942' y='762.8774'>nein</text><ellipse cx='2636.191' cy='765.2085' fill='none' rx='11' ry='11' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='2636.191' cy='765.2085' fill='#222222' rx='6' ry='6' style='stroke:#222222;stroke-width:1.0;'/><polygon fill='#F0E6A8' points='2436.1732,893.533,2573.1254,893.533,2585.1254,905.533,2573.1254,917.533,2436.1732,917.533,2424.1732,905.533,2436.1732,893.533' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='136.9521' x='2436.1732' y='909.5264'>ist Kunde bereits registriert?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='8.5615' x='2415.6117' y='903.2019'>ja</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='2585.1254' y='903.2019'>nein</text><line style='stroke:#181818;stroke-width:1.5;' x1='2054.8203' x2='2377.4565' y1='927.533' y2='927.533'/><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='197.4629' x='1956.0889' y='949.033'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='177.4629' x='1966.0889' y='970.2888'>Kunde gibt seine Login-Daten ein</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='228.7871' x='1940.4268' y='1017.8318'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='208.7871' x='1950.4268' y='1039.0876'>Webseite authentifiziert Kunden bei BC</text><polygon fill='#F1F1F1' points='2004.0688,1086.6306,2105.5718,1086.6306,2117.5718,1098.6306,2105.5718,1110.6306,2004.0688,1110.6306,1992.0688,1098.6306,2004.0688,1086.6306' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='8.5615' x='2058.8203' y='1120.9485'>ja</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='101.5029' x='2004.0688' y='1102.624'>ist Login erfolgreich?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='2117.5718' y='1096.2996'>nein</text><ellipse cx='2160.3687' cy='1098.6306' fill='none' rx='11' ry='11' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='2160.3687' cy='1098.6306' fill='#222222' rx='6' ry='6' style='stroke:#222222;stroke-width:1.0;'/><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='332.1758' x='2211.3687' y='1037.2568'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='312.1758' x='2221.3687' y='1058.5127'>Kunde meldet sich &#252;ber anderen Provider an (z.B. Google)</text><line style='stroke:#181818;stroke-width:1.5;' x1='2054.8203' x2='2377.4565' y1='1179.2795' y2='1179.2795'/><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='379.5371' x='2577.5444' y='927.533'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='359.5371' x='2587.5444' y='948.7888'>Kunde legt seine Login-Daten fest oder nutzt Provider (z.B. Google)</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='203.4863' x='2665.5698' y='996.3318'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='183.4863' x='2675.5698' y='1017.5876'>Kunde gibt seine Kundendaten ein</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='190.8066' x='2671.9097' y='1065.1306'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='170.8066' x='2681.9097' y='1086.3865'>Kunde gibt seine Kontodaten an</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='240.1191' x='2647.2534' y='1133.9294'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='220.1191' x='2657.2534' y='1155.1853'>Webseite sendet die Daten jeweils an BC</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='174.0898' x='2680.2681' y='1202.7283'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='154.0898' x='2690.2681' y='1223.9841'>BC f&#252;hrt Bonit&#228;tspr&#252;fung aus</text><polygon fill='#F0E6A8' points='2504.6493,1242.5271,2516.6493,1254.5271,2504.6493,1266.5271,2492.6493,1254.5271,2504.6493,1242.5271' style='stroke:#181818;stroke-width:0.5;'/><polygon fill='#F0E6A8' points='2430.351,845.2085,2578.9476,845.2085,2590.9476,857.2085,2578.9476,869.2085,2430.351,869.2085,2418.351,857.2085,2430.351,845.2085' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='2508.6493' y='879.5264'>nein</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='148.5967' x='2430.351' y='861.2019'>ist Kunde bereits angemeldet?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='8.5615' x='2590.9476' y='854.8774'>ja</text><polygon fill='#F0E6A8' points='2504.6493,1286.5271,2516.6493,1298.5271,2504.6493,1310.5271,2492.6493,1298.5271,2504.6493,1286.5271' style='stroke:#181818;stroke-width:0.5;'/><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='136.0801' x='2436.6093' y='1330.5271'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='116.0801' x='2446.6093' y='1351.783'>Kunde ist angemeldet</text><line style='stroke:#181818;stroke-width:1.5;' x1='1603.6006' x2='2504.6493' y1='1384.3259' y2='1384.3259'/><rect fill='#F0B4A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='240.1367' x='2036.1936' y='1404.3259'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='220.1367' x='2046.1936' y='1425.5818'>Kunde w&#228;hlt Zeitpunkt f&#252;r die Ausf&#252;hrung</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='176.7617' x='2067.8811' y='1458.1248'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='156.7617' x='2077.8811' y='1479.3806'>Anfrage wird an BC gesendet</text><rect fill='#F0B4A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='172.7305' x='2069.8967' y='1511.9236'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='152.7305' x='2079.8967' y='1533.1794'>BC ermittelt 3 verf&#252;gbare AK</text><rect fill='#F0B4A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='188.0469' x='2062.2385' y='1565.7224'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='168.0469' x='2072.2385' y='1586.9783'>BC ermittelt jeweils Anfahrt-Zeit</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='245.4453' x='2033.5393' y='1619.5212'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='225.4453' x='2043.5393' y='1640.7771'>BC sendet Anfrage an Handwerker-App(s)</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='251.4688' x='2030.5276' y='1673.3201'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='231.4688' x='2040.5276' y='1694.5759'>Handwerker-App zeigt Benachrichtigung an</text><polygon fill='#F1F1F1' points='2125.6897,1727.1189,2186.8342,1727.1189,2198.8342,1739.1189,2186.8342,1751.1189,2125.6897,1751.1189,2113.6897,1739.1189,2125.6897,1727.1189' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='61.1445' x='2125.6897' y='1743.1123'>AK Reaktion</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='271.3262' x='1725.9329' y='1786.4167'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='251.3262' x='1735.9329' y='1807.6726'>Handwerker-App sendet "angenommen" an BC</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='288.1074' x='1717.5422' y='1840.2156'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='268.1074' x='1727.5422' y='1861.4714'>BC tr&#228;gt Termin im Kalender des Handwerkers ein</text><rect fill='#F0B4A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='376.0566' x='1673.5676' y='1894.0144'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='356.0566' x='1683.5676' y='1915.2703'>BC sendet "Auftrag bereits vergeben" an andere Handwerker-Apps</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='298.6719' x='2069.6243' y='1786.4167'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='278.6719' x='2079.6243' y='1807.6726'>Handwerker-App zeigt "Auftrag bereits vergeben" an</text><ellipse cx='2218.9602' cy='1851.2156' fill='none' rx='11' ry='11' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='2218.9602' cy='1851.2156' fill='#222222' rx='6' ry='6' style='stroke:#222222;stroke-width:1.0;'/><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='250.6602' x='2388.2961' y='1786.4167'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='230.6602' x='2398.2961' y='1807.6726'>Handwerker-App sendet "abgelehnt" an BC</text><ellipse cx='2513.6262' cy='1851.2156' fill='none' rx='11' ry='11' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='2513.6262' cy='1851.2156' fill='#222222' rx='6' ry='6' style='stroke:#222222;stroke-width:1.0;'/><polygon fill='#F1F1F1' points='2156.262,1937.8132,2156.262,1937.8132,2168.262,1949.8132,2156.262,1961.8132,2156.262,1961.8132,2144.262,1949.8132,2156.262,1937.8132' style='stroke:#181818;stroke-width:0.5;'/><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='278.8145' x='2016.8547' y='1981.8132'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='258.8145' x='2026.8547' y='2003.0691'>BC sendet Kunden-Adresse an Handwerker-App</text><path d='M2300.3264,2032.5627 L2300.3264,2048.5115 L2280.3264,2052.5115 L2300.3264,2056.5115 L2300.3264,2072.4602 A0,0 0 0 0 2300.3264,2072.4602 L2882.0403,2072.4602 A0,0 0 0 0 2882.0403,2072.4602 L2882.0403,2042.5627 L2872.0403,2032.5627 L2300.3264,2032.5627 A0,0 0 0 0 2300.3264,2032.5627 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M2872.0403,2032.5627 L2872.0403,2042.5627 L2882.0403,2042.5627 L2872.0403,2032.5627 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='505.6289' x='2306.3264' y='2049.7566'>Wenn der Zeitpunkt "schnellstm&#246;glich" ist, wird die Adresse als n&#228;chstes Ziel angezeigt.</text><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='560.7139' x='2306.3264' y='2064.7053'>Ansonsten wird sie am Tag des Auftrags in einer Termin-&#220;bersicht zur passenden Zeit angezeigt.</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='248.1289' x='2032.1975' y='2035.6121'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='228.1289' x='2042.1975' y='2056.8679'>Handwerker-App zeigt Kunden-Adresse an</text><rect fill='#F0B4A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='343.4961' x='1984.5139' y='2092.4602'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='323.4961' x='1994.5139' y='2113.7161'>Handwerker-App zeigt Navigations-Link zur Kunden-Adresse</text><ellipse cx='2156.262' cy='2157.259' fill='none' rx='11' ry='11' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='2156.262' cy='2157.259' fill='#222222' rx='6' ry='6' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='2156.262' cy='2188.259' fill='#222222' rx='10' ry='10' style='stroke:#222222;stroke-width:1.0;'/><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='214.7598' x='2048.8821' y='2218.259'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='194.7598' x='2058.8821' y='2239.5149'>Handwerker kommt beim Kunden an</text><polygon fill='#F1F1F1' points='2111.937,2272.0579,2200.5869,2272.0579,2212.5869,2284.0579,2200.5869,2296.0579,2111.937,2296.0579,2099.937,2284.0579,2111.937,2272.0579' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='88.6499' x='2111.937' y='2288.0513'>ist Kunde vor Ort?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='8.5615' x='2091.3755' y='2281.7268'>ja</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='2212.5869' y='2281.7268'>nein</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='361.3672' x='1127.8716' y='2306.0579'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='341.3672' x='1137.8716' y='2327.3137'>Handwerker w&#228;hlt "Arbeit beginnen" in der Handwerker-App aus</text><path d='M1447.8706,2359.8567 L1447.8706,2383.2798 L1427.8706,2387.2798 L1447.8706,2391.2798 L1447.8706,2414.7029 A0,0 0 0 0 1447.8706,2414.7029 L1936.439,2414.7029 A0,0 0 0 0 1936.439,2414.7029 L1936.439,2369.8567 L1926.439,2359.8567 L1447.8706,2359.8567 A0,0 0 0 0 1447.8706,2359.8567 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M1926.439,2359.8567 L1926.439,2369.8567 L1936.439,2369.8567 L1926.439,2359.8567 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='456.6948' x='1453.8706' y='2377.0505'>Falls keine Internet-Verbindung besteht, wird die Meldung in der App zwischen-</text><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='343.313' x='1453.8706' y='2391.9993'>gespeichert und gesendet, sobald eine Verbindung besteht.</text><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='467.5684' x='1453.8706' y='2406.948'>Ausschalten des Smartphones oder Beenden der App, darf dies nicht verhindern.</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='238.6309' x='1189.2397' y='2370.3804'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='218.6309' x='1199.2397' y='2391.6362'>Handwerker-App sendet "checkin" an BC</text><path d='M963.9106,2454.1279 L963.9106,2479.0767 A0,0 0 0 0 963.9106,2479.0767 L1145.1636,2479.0767 A0,0 0 0 0 1145.1636,2479.0767 L1145.1636,2472.1279 L1165.1636,2466.6023 L1145.1636,2464.1279 L1145.1636,2464.1279 L1135.1636,2454.1279 L963.9106,2454.1279 A0,0 0 0 0 963.9106,2454.1279 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M1135.1636,2454.1279 L1135.1636,2464.1279 L1145.1636,2464.1279 L1135.1636,2454.1279 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='160.2529' x='969.9106' y='2471.3218'>was ist ein "Kleinstauftrag"?</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='286.7832' x='1165.1636' y='2449.7029'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='266.7832' x='1175.1636' y='2470.9587'>Handwerker sieht den Arbeitsort (betritt das Haus)</text><path d='M1146.5811,2518.5017 L1146.5811,2543.4504 L1205.1338,2543.4504 L1205.1338,2528.5017 L1195.1338,2518.5017 L1146.5811,2518.5017 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M1195.1338,2518.5017 L1195.1338,2528.5017 L1205.1338,2528.5017 L1195.1338,2518.5017 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='37.5527' x='1152.5811' y='2535.6956'>TODO</text><polygon fill='#F1F1F1' points='1217.1338,2543.4504,1399.9766,2543.4504,1411.9766,2555.4504,1399.9766,2567.4504,1217.1338,2567.4504,1205.1338,2555.4504,1217.1338,2543.4504' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='182.8428' x='1217.1338' y='2559.4438'>will Kunde einen Kostenvoranschlag?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='8.5615' x='1196.5723' y='2553.1194'>ja</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='1411.9766' y='2553.1194'>nein</text><polygon fill='#F1F1F1' points='1308.5552,2583.4504,1320.5552,2595.4504,1308.5552,2607.4504,1296.5552,2595.4504,1308.5552,2583.4504' style='stroke:#181818;stroke-width:0.5;'/><path d='M863.6956,2642.4504 L863.6956,2667.3992 L1254.6687,2667.3992 L1254.6687,2652.4504 L1244.6687,2642.4504 L863.6956,2642.4504 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M1244.6687,2642.4504 L1244.6687,2652.4504 L1254.6687,2652.4504 L1244.6687,2642.4504 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='369.9731' x='869.6956' y='2659.6443'>was ist, wenn der Einkauf / Bestellung mehrere Wochen dauert?</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='382.7012' x='1117.2046' y='2715.7236'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='362.7012' x='1127.2046' y='2736.9795'>Handwerker w&#228;hlt "Arbeit unterbrechen" in der Handwerker-App aus</text><path d='M854.6323,2784.2759 L854.6323,2809.2246 A0,0 0 0 0 854.6323,2809.2246 L1145.8901,2809.2246 A0,0 0 0 0 1145.8901,2809.2246 L1145.8901,2802.2759 L1165.8901,2796.7502 L1145.8901,2794.2759 L1145.8901,2794.2759 L1135.8901,2784.2759 L854.6323,2784.2759 A0,0 0 0 0 854.6323,2784.2759 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M1135.8901,2784.2759 L1135.8901,2794.2759 L1145.8901,2794.2759 L1135.8901,2784.2759 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='270.2578' x='860.6323' y='2801.4697'>wie wird die Einkaufszeit in Rechnung gestellt?</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='285.3301' x='1165.8901' y='2779.8508'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='265.3301' x='1175.8901' y='2801.1067'>Handwerker-App sendet "Arbeit pausieren" an BC</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='168.7461' x='1224.1821' y='2833.6497'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='148.7461' x='1234.1821' y='2854.9055'>Handwerker f&#228;hrt Einkaufen</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='356.6914' x='1130.2095' y='2887.4485'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='336.6914' x='1140.2095' y='2908.7043'>Handwerker w&#228;hlt "Einkauf t&#228;tigen" in der Handwerker-App aus</text><rect fill='#F0B4A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='482.1406' x='1067.4849' y='2941.2473'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='462.1406' x='1077.4849' y='2962.5032'>Handwerker-App zeigt Erinnerung "Denke daran f&#252;r jeden Kunde einzeln zu bezahlen!"</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='178.0684' x='1219.521' y='2995.0461'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='158.0684' x='1229.521' y='3016.302'>Handwerker bezahlt mit Karte</text><line style='stroke:#181818;stroke-width:1.5;' x1='1308.5552' x2='1794.6743' y1='3048.845' y2='3048.845'/><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='342.1602' x='1138.7222' y='3103.2444'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='322.1602' x='1148.7222' y='3124.5002'>Handwerker tr&#228;gt Betrag manuell in der Handwerker-App ein</text><path d='M608.3452,3176.4683 L608.3452,3201.417 A0,0 0 0 0 608.3452,3201.417 L1047.021,3201.417 A0,0 0 0 0 1047.021,3201.417 L1047.021,3194.4683 L1067.021,3188.9426 L1047.021,3186.4683 L1047.021,3186.4683 L1037.021,3176.4683 L608.3452,3176.4683 A0,0 0 0 0 608.3452,3176.4683 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M1037.021,3176.4683 L1037.021,3186.4683 L1047.021,3186.4683 L1037.021,3176.4683 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='417.6758' x='614.3452' y='3193.6621'>ist ein Foto/Nachweis rechtlich notwendig oder welche Optionen gibt es?</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='485.5625' x='1067.021' y='3172.0432'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='465.5625' x='1077.021' y='3193.2991'>Handwerker macht ein Foto vom Kassenzettel und w&#228;hlt es in der Handwerker-App aus</text><rect fill='#F0B4A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='254.1172' x='1667.6157' y='3068.845'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='234.1172' x='1677.6157' y='3090.1008'>Handwerker scannt den Kassenzettel (Foto)</text><rect fill='#F0B4A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='343.4902' x='1622.9292' y='3137.6438'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='323.4902' x='1632.9292' y='3158.8997'>Handwerker-App erfasst den Gesamtbetrag (Texterkennung)</text><rect fill='#F0B4A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='428.1816' x='1580.5835' y='3206.4426'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='408.1816' x='1590.5835' y='3227.6985'>Handwerker best&#228;tigt korrekt erfassten Gesamtbetrag oder korrigiert manuell</text><line style='stroke:#181818;stroke-width:1.5;' x1='1308.5552' x2='1794.6743' y1='3260.2415' y2='3260.2415'/><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='182.75' x='1217.1802' y='3280.2415'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='162.75' x='1227.1802' y='3301.4973'>Handwerker f&#228;hrt zum Kunden</text><polygon fill='#F1F1F1' points='1266.6687,2667.3992,1350.4417,2667.3992,1362.4417,2679.3992,1350.4417,2691.3992,1266.6687,2691.3992,1254.6687,2679.3992,1266.6687,2667.3992' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='8.5615' x='1312.5552' y='2701.717'>ja</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='83.7729' x='1266.6687' y='2683.3926'>ist Einkauf n&#246;tig?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='1362.4417' y='2677.0681'>nein</text><polygon fill='#F1F1F1' points='1308.5552,3334.0403,1320.5552,3346.0403,1308.5552,3358.0403,1296.5552,3346.0403,1308.5552,3334.0403' style='stroke:#181818;stroke-width:0.5;'/><path d='M929.3638,3382.4653 L929.3638,3407.4141 L1197.5034,3407.4141 L1197.5034,3392.4653 L1187.5034,3382.4653 L929.3638,3382.4653 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M1187.5034,3382.4653 L1187.5034,3392.4653 L1197.5034,3392.4653 L1187.5034,3382.4653 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='247.1396' x='935.3638' y='3399.6592'>kann noch mehrmals unterbrochen werden</text><path d='M1419.6069,3382.4653 L1419.6069,3407.4141 L1719.5229,3407.4141 L1719.5229,3392.4653 L1709.5229,3382.4653 L1419.6069,3382.4653 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M1709.5229,3382.4653 L1709.5229,3392.4653 L1719.5229,3392.4653 L1709.5229,3382.4653 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='278.916' x='1425.6069' y='3399.6592'>TODO: erneut einkaufen / mehrere Kassenzettel</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='202.1035' x='1207.5034' y='3378.0403'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='182.1035' x='1217.5034' y='3399.2961'>Handwerker arbeitet beim Kunden</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='354.0078' x='1131.5513' y='3437.4141'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='334.0078' x='1141.5513' y='3458.6699'>Handwerker w&#228;hlt in der Handwerker-App "Arbeit abschlie&#223;en"</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='309.4473' x='1153.8315' y='3491.2129'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='289.4473' x='1163.8315' y='3512.4688'>Handwerker macht Beweisfoto(s) vom Arbeitsergebnis</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='468.125' x='1074.4927' y='3545.0117'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='448.125' x='1084.4927' y='3566.2676'>Handwerker w&#228;hlt in der Handwerker-App "Best&#228;tigung durch den Kunden einholen"</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='254.7734' x='1181.1685' y='3598.8105'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='234.7734' x='1191.1685' y='3620.0664'>Handwerker-App zeigt Unterschrift-Panel an</text><path d='M713.0581,3657.0344 L713.0581,3681.9832 A0,0 0 0 0 713.0581,3681.9832 L1075.1177,3681.9832 A0,0 0 0 0 1075.1177,3681.9832 L1075.1177,3675.0344 L1095.1177,3669.5088 L1075.1177,3667.0344 L1075.1177,3667.0344 L1065.1177,3657.0344 L713.0581,3657.0344 A0,0 0 0 0 713.0581,3657.0344 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M1065.1177,3657.0344 L1065.1177,3667.0344 L1075.1177,3667.0344 L1065.1177,3657.0344 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='341.0596' x='719.0581' y='3674.2283'>was passiert mit dem Kunden, wenn er nicht unterschreibt?</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='426.875' x='1095.1177' y='3652.6094'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='406.875' x='1105.1177' y='3673.8652'>Kunde unterschreibt auf dem iPad des Handwerker (in der Handwerker-App)</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='358.0215' x='1129.5444' y='3706.4082'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='338.0215' x='1139.5444' y='3727.6641'>Handwerker w&#228;hlt in der Handwerker-App "Rechnung erstellen"</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='262.1152' x='1177.4976' y='3760.207'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='242.1152' x='1187.4976' y='3781.4629'>Handwerker-App sendet Beweisfoto(s) an BC</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='141.3887' x='1237.8608' y='3814.0059'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='121.3887' x='1247.8608' y='3835.2617'>BC speichert die Fotos</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='245.9727' x='1185.5688' y='3867.8047'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='225.9727' x='1195.5688' y='3889.0605'>Handwerker-App sendet "checkout" an BC</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='149.3984' x='1233.856' y='3921.6035'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='129.3984' x='1243.856' y='3942.8594'>Arbeitszeit endet offiziell</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='348.0371' x='1134.5366' y='3975.4023'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='328.0371' x='1144.5366' y='3996.6582'>BC speichert Auftrag als "abgeschlossen / noch nicht bezahlt"</text><path d='M294.8398,4033.6262 L294.8398,4058.575 A0,0 0 0 0 294.8398,4058.575 L1150.4868,4058.575 A0,0 0 0 0 1150.4868,4058.575 L1150.4868,4051.6262 L1170.4868,4046.1006 L1150.4868,4043.6262 L1150.4868,4043.6262 L1140.4868,4033.6262 L294.8398,4033.6262 A0,0 0 0 0 294.8398,4033.6262 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M1140.4868,4033.6262 L1140.4868,4043.6262 L1150.4868,4043.6262 L1140.4868,4033.6262 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='834.647' x='300.8398' y='4050.8201'>was soll passieren, wenn es beim Kunden keine Internet-Verbindung gibt? -&gt; Alternativ-Pfad: Rechnung sp&#228;ter stellen -&gt; Rechnung oder E-Mail?</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='276.1367' x='1170.4868' y='4029.2012'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='256.1367' x='1180.4868' y='4050.457'>BC berechnet Gesamtbetrag / erstellt Rechnung</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='265.4609' x='1175.8247' y='4083'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='245.4609' x='1185.8247' y='4104.2559'>BC sendet Gesamtbetrag an Handwerker-App</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='290.1523' x='1163.479' y='4136.7988'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='270.1523' x='1173.479' y='4158.0547'>Handwerker fragt, wie der Kunde bezahlen m&#246;chte</text><path d='M769.436,4195.0227 L769.436,4219.9714 A0,0 0 0 0 769.436,4219.9714 L1118.4829,4219.9714 A0,0 0 0 0 1118.4829,4219.9714 L1118.4829,4213.0227 L1138.4829,4207.4971 L1118.4829,4205.0227 L1118.4829,4205.0227 L1108.4829,4195.0227 L769.436,4195.0227 A0,0 0 0 0 769.436,4195.0227 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M1108.4829,4195.0227 L1108.4829,4205.0227 L1118.4829,4205.0227 L1108.4829,4195.0227 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='328.0469' x='775.436' y='4212.2166'>wie l&#228;uft die Rechnungsabwicklung mit der Kreditreform?</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='340.1445' x='1138.4829' y='4190.5977'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='320.1445' x='1148.4829' y='4211.8535'>Handwerker w&#228;hlt (Zahlungsart) in der Handwerker-App aus</text><polygon fill='#F1F1F1' points='1279.511,4244.3965,1337.5994,4244.3965,1349.5994,4256.3965,1337.5994,4268.3965,1279.511,4268.3965,1267.511,4256.3965,1279.511,4244.3965' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='58.0884' x='1279.511' y='4260.3899'>Zahlungsart</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='443.4453' x='318.7939' y='4303.6943'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='423.4453' x='328.7939' y='4324.9502'>Handwerker-App zeigt an "Bitte Kartenleser nutzen und danach hier best&#228;tigen"</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='172.0742' x='454.4795' y='4357.4932'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='152.0742' x='464.4795' y='4378.749'>Handwerker holt Kartenleser</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='302.8145' x='389.1094' y='4411.292'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='282.8145' x='399.1094' y='4432.5479'>Handwerker nutzt Kartenleser-App (Betrag eintippen)</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='165.418' x='457.8076' y='4480.0908'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='145.418' x='467.8076' y='4501.3467'>Kartenleser zeigt Betrag an</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='148.0742' x='466.4795' y='4548.8896'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='128.0742' x='476.4795' y='4570.1455'>Kunde bezahlt mit Karte</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='342.207' x='369.4131' y='4617.6885'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='322.207' x='379.4131' y='4638.9443'>Kartenleser sendet Zahlungsbest&#228;tigung an Kartenleser-App</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='369.5234' x='355.7549' y='4686.4873'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='349.5234' x='365.7549' y='4707.7432'>Handwerker best&#228;tigt die erfolgte Zahlung in der Handwerker-App</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='334.6953' x='373.1689' y='4755.2861'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='314.6953' x='383.1689' y='4776.542'>Handwerker-App sendet "Kartenzahlung erfolgreich" an BC</text><path d='M10,4814.3088 L10,4839.2576 A0,0 0 0 0 10,4839.2576 L386.5322,4839.2576 A0,0 0 0 0 386.5322,4839.2576 L386.5322,4832.3088 L406.5322,4826.7832 L386.5322,4824.3088 L386.5322,4824.3088 L376.5322,4814.3088 L10,4814.3088 A0,0 0 0 0 10,4814.3088 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><path d='M376.5322,4814.3088 L376.5322,4824.3088 L386.5322,4824.3088 L376.5322,4814.3088 ' fill='#FEFFDD' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='13' lengthAdjust='spacing' textLength='355.5322' x='16' y='4831.5027'>wie wird kontrolliert, ob das Geld tats&#228;chlich eingegangen ist?</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='267.9688' x='406.5322' y='4809.8838'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='247.9688' x='416.5322' y='4831.1396'>BC speichert Auftrag als "mit Karte bezahlt" ab</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='269.9844' x='405.5244' y='4864.4814'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='249.9844' x='415.5244' y='4885.7373'>Handwerker-App zeigt "Zahlung erfolgreich" an</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='283.3203' x='823.6094' y='4303.6943'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='263.3203' x='833.6094' y='4324.9502'>Handwerker-App sendet "pay with PayPal" an BC</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='366.0605' x='782.2393' y='4357.4932'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='346.0605' x='792.2393' y='4378.749'>BC sendet "pay with PayPal" an Kunden-App oder Kunden-Email</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='228.8164' x='850.8613' y='4411.292'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='208.8164' x='860.8613' y='4432.5479'>Kunde empf&#228;ngt Zahlungsaufforderung</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='288.8398' x='820.8496' y='4480.0908'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='268.8398' x='830.8496' y='4501.3467'>Kunde zahlt auf dem eigenen Endger&#228;t mit PayPal</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='248.8203' x='840.8594' y='4548.8896'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='228.8203' x='850.8594' y='4570.1455'>PayPal sendet Zahlungsbest&#228;tigung an BC</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='260.627' x='834.9561' y='4617.6885'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='240.627' x='844.9561' y='4638.9443'>BC speichert Auftrag als "mit PayPal bezahlt"</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='300.8398' x='814.8496' y='4686.4873'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='280.8398' x='824.8496' y='4707.7432'>BC sendet Zahlungsbest&#228;tigung an Handwerker-App</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='233.9668' x='848.2861' y='4755.2861'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='213.9668' x='858.2861' y='4776.542'>Handwerker-App zeigt "alles bezahlt" an</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='113.3984' x='1168.2998' y='4303.6943'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='93.3984' x='1178.2998' y='4324.9502'>analog zu PayPal</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='282.6523' x='1738.7944' y='4303.6943'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='262.6523' x='1748.7944' y='4324.9502'>Handwerker-App sendet "pay with invoice" an BC</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='237.4238' x='1761.4087' y='4357.4932'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='217.4238' x='1771.4087' y='4378.749'>BC pr&#252;ft, ob eine E-Mail-Adresse vorliegt</text><polygon fill='#F1F1F1' points='1833.3491,4411.292,1926.8921,4411.292,1938.8921,4423.292,1926.8921,4435.292,1833.3491,4435.292,1821.3491,4423.292,1833.3491,4411.292' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='93.543' x='1833.3491' y='4427.2854'>ist E-Mail bekannt?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='8.5615' x='1812.7876' y='4420.9609'>ja</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='1938.8921' y='4420.9609'>nein</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='277.3203' x='1408.0176' y='4445.292'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='257.3203' x='1418.0176' y='4466.5479'>BC sendet "email available" an Handwerker-App</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='469.959' x='1311.6982' y='4514.0908'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='449.959' x='1321.6982' y='4535.3467'>Handwerker-App zeigt Optionen "Rechnung per E-Mail" und "Rechnung per Post" an</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='269.4863' x='1411.9346' y='4582.8896'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='249.4863' x='1421.9346' y='4604.1455'>Handwerker fragt Kunden und w&#228;hlt die Option</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='234.7813' x='1429.2871' y='4651.6885'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='214.7813' x='1439.2871' y='4672.9443'>Handwerker-App sendet Auswahl an BC</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='277.9883' x='2074.5693' y='4445.292'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='257.9883' x='2084.5693' y='4466.5479'>BC sendet "email unknown" an Handwerker-App</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='308.6797' x='2059.2236' y='4514.0908'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='288.6797' x='2069.2236' y='4535.3467'>Handwerker-App zeigt Option "Rechnung per Post" an</text><rect fill='#A172B3' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='350.0176' x='2038.5547' y='4582.8896'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='330.0176' x='2048.5547' y='4604.1455'>Handwerker-App zeigt Option "E-Mail-Adresse hinzuf&#252;gen" an</text><rect fill='#A172B3' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='394.877' x='2016.125' y='4651.6885'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='374.877' x='2026.125' y='4672.9443'>Handwerker fragt, ob der Kunde eine E-Mail-Adresse angeben m&#246;chte</text><polygon fill='#F1F1F1' points='2135.6047,4720.4873,2291.5222,4720.4873,2303.5222,4732.4873,2291.5222,4744.4873,2135.6047,4744.4873,2123.6047,4732.4873,2135.6047,4720.4873' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='155.9175' x='2135.6047' y='4736.4807'>soll E-Mail hinzugef&#252;gt werden?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='8.5615' x='2115.0432' y='4730.1563'>ja</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='2303.5222' y='4730.1563'>nein</text><rect fill='#A172B3' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='294.6465' x='1867.377' y='4754.4873'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='274.6465' x='1877.377' y='4775.7432'>Handwerker w&#228;hlt "E-Mail-Adresse hinzuf&#252;gen" aus</text><rect fill='#A172B3' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='276.7988' x='1876.3008' y='4809.085'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='256.7988' x='1886.3008' y='4830.3408'>Handwerker-App zeigt Eingabefeld f&#252;r E-Mail an</text><rect fill='#A172B3' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='290.8145' x='1869.293' y='4863.6826'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='270.8145' x='1879.293' y='4884.9385'>Kunde gibt E-Mail auf Handwerker Smartphone ein</text><rect fill='#A172B3' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='406.0859' x='1811.6572' y='4918.2803'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='386.0859' x='1821.6572' y='4939.5361'>Handwerker dr&#252;ckt "absenden und f&#252;r Rechnungszustellung verwenden"</text><rect fill='#A172B3' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='265.3027' x='1882.0488' y='4972.0791'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='245.3027' x='1892.0488' y='4993.335'>Handwerker-App sendet "new email ..." an BC</text><rect fill='#A172B3' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='277.4609' x='1875.9697' y='5025.8779'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='257.4609' x='1885.9697' y='5047.1338'>BC f&#252;gt E-Mail-Adresse zum Kundenkonto hinzu</text><rect fill='#A172B3' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='310.666' x='1859.3672' y='5079.6768'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='290.666' x='1869.3672' y='5100.9326'>Handwerker-App sendet "Rechnung per E-Mail" an BC</text><rect fill='#A172B3' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='349.3672' x='2237.7432' y='4754.4873'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='329.3672' x='2247.7432' y='4775.7432'>Handwerker-App sendet Auswahl "Rechnung per Post" an BC</text><polygon fill='#F1F1F1' points='2213.5635,5119.4756,2225.5635,5131.4756,2213.5635,5143.4756,2201.5635,5131.4756,2213.5635,5119.4756' style='stroke:#181818;stroke-width:0.5;'/><polygon fill='#F1F1F1' points='1880.1206,5149.4756,1892.1206,5161.4756,1880.1206,5173.4756,1868.1206,5161.4756,1880.1206,5149.4756' style='stroke:#181818;stroke-width:0.5;'/><polygon fill='#F1F1F1' points='1852.3037,5193.4756,1907.9375,5193.4756,1919.9375,5205.4756,1907.9375,5217.4756,1852.3037,5217.4756,1840.3037,5205.4756,1852.3037,5193.4756' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='55.6338' x='1852.3037' y='5209.469'>ist Auswahl</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='40.9653' x='1799.3384' y='5203.1445'>per Post</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='50.123' x='1919.9375' y='5203.1445'>per E-Mail</text><rect fill='#A8F0B1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='246.7871' x='1622.8267' y='5227.4756'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='226.7871' x='1632.8267' y='5248.7314'>BC st&#246;&#223;t postalischen Rechnungsdruck an</text><rect fill='#F0E6A8' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='248.8145' x='1889.6138' y='5227.4756'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='228.8145' x='1899.6138' y='5248.7314'>BC sendet Rechnung an die Kunden-Email</text><polygon fill='#F1F1F1' points='1880.1206,5267.2744,1892.1206,5279.2744,1880.1206,5291.2744,1868.1206,5279.2744,1880.1206,5267.2744' style='stroke:#181818;stroke-width:0.5;'/><polygon fill='#F1F1F1' points='1308.5552,5301.2744,1308.5552,5301.2744,1320.5552,5313.2744,1308.5552,5325.2744,1308.5552,5325.2744,1296.5552,5313.2744,1308.5552,5301.2744' style='stroke:#181818;stroke-width:0.5;'/><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='430.2383' x='2788.8496' y='2306.0579'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='410.2383' x='2798.8496' y='2327.3137'>Handwerker meldet den Kunden in der Handwerker-App als nicht angetroffen</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='258.1367' x='2874.9004' y='2359.8567'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='238.1367' x='2884.9004' y='2381.1125'>Handwerker-App sendet Abwesenheit an BC</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='149.3984' x='2929.2695' y='2428.6555'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='129.3984' x='2939.2695' y='2449.9114'>BC speichert den Vorfall</text><polygon fill='#F1F1F1' points='2925.7146,2497.4543,3082.2229,2497.4543,3094.2229,2509.4543,3082.2229,2521.4543,2925.7146,2521.4543,2913.7146,2509.4543,2925.7146,2497.4543' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='156.5083' x='2925.7146' y='2513.4478'>ist der Grenzwert &#252;berschritten?</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='17.1177' x='2896.5969' y='2507.1233'>yes</text><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='20.7969' x='3094.2229' y='2507.1233'>nein</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='327.5059' x='2637.1104' y='2531.4543'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='307.5059' x='2647.1104' y='2552.7102'>Kunde wird in BC auf die rote Liste geschrieben / gesperrt</text><ellipse cx='2800.8633' cy='2611.2532' fill='none' rx='11' ry='11' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='2800.8633' cy='2611.2532' fill='#222222' rx='6' ry='6' style='stroke:#222222;stroke-width:1.0;'/><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='444.916' x='2984.6162' y='2531.4543'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='424.916' x='2994.6162' y='2552.7102'>BC sendet Warnung an Kunden-App, dass zu viele Vorf&#228;lle zur Sperrung f&#252;hren</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='194.7793' x='3109.6846' y='2600.2532'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='174.7793' x='3119.6846' y='2621.509'>Kunden-App zeigt Notification an</text><rect fill='#F1F1F1' height='33.7988' rx='12.5' ry='12.5' style='stroke:#181818;stroke-width:0.5;' width='271.4785' x='3071.335' y='2669.052'/><text fill='#000000' font-family='Arial' font-size='12' lengthAdjust='spacing' textLength='251.4785' x='3081.335' y='2690.3079'>Kunden-App zeigt Warnung in der &#220;bersicht an</text><ellipse cx='3207.0742' cy='2748.8508' fill='none' rx='11' ry='11' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='3207.0742' cy='2748.8508' fill='#222222' rx='6' ry='6' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='2156.262' cy='5356.2744' fill='none' rx='11' ry='11' style='stroke:#222222;stroke-width:1.0;'/><ellipse cx='2156.262' cy='5356.2744' fill='#222222' rx='6' ry='6' style='stroke:#222222;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='30' y2='50'/><polygon fill='#181818' points='2152.262,40,2156.262,50,2160.262,40,2156.262,44' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1875.842' x2='1875.842' y1='157.5977' y2='177.5977'/><polygon fill='#181818' points='1871.842,167.5977,1875.842,177.5977,1879.842,167.5977,1875.842,171.5977' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1875.842' x2='1875.842' y1='270.3965' y2='302.2144'/><polygon fill='#181818' points='1871.842,292.2144,1875.842,302.2144,1879.842,292.2144,1875.842,296.2144' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1938.5935' x2='1970.3904' y1='258.3965' y2='258.3965'/><polygon fill='#181818' points='1960.3904,254.3965,1970.3904,258.3965,1960.3904,262.3965,1964.3904,258.3965' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1875.842' x2='1875.842' y1='211.3965' y2='246.3965'/><polygon fill='#181818' points='1871.842,236.3965,1875.842,246.3965,1879.842,236.3965,1875.842,240.3965' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1875.842' x2='1875.842' y1='105.2988' y2='123.7988'/><polygon fill='#181818' points='1871.842,113.7988,1875.842,123.7988,1879.842,113.7988,1875.842,117.7988' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2213.4753' x2='2213.4753' y1='105.2988' y2='197.0227'/><polygon fill='#181818' points='2209.4753,187.0227,2213.4753,197.0227,2217.4753,187.0227,2213.4753,191.0227' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2213.4753' x2='2213.4753' y1='230.8215' y2='302.2144'/><polygon fill='#181818' points='2209.4753,292.2144,2213.4753,302.2144,2217.4753,292.2144,2213.4753,296.2144' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='83.7988' y2='103.7988'/><polygon fill='#181818' points='2152.262,93.7988,2156.262,103.7988,2160.262,93.7988,2156.262,97.7988' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='303.7144' y2='322.2144'/><polygon fill='#181818' points='2152.262,312.2144,2156.262,322.2144,2160.262,312.2144,2156.262,316.2144' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='356.0132' y2='376.0132'/><polygon fill='#181818' points='2152.262,366.0132,2156.262,376.0132,2160.262,366.0132,2156.262,370.0132' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='428.0132' y2='448.0132'/><polygon fill='#181818' points='2152.262,438.0132,2156.262,448.0132,2160.262,438.0132,2156.262,442.0132' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1464.8545' x2='1464.8545' y1='779.033' y2='799.033'/><polygon fill='#181818' points='1460.8545,789.033,1464.8545,799.033,1468.8545,789.033,1464.8545,793.033' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1742.3467' x2='1742.3467' y1='779.033' y2='799.033'/><polygon fill='#181818' points='1738.3467,789.033,1742.3467,799.033,1746.3467,789.033,1742.3467,793.033' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1742.3467' x2='1742.3467' y1='832.8318' y2='867.8318'/><polygon fill='#181818' points='1738.3467,857.8318,1742.3467,867.8318,1746.3467,857.8318,1742.3467,861.8318' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1742.3467' x2='1742.3467' y1='901.6306' y2='936.6306'/><polygon fill='#181818' points='1738.3467,926.6306,1742.3467,936.6306,1746.3467,926.6306,1742.3467,930.6306' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1520.3772' x2='1464.8545' y1='723.2341' y2='723.2341'/><line style='stroke:#181818;stroke-width:1.0;' x1='1464.8545' x2='1464.8545' y1='723.2341' y2='745.2341'/><polygon fill='#181818' points='1460.8545,735.2341,1464.8545,745.2341,1468.8545,735.2341,1464.8545,739.2341' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1686.824' x2='1742.3467' y1='723.2341' y2='723.2341'/><line style='stroke:#181818;stroke-width:1.0;' x1='1742.3467' x2='1742.3467' y1='723.2341' y2='745.2341'/><polygon fill='#181818' points='1738.3467,735.2341,1742.3467,745.2341,1746.3467,735.2341,1742.3467,739.2341' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1464.8545' x2='1464.8545' y1='832.8318' y2='988.4294'/><line style='stroke:#181818;stroke-width:1.0;' x1='1464.8545' x2='1591.6006' y1='988.4294' y2='988.4294'/><polygon fill='#181818' points='1581.6006,984.4294,1591.6006,988.4294,1581.6006,992.4294,1585.6006,988.4294' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1742.3467' x2='1742.3467' y1='970.4294' y2='988.4294'/><line style='stroke:#181818;stroke-width:1.0;' x1='1742.3467' x2='1615.6006' y1='988.4294' y2='988.4294'/><polygon fill='#181818' points='1625.6006,984.4294,1615.6006,988.4294,1625.6006,992.4294,1621.6006,988.4294' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1603.6006' x2='1603.6006' y1='676.2341' y2='711.2341'/><polygon fill='#181818' points='1599.6006,701.2341,1603.6006,711.2341,1607.6006,701.2341,1603.6006,705.2341' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1603.6006' x2='1603.6006' y1='1000.4294' y2='1035.4294'/><polygon fill='#181818' points='1599.6006,1025.4294,1603.6006,1035.4294,1607.6006,1025.4294,1603.6006,1029.4294' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1603.6006' x2='1603.6006' y1='1069.2283' y2='1104.2283'/><polygon fill='#181818' points='1599.6006,1094.2283,1603.6006,1104.2283,1607.6006,1094.2283,1603.6006,1098.2283' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1603.6006' x2='1603.6006' y1='1197.0271' y2='1384.3259'/><polygon fill='#181818' points='1599.6006,1374.3259,1603.6006,1384.3259,1607.6006,1374.3259,1603.6006,1378.3259' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1692.3455' x2='1724.1423' y1='1185.0271' y2='1185.0271'/><polygon fill='#181818' points='1714.1423,1181.0271,1724.1423,1185.0271,1714.1423,1189.0271,1718.1423,1185.0271' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1603.6006' x2='1603.6006' y1='1138.0271' y2='1173.0271'/><polygon fill='#181818' points='1599.6006,1163.0271,1603.6006,1173.0271,1607.6006,1163.0271,1603.6006,1167.0271' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2387.5848' x2='2387.5848' y1='523.312' y2='541.812'/><polygon fill='#181818' points='2383.5848,531.812,2387.5848,541.812,2391.5848,531.812,2387.5848,535.812' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2605.0321' x2='2605.0321' y1='523.312' y2='541.812'/><polygon fill='#181818' points='2601.0321,531.812,2605.0321,541.812,2609.0321,531.812,2605.0321,535.812' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2387.5848' x2='2387.5848' y1='575.6108' y2='595.6108'/><polygon fill='#181818' points='2383.5848,585.6108,2387.5848,595.6108,2391.5848,585.6108,2387.5848,589.6108' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2605.0321' x2='2605.0321' y1='575.6108' y2='595.6108'/><polygon fill='#181818' points='2601.0321,585.6108,2605.0321,595.6108,2609.0321,585.6108,2605.0321,589.6108' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2504.6493' x2='2504.6493' y1='597.1108' y2='615.6108'/><polygon fill='#181818' points='2500.6493,605.6108,2504.6493,615.6108,2508.6493,605.6108,2504.6493,609.6108' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2504.6493' x2='2504.6493' y1='649.4097' y2='684.4097'/><polygon fill='#181818' points='2500.6493,674.4097,2504.6493,684.4097,2508.6493,674.4097,2504.6493,678.4097' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2504.6493' x2='2504.6493' y1='777.2085' y2='845.2085'/><polygon fill='#181818' points='2500.6493,835.2085,2504.6493,845.2085,2508.6493,835.2085,2504.6493,839.2085' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2593.3942' x2='2625.191' y1='765.2085' y2='765.2085'/><polygon fill='#181818' points='2615.191,761.2085,2625.191,765.2085,2615.191,769.2085,2619.191,765.2085' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2504.6493' x2='2504.6493' y1='718.2085' y2='753.2085'/><polygon fill='#181818' points='2500.6493,743.2085,2504.6493,753.2085,2508.6493,743.2085,2504.6493,747.2085' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2054.8203' x2='2054.8203' y1='982.8318' y2='1017.8318'/><polygon fill='#181818' points='2050.8203,1007.8318,2054.8203,1017.8318,2058.8203,1007.8318,2054.8203,1011.8318' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2054.8203' x2='2054.8203' y1='1110.6306' y2='1179.2795'/><polygon fill='#181818' points='2050.8203,1169.2795,2054.8203,1179.2795,2058.8203,1169.2795,2054.8203,1173.2795' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2117.5718' x2='2149.3687' y1='1098.6306' y2='1098.6306'/><polygon fill='#181818' points='2139.3687,1094.6306,2149.3687,1098.6306,2139.3687,1102.6306,2143.3687,1098.6306' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2054.8203' x2='2054.8203' y1='1051.6306' y2='1086.6306'/><polygon fill='#181818' points='2050.8203,1076.6306,2054.8203,1086.6306,2058.8203,1076.6306,2054.8203,1080.6306' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2054.8203' x2='2054.8203' y1='929.033' y2='949.033'/><polygon fill='#181818' points='2050.8203,939.033,2054.8203,949.033,2058.8203,939.033,2054.8203,943.033' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2377.4565' x2='2377.4565' y1='929.033' y2='1037.2568'/><polygon fill='#181818' points='2373.4565,1027.2568,2377.4565,1037.2568,2381.4565,1027.2568,2377.4565,1031.2568' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2377.4565' x2='2377.4565' y1='1071.0557' y2='1179.2795'/><polygon fill='#181818' points='2373.4565,1169.2795,2377.4565,1179.2795,2381.4565,1169.2795,2377.4565,1173.2795' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2767.313' x2='2767.313' y1='961.3318' y2='996.3318'/><polygon fill='#181818' points='2763.313,986.3318,2767.313,996.3318,2771.313,986.3318,2767.313,990.3318' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2767.313' x2='2767.313' y1='1030.1306' y2='1065.1306'/><polygon fill='#181818' points='2763.313,1055.1306,2767.313,1065.1306,2771.313,1055.1306,2767.313,1059.1306' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2767.313' x2='2767.313' y1='1098.9294' y2='1133.9294'/><polygon fill='#181818' points='2763.313,1123.9294,2767.313,1133.9294,2771.313,1123.9294,2767.313,1127.9294' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2767.313' x2='2767.313' y1='1167.7283' y2='1202.7283'/><polygon fill='#181818' points='2763.313,1192.7283,2767.313,1202.7283,2771.313,1192.7283,2767.313,1196.7283' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2424.1732' x2='2241.9856' y1='905.533' y2='905.533'/><line style='stroke:#181818;stroke-width:1.0;' x1='2241.9856' x2='2241.9856' y1='905.533' y2='927.533'/><polygon fill='#181818' points='2237.9856,917.533,2241.9856,927.533,2245.9856,917.533,2241.9856,921.533' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2585.1254' x2='2767.313' y1='905.533' y2='905.533'/><line style='stroke:#181818;stroke-width:1.0;' x1='2767.313' x2='2767.313' y1='905.533' y2='927.533'/><polygon fill='#181818' points='2763.313,917.533,2767.313,927.533,2771.313,917.533,2767.313,921.533' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2241.9856' x2='2241.9856' y1='1180.7795' y2='1254.5271'/><line style='stroke:#181818;stroke-width:1.0;' x1='2241.9856' x2='2492.6493' y1='1254.5271' y2='1254.5271'/><polygon fill='#181818' points='2482.6493,1250.5271,2492.6493,1254.5271,2482.6493,1258.5271,2486.6493,1254.5271' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2767.313' x2='2767.313' y1='1236.5271' y2='1254.5271'/><line style='stroke:#181818;stroke-width:1.0;' x1='2767.313' x2='2516.6493' y1='1254.5271' y2='1254.5271'/><polygon fill='#181818' points='2526.6493,1250.5271,2516.6493,1254.5271,2526.6493,1258.5271,2522.6493,1254.5271' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2504.6493' x2='2504.6493' y1='869.2085' y2='893.533'/><polygon fill='#181818' points='2500.6493,883.533,2504.6493,893.533,2508.6493,883.533,2504.6493,887.533' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2590.9476' x2='2977.0815' y1='857.2085' y2='857.2085'/><polygon fill='#181818' points='2973.0815,1070.03,2977.0815,1080.03,2981.0815,1070.03,2977.0815,1074.03' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2977.0815' x2='2977.0815' y1='857.2085' y2='1298.5271'/><line style='stroke:#181818;stroke-width:1.0;' x1='2977.0815' x2='2516.6493' y1='1298.5271' y2='1298.5271'/><polygon fill='#181818' points='2526.6493,1294.5271,2516.6493,1298.5271,2526.6493,1302.5271,2522.6493,1298.5271' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2504.6493' x2='2504.6493' y1='1266.5271' y2='1286.5271'/><polygon fill='#181818' points='2500.6493,1276.5271,2504.6493,1286.5271,2508.6493,1276.5271,2504.6493,1280.5271' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2504.6493' x2='2504.6493' y1='1310.5271' y2='1330.5271'/><polygon fill='#181818' points='2500.6493,1320.5271,2504.6493,1330.5271,2508.6493,1320.5271,2504.6493,1324.5271' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1603.6006' x2='1603.6006' y1='503.312' y2='642.4353'/><polygon fill='#181818' points='1599.6006,632.4353,1603.6006,642.4353,1607.6006,632.4353,1603.6006,636.4353' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2504.6493' x2='2504.6493' y1='503.312' y2='521.812'/><polygon fill='#181818' points='2500.6493,511.812,2504.6493,521.812,2508.6493,511.812,2504.6493,515.812' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2504.6493' x2='2504.6493' y1='1364.3259' y2='1384.3259'/><polygon fill='#181818' points='2500.6493,1374.3259,2504.6493,1384.3259,2508.6493,1374.3259,2504.6493,1378.3259' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='481.812' y2='501.812'/><polygon fill='#181818' points='2152.262,491.812,2156.262,501.812,2160.262,491.812,2156.262,495.812' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='1385.8259' y2='1404.3259'/><polygon fill='#181818' points='2152.262,1394.3259,2156.262,1404.3259,2160.262,1394.3259,2156.262,1398.3259' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='1438.1248' y2='1458.1248'/><polygon fill='#181818' points='2152.262,1448.1248,2156.262,1458.1248,2160.262,1448.1248,2156.262,1452.1248' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='1491.9236' y2='1511.9236'/><polygon fill='#181818' points='2152.262,1501.9236,2156.262,1511.9236,2160.262,1501.9236,2156.262,1505.9236' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='1545.7224' y2='1565.7224'/><polygon fill='#181818' points='2152.262,1555.7224,2156.262,1565.7224,2160.262,1555.7224,2156.262,1559.7224' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='1599.5212' y2='1619.5212'/><polygon fill='#181818' points='2152.262,1609.5212,2156.262,1619.5212,2160.262,1609.5212,2156.262,1613.5212' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='1653.3201' y2='1673.3201'/><polygon fill='#181818' points='2152.262,1663.3201,2156.262,1673.3201,2160.262,1663.3201,2156.262,1667.3201' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1861.5959' x2='1861.5959' y1='1820.2156' y2='1840.2156'/><polygon fill='#181818' points='1857.5959,1830.2156,1861.5959,1840.2156,1865.5959,1830.2156,1861.5959,1834.2156' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1861.5959' x2='1861.5959' y1='1874.0144' y2='1894.0144'/><polygon fill='#181818' points='1857.5959,1884.0144,1861.5959,1894.0144,1865.5959,1884.0144,1861.5959,1888.0144' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2218.9602' x2='2218.9602' y1='1820.2156' y2='1840.2156'/><polygon fill='#181818' points='2214.9602,1830.2156,2218.9602,1840.2156,2222.9602,1830.2156,2218.9602,1834.2156' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2513.6262' x2='2513.6262' y1='1820.2156' y2='1840.2156'/><polygon fill='#181818' points='2509.6262,1830.2156,2513.6262,1840.2156,2517.6262,1830.2156,2513.6262,1834.2156' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2113.6897' x2='1861.5959' y1='1739.1189' y2='1739.1189'/><line style='stroke:#181818;stroke-width:1.0;' x1='1861.5959' x2='1861.5959' y1='1739.1189' y2='1786.4167'/><polygon fill='#181818' points='1857.5959,1776.4167,1861.5959,1786.4167,1865.5959,1776.4167,1861.5959,1780.4167' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='83.7568' x='1861.5959' y='1766.7612'>nimmt Auftrag an</text><line style='stroke:#181818;stroke-width:1.0;' x1='2198.8342' x2='2513.6262' y1='1739.1189' y2='1739.1189'/><line style='stroke:#181818;stroke-width:1.0;' x1='2513.6262' x2='2513.6262' y1='1739.1189' y2='1786.4167'/><polygon fill='#181818' points='2509.6262,1776.4167,2513.6262,1786.4167,2517.6262,1776.4167,2513.6262,1780.4167' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='77.666' x='2513.6262' y='1766.7612'>lehnt Auftrag ab</text><line style='stroke:#181818;stroke-width:1.0;' x1='2218.9602' x2='2218.9602' y1='1739.1189' y2='1786.4167'/><polygon fill='#181818' points='2214.9602,1776.4167,2218.9602,1786.4167,2222.9602,1776.4167,2218.9602,1780.4167' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='76.4307' x='2218.9602' y='1761.7612'>sieht es zu sp&#228;t</text><line style='stroke:#181818;stroke-width:1.0;' x1='1861.5959' x2='1861.5959' y1='1927.8132' y2='1949.8132'/><line style='stroke:#181818;stroke-width:1.0;' x1='1861.5959' x2='2144.262' y1='1949.8132' y2='1949.8132'/><polygon fill='#181818' points='2134.262,1945.8132,2144.262,1949.8132,2134.262,1953.8132,2138.262,1949.8132' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='1707.1189' y2='1727.1189'/><polygon fill='#181818' points='2152.262,1717.1189,2156.262,1727.1189,2160.262,1717.1189,2156.262,1721.1189' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='1961.8132' y2='1981.8132'/><polygon fill='#181818' points='2152.262,1971.8132,2156.262,1981.8132,2160.262,1971.8132,2156.262,1975.8132' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='2015.6121' y2='2035.6121'/><polygon fill='#181818' points='2152.262,2025.6121,2156.262,2035.6121,2160.262,2025.6121,2156.262,2029.6121' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='2069.4109' y2='2092.4602'/><polygon fill='#181818' points='2152.262,2082.4602,2156.262,2092.4602,2160.262,2082.4602,2156.262,2086.4602' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='2126.259' y2='2146.259'/><polygon fill='#181818' points='2152.262,2136.259,2156.262,2146.259,2160.262,2136.259,2156.262,2140.259' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='2198.259' y2='2218.259'/><polygon fill='#181818' points='2152.262,2208.259,2156.262,2218.259,2160.262,2208.259,2156.262,2212.259' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='2339.8567' y2='2370.3804'/><polygon fill='#181818' points='1304.5552,2360.3804,1308.5552,2370.3804,1312.5552,2360.3804,1308.5552,2364.3804' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='2404.1792' y2='2449.7029'/><polygon fill='#181818' points='1304.5552,2439.7029,1308.5552,2449.7029,1312.5552,2439.7029,1308.5552,2443.7029' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1205.1338' x2='1195.1338' y1='2555.4504' y2='2555.4504'/><polygon fill='#181818' points='1191.1338,2565.4504,1195.1338,2575.4504,1199.1338,2565.4504,1195.1338,2569.4504' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1195.1338' x2='1195.1338' y1='2555.4504' y2='2595.4504'/><line style='stroke:#181818;stroke-width:1.0;' x1='1195.1338' x2='1296.5552' y1='2595.4504' y2='2595.4504'/><polygon fill='#181818' points='1286.5552,2591.4504,1296.5552,2595.4504,1286.5552,2599.4504,1290.5552,2595.4504' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1411.9766' x2='1421.9766' y1='2555.4504' y2='2555.4504'/><polygon fill='#181818' points='1417.9766,2565.4504,1421.9766,2575.4504,1425.9766,2565.4504,1421.9766,2569.4504' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1421.9766' x2='1421.9766' y1='2555.4504' y2='2595.4504'/><line style='stroke:#181818;stroke-width:1.0;' x1='1421.9766' x2='1320.5552' y1='2595.4504' y2='2595.4504'/><polygon fill='#181818' points='1330.5552,2591.4504,1320.5552,2595.4504,1330.5552,2599.4504,1326.5552,2595.4504' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='2483.5017' y2='2543.4504'/><polygon fill='#181818' points='1304.5552,2533.4504,1308.5552,2543.4504,1312.5552,2533.4504,1308.5552,2537.4504' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='2749.5225' y2='2779.8508'/><polygon fill='#181818' points='1304.5552,2769.8508,1308.5552,2779.8508,1312.5552,2769.8508,1308.5552,2773.8508' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='2813.6497' y2='2833.6497'/><polygon fill='#181818' points='1304.5552,2823.6497,1308.5552,2833.6497,1312.5552,2823.6497,1308.5552,2827.6497' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='2867.4485' y2='2887.4485'/><polygon fill='#181818' points='1304.5552,2877.4485,1308.5552,2887.4485,1312.5552,2877.4485,1308.5552,2881.4485' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='2921.2473' y2='2941.2473'/><polygon fill='#181818' points='1304.5552,2931.2473,1308.5552,2941.2473,1312.5552,2931.2473,1308.5552,2935.2473' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='2975.0461' y2='2995.0461'/><polygon fill='#181818' points='1304.5552,2985.0461,1308.5552,2995.0461,1312.5552,2985.0461,1308.5552,2989.0461' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1309.8022' x2='1309.8022' y1='3137.0432' y2='3172.0432'/><polygon fill='#181818' points='1305.8022,3162.0432,1309.8022,3172.0432,1313.8022,3162.0432,1309.8022,3166.0432' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1794.6743' x2='1794.6743' y1='3102.6438' y2='3137.6438'/><polygon fill='#181818' points='1790.6743,3127.6438,1794.6743,3137.6438,1798.6743,3127.6438,1794.6743,3131.6438' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1794.6743' x2='1794.6743' y1='3171.4426' y2='3206.4426'/><polygon fill='#181818' points='1790.6743,3196.4426,1794.6743,3206.4426,1798.6743,3196.4426,1794.6743,3200.4426' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1309.8022' x2='1309.8022' y1='3050.345' y2='3103.2444'/><polygon fill='#181818' points='1305.8022,3093.2444,1309.8022,3103.2444,1313.8022,3093.2444,1309.8022,3097.2444' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1794.6743' x2='1794.6743' y1='3050.345' y2='3068.845'/><polygon fill='#181818' points='1790.6743,3058.845,1794.6743,3068.845,1798.6743,3058.845,1794.6743,3062.845' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1309.8022' x2='1309.8022' y1='3205.842' y2='3260.2415'/><polygon fill='#181818' points='1305.8022,3250.2415,1309.8022,3260.2415,1313.8022,3250.2415,1309.8022,3254.2415' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1794.6743' x2='1794.6743' y1='3240.2415' y2='3260.2415'/><polygon fill='#181818' points='1790.6743,3250.2415,1794.6743,3260.2415,1798.6743,3250.2415,1794.6743,3254.2415' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3028.845' y2='3048.845'/><polygon fill='#181818' points='1304.5552,3038.845,1308.5552,3048.845,1312.5552,3038.845,1308.5552,3042.845' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3261.7415' y2='3280.2415'/><polygon fill='#181818' points='1304.5552,3270.2415,1308.5552,3280.2415,1312.5552,3270.2415,1308.5552,3274.2415' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='2691.3992' y2='2715.7236'/><polygon fill='#181818' points='1304.5552,2705.7236,1308.5552,2715.7236,1312.5552,2705.7236,1308.5552,2709.7236' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1362.4417' x2='2032.7651' y1='2679.3992' y2='2679.3992'/><polygon fill='#181818' points='2028.7651,2989.0461,2032.7651,2999.0461,2036.7651,2989.0461,2032.7651,2993.0461' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2032.7651' x2='2032.7651' y1='2679.3992' y2='3346.0403'/><line style='stroke:#181818;stroke-width:1.0;' x1='2032.7651' x2='1320.5552' y1='3346.0403' y2='3346.0403'/><polygon fill='#181818' points='1330.5552,3342.0403,1320.5552,3346.0403,1330.5552,3350.0403,1326.5552,3346.0403' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3314.0403' y2='3334.0403'/><polygon fill='#181818' points='1304.5552,3324.0403,1308.5552,3334.0403,1312.5552,3324.0403,1308.5552,3328.0403' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='2607.4504' y2='2667.3992'/><polygon fill='#181818' points='1304.5552,2657.3992,1308.5552,2667.3992,1312.5552,2657.3992,1308.5552,2661.3992' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3358.0403' y2='3378.0403'/><polygon fill='#181818' points='1304.5552,3368.0403,1308.5552,3378.0403,1312.5552,3368.0403,1308.5552,3372.0403' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3411.8391' y2='3437.4141'/><polygon fill='#181818' points='1304.5552,3427.4141,1308.5552,3437.4141,1312.5552,3427.4141,1308.5552,3431.4141' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3471.2129' y2='3491.2129'/><polygon fill='#181818' points='1304.5552,3481.2129,1308.5552,3491.2129,1312.5552,3481.2129,1308.5552,3485.2129' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3525.0117' y2='3545.0117'/><polygon fill='#181818' points='1304.5552,3535.0117,1308.5552,3545.0117,1312.5552,3535.0117,1308.5552,3539.0117' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3578.8105' y2='3598.8105'/><polygon fill='#181818' points='1304.5552,3588.8105,1308.5552,3598.8105,1312.5552,3588.8105,1308.5552,3592.8105' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3632.6094' y2='3652.6094'/><polygon fill='#181818' points='1304.5552,3642.6094,1308.5552,3652.6094,1312.5552,3642.6094,1308.5552,3646.6094' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3686.4082' y2='3706.4082'/><polygon fill='#181818' points='1304.5552,3696.4082,1308.5552,3706.4082,1312.5552,3696.4082,1308.5552,3700.4082' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3740.207' y2='3760.207'/><polygon fill='#181818' points='1304.5552,3750.207,1308.5552,3760.207,1312.5552,3750.207,1308.5552,3754.207' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3794.0059' y2='3814.0059'/><polygon fill='#181818' points='1304.5552,3804.0059,1308.5552,3814.0059,1312.5552,3804.0059,1308.5552,3808.0059' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3847.8047' y2='3867.8047'/><polygon fill='#181818' points='1304.5552,3857.8047,1308.5552,3867.8047,1312.5552,3857.8047,1308.5552,3861.8047' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3901.6035' y2='3921.6035'/><polygon fill='#181818' points='1304.5552,3911.6035,1308.5552,3921.6035,1312.5552,3911.6035,1308.5552,3915.6035' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='3955.4023' y2='3975.4023'/><polygon fill='#181818' points='1304.5552,3965.4023,1308.5552,3975.4023,1312.5552,3965.4023,1308.5552,3969.4023' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='4009.2012' y2='4029.2012'/><polygon fill='#181818' points='1304.5552,4019.2012,1308.5552,4029.2012,1312.5552,4019.2012,1308.5552,4023.2012' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='4063' y2='4083'/><polygon fill='#181818' points='1304.5552,4073,1308.5552,4083,1312.5552,4073,1308.5552,4077' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='4116.7988' y2='4136.7988'/><polygon fill='#181818' points='1304.5552,4126.7988,1308.5552,4136.7988,1312.5552,4126.7988,1308.5552,4130.7988' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='4170.5977' y2='4190.5977'/><polygon fill='#181818' points='1304.5552,4180.5977,1308.5552,4190.5977,1312.5552,4180.5977,1308.5552,4184.5977' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='540.5166' y1='4337.4932' y2='4357.4932'/><polygon fill='#181818' points='536.5166,4347.4932,540.5166,4357.4932,544.5166,4347.4932,540.5166,4351.4932' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='540.5166' y1='4391.292' y2='4411.292'/><polygon fill='#181818' points='536.5166,4401.292,540.5166,4411.292,544.5166,4401.292,540.5166,4405.292' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='540.5166' y1='4445.0908' y2='4480.0908'/><polygon fill='#181818' points='536.5166,4470.0908,540.5166,4480.0908,544.5166,4470.0908,540.5166,4474.0908' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='540.5166' y1='4513.8896' y2='4548.8896'/><polygon fill='#181818' points='536.5166,4538.8896,540.5166,4548.8896,544.5166,4538.8896,540.5166,4542.8896' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='540.5166' y1='4582.6885' y2='4617.6885'/><polygon fill='#181818' points='536.5166,4607.6885,540.5166,4617.6885,544.5166,4607.6885,540.5166,4611.6885' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='540.5166' y1='4651.4873' y2='4686.4873'/><polygon fill='#181818' points='536.5166,4676.4873,540.5166,4686.4873,544.5166,4676.4873,540.5166,4680.4873' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='540.5166' y1='4720.2861' y2='4755.2861'/><polygon fill='#181818' points='536.5166,4745.2861,540.5166,4755.2861,544.5166,4745.2861,540.5166,4749.2861' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='540.5166' y1='4789.085' y2='4809.8838'/><polygon fill='#181818' points='536.5166,4799.8838,540.5166,4809.8838,544.5166,4799.8838,540.5166,4803.8838' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='540.5166' y1='4843.6826' y2='4864.4814'/><polygon fill='#181818' points='536.5166,4854.4814,540.5166,4864.4814,544.5166,4854.4814,540.5166,4858.4814' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='965.2695' x2='965.2695' y1='4337.4932' y2='4357.4932'/><polygon fill='#181818' points='961.2695,4347.4932,965.2695,4357.4932,969.2695,4347.4932,965.2695,4351.4932' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='965.2695' x2='965.2695' y1='4391.292' y2='4411.292'/><polygon fill='#181818' points='961.2695,4401.292,965.2695,4411.292,969.2695,4401.292,965.2695,4405.292' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='965.2695' x2='965.2695' y1='4445.0908' y2='4480.0908'/><polygon fill='#181818' points='961.2695,4470.0908,965.2695,4480.0908,969.2695,4470.0908,965.2695,4474.0908' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='965.2695' x2='965.2695' y1='4513.8896' y2='4548.8896'/><polygon fill='#181818' points='961.2695,4538.8896,965.2695,4548.8896,969.2695,4538.8896,965.2695,4542.8896' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='965.2695' x2='965.2695' y1='4582.6885' y2='4617.6885'/><polygon fill='#181818' points='961.2695,4607.6885,965.2695,4617.6885,969.2695,4607.6885,965.2695,4611.6885' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='965.2695' x2='965.2695' y1='4651.4873' y2='4686.4873'/><polygon fill='#181818' points='961.2695,4676.4873,965.2695,4686.4873,969.2695,4676.4873,965.2695,4680.4873' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='965.2695' x2='965.2695' y1='4720.2861' y2='4755.2861'/><polygon fill='#181818' points='961.2695,4745.2861,965.2695,4755.2861,969.2695,4745.2861,965.2695,4749.2861' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1880.1206' x2='1880.1206' y1='4337.4932' y2='4357.4932'/><polygon fill='#181818' points='1876.1206,4347.4932,1880.1206,4357.4932,1884.1206,4347.4932,1880.1206,4351.4932' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1546.6777' x2='1546.6777' y1='4479.0908' y2='4514.0908'/><polygon fill='#181818' points='1542.6777,4504.0908,1546.6777,4514.0908,1550.6777,4504.0908,1546.6777,4508.0908' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1546.6777' x2='1546.6777' y1='4547.8896' y2='4582.8896'/><polygon fill='#181818' points='1542.6777,4572.8896,1546.6777,4582.8896,1550.6777,4572.8896,1546.6777,4576.8896' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1546.6777' x2='1546.6777' y1='4616.6885' y2='4651.6885'/><polygon fill='#181818' points='1542.6777,4641.6885,1546.6777,4651.6885,1550.6777,4641.6885,1546.6777,4645.6885' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2213.5635' x2='2213.5635' y1='4479.0908' y2='4514.0908'/><polygon fill='#181818' points='2209.5635,4504.0908,2213.5635,4514.0908,2217.5635,4504.0908,2213.5635,4508.0908' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2213.5635' x2='2213.5635' y1='4547.8896' y2='4582.8896'/><polygon fill='#181818' points='2209.5635,4572.8896,2213.5635,4582.8896,2217.5635,4572.8896,2213.5635,4576.8896' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2213.5635' x2='2213.5635' y1='4616.6885' y2='4651.6885'/><polygon fill='#181818' points='2209.5635,4641.6885,2213.5635,4651.6885,2217.5635,4641.6885,2213.5635,4645.6885' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.7002' x2='2014.7002' y1='4788.2861' y2='4809.085'/><polygon fill='#181818' points='2010.7002,4799.085,2014.7002,4809.085,2018.7002,4799.085,2014.7002,4803.085' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.7002' x2='2014.7002' y1='4842.8838' y2='4863.6826'/><polygon fill='#181818' points='2010.7002,4853.6826,2014.7002,4863.6826,2018.7002,4853.6826,2014.7002,4857.6826' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.7002' x2='2014.7002' y1='4897.4814' y2='4918.2803'/><polygon fill='#181818' points='2010.7002,4908.2803,2014.7002,4918.2803,2018.7002,4908.2803,2014.7002,4912.2803' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.7002' x2='2014.7002' y1='4952.0791' y2='4972.0791'/><polygon fill='#181818' points='2010.7002,4962.0791,2014.7002,4972.0791,2018.7002,4962.0791,2014.7002,4966.0791' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.7002' x2='2014.7002' y1='5005.8779' y2='5025.8779'/><polygon fill='#181818' points='2010.7002,5015.8779,2014.7002,5025.8779,2018.7002,5015.8779,2014.7002,5019.8779' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.7002' x2='2014.7002' y1='5059.6768' y2='5079.6768'/><polygon fill='#181818' points='2010.7002,5069.6768,2014.7002,5079.6768,2018.7002,5069.6768,2014.7002,5073.6768' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2123.6047' x2='2014.7002' y1='4732.4873' y2='4732.4873'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.7002' x2='2014.7002' y1='4732.4873' y2='4754.4873'/><polygon fill='#181818' points='2010.7002,4744.4873,2014.7002,4754.4873,2018.7002,4744.4873,2014.7002,4748.4873' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2303.5222' x2='2412.4268' y1='4732.4873' y2='4732.4873'/><line style='stroke:#181818;stroke-width:1.0;' x1='2412.4268' x2='2412.4268' y1='4732.4873' y2='4754.4873'/><polygon fill='#181818' points='2408.4268,4744.4873,2412.4268,4754.4873,2416.4268,4744.4873,2412.4268,4748.4873' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.7002' x2='2014.7002' y1='5113.4756' y2='5131.4756'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.7002' x2='2201.5635' y1='5131.4756' y2='5131.4756'/><polygon fill='#181818' points='2191.5635,5127.4756,2201.5635,5131.4756,2191.5635,5135.4756,2195.5635,5131.4756' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2412.4268' x2='2412.4268' y1='4788.2861' y2='5131.4756'/><line style='stroke:#181818;stroke-width:1.0;' x1='2412.4268' x2='2225.5635' y1='5131.4756' y2='5131.4756'/><polygon fill='#181818' points='2235.5635,5127.4756,2225.5635,5131.4756,2235.5635,5135.4756,2231.5635,5131.4756' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2213.5635' x2='2213.5635' y1='4685.4873' y2='4720.4873'/><polygon fill='#181818' points='2209.5635,4710.4873,2213.5635,4720.4873,2217.5635,4710.4873,2213.5635,4714.4873' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1821.3491' x2='1546.6777' y1='4423.292' y2='4423.292'/><line style='stroke:#181818;stroke-width:1.0;' x1='1546.6777' x2='1546.6777' y1='4423.292' y2='4445.292'/><polygon fill='#181818' points='1542.6777,4435.292,1546.6777,4445.292,1550.6777,4435.292,1546.6777,4439.292' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1938.8921' x2='2213.5635' y1='4423.292' y2='4423.292'/><line style='stroke:#181818;stroke-width:1.0;' x1='2213.5635' x2='2213.5635' y1='4423.292' y2='4445.292'/><polygon fill='#181818' points='2209.5635,4435.292,2213.5635,4445.292,2217.5635,4435.292,2213.5635,4439.292' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1546.6777' x2='1546.6777' y1='4685.4873' y2='5161.4756'/><line style='stroke:#181818;stroke-width:1.0;' x1='1546.6777' x2='1868.1206' y1='5161.4756' y2='5161.4756'/><polygon fill='#181818' points='1858.1206,5157.4756,1868.1206,5161.4756,1858.1206,5165.4756,1862.1206,5161.4756' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2213.5635' x2='2213.5635' y1='5143.4756' y2='5161.4756'/><line style='stroke:#181818;stroke-width:1.0;' x1='2213.5635' x2='1892.1206' y1='5161.4756' y2='5161.4756'/><polygon fill='#181818' points='1902.1206,5157.4756,1892.1206,5161.4756,1902.1206,5165.4756,1898.1206,5161.4756' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1880.1206' x2='1880.1206' y1='4391.292' y2='4411.292'/><polygon fill='#181818' points='1876.1206,4401.292,1880.1206,4411.292,1884.1206,4401.292,1880.1206,4405.292' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1840.3037' x2='1746.2202' y1='5205.4756' y2='5205.4756'/><line style='stroke:#181818;stroke-width:1.0;' x1='1746.2202' x2='1746.2202' y1='5205.4756' y2='5227.4756'/><polygon fill='#181818' points='1742.2202,5217.4756,1746.2202,5227.4756,1750.2202,5217.4756,1746.2202,5221.4756' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1919.9375' x2='2014.021' y1='5205.4756' y2='5205.4756'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.021' x2='2014.021' y1='5205.4756' y2='5227.4756'/><polygon fill='#181818' points='2010.021,5217.4756,2014.021,5227.4756,2018.021,5217.4756,2014.021,5221.4756' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1746.2202' x2='1746.2202' y1='5261.2744' y2='5279.2744'/><line style='stroke:#181818;stroke-width:1.0;' x1='1746.2202' x2='1868.1206' y1='5279.2744' y2='5279.2744'/><polygon fill='#181818' points='1858.1206,5275.2744,1868.1206,5279.2744,1858.1206,5283.2744,1862.1206,5279.2744' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.021' x2='2014.021' y1='5261.2744' y2='5279.2744'/><line style='stroke:#181818;stroke-width:1.0;' x1='2014.021' x2='1892.1206' y1='5279.2744' y2='5279.2744'/><polygon fill='#181818' points='1902.1206,5275.2744,1892.1206,5279.2744,1902.1206,5283.2744,1898.1206,5279.2744' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1880.1206' x2='1880.1206' y1='5173.4756' y2='5193.4756'/><polygon fill='#181818' points='1876.1206,5183.4756,1880.1206,5193.4756,1884.1206,5183.4756,1880.1206,5187.4756' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1267.511' x2='540.5166' y1='4256.3965' y2='4256.3965'/><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='540.5166' y1='4256.3965' y2='4303.6943'/><polygon fill='#181818' points='536.5166,4293.6943,540.5166,4303.6943,544.5166,4293.6943,540.5166,4297.6943' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='26.2915' x='540.5166' y='4284.0388'>Karte</text><line style='stroke:#181818;stroke-width:1.0;' x1='1349.5994' x2='1880.1206' y1='4256.3965' y2='4256.3965'/><line style='stroke:#181818;stroke-width:1.0;' x1='1880.1206' x2='1880.1206' y1='4256.3965' y2='4303.6943'/><polygon fill='#181818' points='1876.1206,4293.6943,1880.1206,4303.6943,1884.1206,4293.6943,1880.1206,4297.6943' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='50.1499' x='1880.1206' y='4284.0388'>Rechnung</text><line style='stroke:#181818;stroke-width:1.0;' x1='965.2695' x2='965.2695' y1='4256.3965' y2='4303.6943'/><polygon fill='#181818' points='961.2695,4293.6943,965.2695,4303.6943,969.2695,4293.6943,965.2695,4297.6943' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='34.853' x='965.2695' y='4279.0388'>PayPal</text><line style='stroke:#181818;stroke-width:1.0;' x1='1224.999' x2='1224.999' y1='4256.3965' y2='4303.6943'/><polygon fill='#181818' points='1220.999,4293.6943,1224.999,4303.6943,1228.999,4293.6943,1224.999,4297.6943' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='Arial' font-size='11' lengthAdjust='spacing' textLength='31.7969' x='1224.999' y='4279.0388'>Klarna</text><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='540.5166' y1='4898.2803' y2='5313.2744'/><line style='stroke:#181818;stroke-width:1.0;' x1='540.5166' x2='1296.5552' y1='5313.2744' y2='5313.2744'/><polygon fill='#181818' points='1286.5552,5309.2744,1296.5552,5313.2744,1286.5552,5317.2744,1290.5552,5313.2744' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1880.1206' x2='1880.1206' y1='5291.2744' y2='5313.2744'/><line style='stroke:#181818;stroke-width:1.0;' x1='1880.1206' x2='1320.5552' y1='5313.2744' y2='5313.2744'/><polygon fill='#181818' points='1330.5552,5309.2744,1320.5552,5313.2744,1330.5552,5317.2744,1326.5552,5313.2744' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='965.2695' x2='965.2695' y1='4789.085' y2='5313.2744'/><polygon fill='#181818' points='961.2695,5303.2744,965.2695,5313.2744,969.2695,5303.2744,965.2695,5307.2744' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1224.999' x2='1224.999' y1='4337.4932' y2='5313.2744'/><polygon fill='#181818' points='1220.999,5303.2744,1224.999,5313.2744,1228.999,5303.2744,1224.999,5307.2744' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='4224.3965' y2='4244.3965'/><polygon fill='#181818' points='1304.5552,4234.3965,1308.5552,4244.3965,1312.5552,4234.3965,1308.5552,4238.3965' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='3003.9688' x2='3003.9688' y1='2339.8567' y2='2359.8567'/><polygon fill='#181818' points='2999.9688,2349.8567,3003.9688,2359.8567,3007.9688,2349.8567,3003.9688,2353.8567' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='3003.9688' x2='3003.9688' y1='2393.6555' y2='2428.6555'/><polygon fill='#181818' points='2999.9688,2418.6555,3003.9688,2428.6555,3007.9688,2418.6555,3003.9688,2422.6555' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2800.8633' x2='2800.8633' y1='2565.2532' y2='2600.2532'/><polygon fill='#181818' points='2796.8633,2590.2532,2800.8633,2600.2532,2804.8633,2590.2532,2800.8633,2594.2532' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='3207.0742' x2='3207.0742' y1='2565.2532' y2='2600.2532'/><polygon fill='#181818' points='3203.0742,2590.2532,3207.0742,2600.2532,3211.0742,2590.2532,3207.0742,2594.2532' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='3207.0742' x2='3207.0742' y1='2634.052' y2='2669.052'/><polygon fill='#181818' points='3203.0742,2659.052,3207.0742,2669.052,3211.0742,2659.052,3207.0742,2663.052' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='3207.0742' x2='3207.0742' y1='2702.8508' y2='2737.8508'/><polygon fill='#181818' points='3203.0742,2727.8508,3207.0742,2737.8508,3211.0742,2727.8508,3207.0742,2731.8508' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2913.7146' x2='2800.8633' y1='2509.4543' y2='2509.4543'/><line style='stroke:#181818;stroke-width:1.0;' x1='2800.8633' x2='2800.8633' y1='2509.4543' y2='2531.4543'/><polygon fill='#181818' points='2796.8633,2521.4543,2800.8633,2531.4543,2804.8633,2521.4543,2800.8633,2525.4543' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='3094.2229' x2='3207.0742' y1='2509.4543' y2='2509.4543'/><line style='stroke:#181818;stroke-width:1.0;' x1='3207.0742' x2='3207.0742' y1='2509.4543' y2='2531.4543'/><polygon fill='#181818' points='3203.0742,2521.4543,3207.0742,2531.4543,3211.0742,2521.4543,3207.0742,2525.4543' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='3003.9688' x2='3003.9688' y1='2462.4543' y2='2497.4543'/><polygon fill='#181818' points='2999.9688,2487.4543,3003.9688,2497.4543,3007.9688,2487.4543,3003.9688,2491.4543' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2099.937' x2='1308.5552' y1='2284.0579' y2='2284.0579'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='2284.0579' y2='2306.0579'/><polygon fill='#181818' points='1304.5552,2296.0579,1308.5552,2306.0579,1312.5552,2296.0579,1308.5552,2300.0579' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2212.5869' x2='3003.9688' y1='2284.0579' y2='2284.0579'/><line style='stroke:#181818;stroke-width:1.0;' x1='3003.9688' x2='3003.9688' y1='2284.0579' y2='2306.0579'/><polygon fill='#181818' points='2999.9688,2296.0579,3003.9688,2306.0579,3007.9688,2296.0579,3003.9688,2300.0579' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='1308.5552' y1='5325.2744' y2='5330.2744'/><line style='stroke:#181818;stroke-width:1.0;' x1='1308.5552' x2='2156.262' y1='5330.2744' y2='5330.2744'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='5330.2744' y2='5345.2744'/><polygon fill='#181818' points='2152.262,5335.2744,2156.262,5345.2744,2160.262,5335.2744,2156.262,5339.2744' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='2156.262' x2='2156.262' y1='2252.0579' y2='2272.0579'/><polygon fill='#181818' points='2152.262,2262.0579,2156.262,2272.0579,2160.262,2262.0579,2156.262,2266.0579' style='stroke:#181818;stroke-width:1.0;'/></g></svg>