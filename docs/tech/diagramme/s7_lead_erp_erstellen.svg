<svg contentStyleType='text/css' height='949px' preserveAspectRatio='none' style='width:812px;height:949px;background:#FFFFFF;' version='1.1' viewBox='0 0 812 949' width='812px' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns='http://www.w3.org/2000/svg' zoomAndPan='magnify'><defs/><g><line style='stroke:#181818;stroke-width:0.5;stroke-dasharray:5.0,5.0;' x1='29' x2='29' y1='84.0679' y2='865.4822'/><line style='stroke:#181818;stroke-width:0.5;stroke-dasharray:5.0,5.0;' x1='149.7532' x2='149.7532' y1='84.0679' y2='865.4822'/><line style='stroke:#181818;stroke-width:0.5;stroke-dasharray:5.0,5.0;' x1='513.7375' x2='513.7375' y1='84.0679' y2='865.4822'/><text fill='#000000' font-family='sans-serif' font-size='14' lengthAdjust='spacing' textLength='42.4758' x='5' y='79.9659'>Kunde</text><ellipse cx='29.2379' cy='13.5' fill='#E2E2F0' rx='8' ry='8' style='stroke:#181818;stroke-width:0.5;'/><path d='M29.2379,21.5 L29.2379,48.5 M16.2379,29.5 L42.2379,29.5 M29.2379,48.5 L16.2379,63.5 M29.2379,48.5 L42.2379,63.5 ' fill='none' style='stroke:#181818;stroke-width:0.5;'/><text fill='#000000' font-family='sans-serif' font-size='14' lengthAdjust='spacing' textLength='42.4758' x='5' y='879.4482'>Kunde</text><ellipse cx='29.2379' cy='892.0501' fill='#E2E2F0' rx='8' ry='8' style='stroke:#181818;stroke-width:0.5;'/><path d='M29.2379,900.0501 L29.2379,927.0501 M16.2379,908.0501 L42.2379,908.0501 M29.2379,927.0501 L16.2379,942.0501 M29.2379,927.0501 L42.2379,942.0501 ' fill='none' style='stroke:#181818;stroke-width:0.5;'/><rect fill='#E2E2F0' height='33.0679' rx='2.5' ry='2.5' style='stroke:#181818;stroke-width:0.5;' width='123.2416' x='88.7532' y='50'/><text fill='#000000' font-family='sans-serif' font-size='14' lengthAdjust='spacing' textLength='109.2416' x='95.7532' y='71.9659'>BC Hauptinstanz</text><rect fill='#E2E2F0' height='33.0679' rx='2.5' ry='2.5' style='stroke:#181818;stroke-width:0.5;' width='123.2416' x='88.7532' y='864.4822'/><text fill='#000000' font-family='sans-serif' font-size='14' lengthAdjust='spacing' textLength='109.2416' x='95.7532' y='886.4482'>BC Hauptinstanz</text><rect fill='#E2E2F0' height='33.0679' rx='2.5' ry='2.5' style='stroke:#181818;stroke-width:0.5;' width='164.0234' x='431.7375' y='50'/><text fill='#000000' font-family='sans-serif' font-size='14' lengthAdjust='spacing' textLength='150.0234' x='438.7375' y='71.9659'>BC Handwerkerinstanz</text><rect fill='#E2E2F0' height='33.0679' rx='2.5' ry='2.5' style='stroke:#181818;stroke-width:0.5;' width='164.0234' x='431.7375' y='864.4822'/><text fill='#000000' font-family='sans-serif' font-size='14' lengthAdjust='spacing' textLength='150.0234' x='438.7375' y='886.4482'>BC Handwerkerinstanz</text><polygon fill='#181818' points='138.374,113.7739,148.374,117.7739,138.374,121.7739,142.374,117.7739' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='29.2379' x2='144.374' y1='117.7739' y2='117.7739'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='97.1361' x='36.2379' y='111.9649'>Anfrage senden</text><line style='stroke:#181818;stroke-width:1.0;' x1='150.374' x2='192.374' y1='149.4799' y2='149.4799'/><line style='stroke:#181818;stroke-width:1.0;' x1='192.374' x2='192.374' y1='149.4799' y2='162.4799'/><line style='stroke:#181818;stroke-width:1.0;' x1='151.374' x2='192.374' y1='162.4799' y2='162.4799'/><polygon fill='#181818' points='161.374,158.4799,151.374,162.4799,161.374,166.4799,157.374,162.4799' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='114.7251' x='157.374' y='143.6709'>Kunde gespeichert</text><line style='stroke:#181818;stroke-width:1.0;' x1='150.374' x2='192.374' y1='194.186' y2='194.186'/><line style='stroke:#181818;stroke-width:1.0;' x1='192.374' x2='192.374' y1='194.186' y2='207.186'/><line style='stroke:#181818;stroke-width:1.0;' x1='151.374' x2='192.374' y1='207.186' y2='207.186'/><polygon fill='#181818' points='161.374,203.186,151.374,207.186,161.374,211.186,157.374,207.186' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='349.3752' x='157.374' y='188.377'>Sales Quote gespeichert (als Repr&#228;sentation der Anfrage)</text><line style='stroke:#181818;stroke-width:1.0;' x1='150.374' x2='192.374' y1='238.892' y2='238.892'/><line style='stroke:#181818;stroke-width:1.0;' x1='192.374' x2='192.374' y1='238.892' y2='251.892'/><line style='stroke:#181818;stroke-width:1.0;' x1='151.374' x2='192.374' y1='251.892' y2='251.892'/><polygon fill='#181818' points='161.374,247.892,151.374,251.892,161.374,255.892,157.374,251.892' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='170.7812' x='157.374' y='233.083'>Bonit&#228;tspr&#252;fung findet statt</text><rect fill='#EEEEEE' height='3' style='stroke:#EEEEEE;stroke-width:1.0;' width='805.6895' x='0' y='281.745'/><line style='stroke:#000000;stroke-width:1.0;' x1='0' x2='805.6895' y1='281.745' y2='281.745'/><line style='stroke:#000000;stroke-width:1.0;' x1='0' x2='805.6895' y1='284.745' y2='284.745'/><rect fill='#EEEEEE' height='25.706' style='stroke:#000000;stroke-width:2.0;' width='165.1381' x='320.2757' y='269.892'/><text fill='#000000' font-family='sans-serif' font-size='13' font-weight='bold' lengthAdjust='spacing' textLength='147.7581' x='326.2757' y='287.789'>Handwerker nimmt an</text><polygon fill='#181818' points='501.7492,325.304,511.7492,329.304,501.7492,333.304,505.7492,329.304' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='150.374' x2='507.7492' y1='329.304' y2='329.304'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='96.8501' x='157.374' y='323.495'>Kunde angelegt</text><polygon fill='#181818' points='501.7492,357.01,511.7492,361.01,501.7492,365.01,505.7492,361.01' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='150.374' x2='507.7492' y1='361.01' y2='361.01'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='229.4632' x='157.374' y='355.201'>Sales Order aus Sales Quote angelegt</text><line style='stroke:#181818;stroke-width:1.0;' x1='513.7492' x2='555.7492' y1='392.716' y2='392.716'/><line style='stroke:#181818;stroke-width:1.0;' x1='555.7492' x2='555.7492' y1='392.716' y2='405.716'/><line style='stroke:#181818;stroke-width:1.0;' x1='514.7492' x2='555.7492' y1='405.716' y2='405.716'/><polygon fill='#181818' points='524.7492,401.716,514.7492,405.716,524.7492,409.716,520.7492,405.716' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='119.5611' x='520.7492' y='386.907'>F&#252;gt Inventar hinzu</text><line style='stroke:#181818;stroke-width:1.0;' x1='513.7492' x2='555.7492' y1='437.4221' y2='437.4221'/><line style='stroke:#181818;stroke-width:1.0;' x1='555.7492' x2='555.7492' y1='437.4221' y2='450.4221'/><line style='stroke:#181818;stroke-width:1.0;' x1='514.7492' x2='555.7492' y1='450.4221' y2='450.4221'/><polygon fill='#181818' points='524.7492,446.4221,514.7492,450.4221,524.7492,454.4221,520.7492,450.4221' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='122.8111' x='520.7492' y='431.6131'>Schlie&#223;t Arbeiten ab</text><rect fill='#EEEEEE' height='3' style='stroke:#EEEEEE;stroke-width:1.0;' width='805.6895' x='0' y='480.2751'/><line style='stroke:#000000;stroke-width:1.0;' x1='0' x2='805.6895' y1='480.2751' y2='480.2751'/><line style='stroke:#000000;stroke-width:1.0;' x1='0' x2='805.6895' y1='483.2751' y2='483.2751'/><rect fill='#EEEEEE' height='25.706' style='stroke:#000000;stroke-width:2.0;' width='155.7911' x='324.9492' y='468.4221'/><text fill='#000000' font-family='sans-serif' font-size='13' font-weight='bold' lengthAdjust='spacing' textLength='138.4111' x='330.9492' y='486.3191'>Rechnungserstellung</text><line style='stroke:#181818;stroke-width:1.0;' x1='513.7492' x2='555.7492' y1='527.8341' y2='527.8341'/><line style='stroke:#181818;stroke-width:1.0;' x1='555.7492' x2='555.7492' y1='527.8341' y2='540.8341'/><line style='stroke:#181818;stroke-width:1.0;' x1='514.7492' x2='555.7492' y1='540.8341' y2='540.8341'/><polygon fill='#181818' points='524.7492,536.8341,514.7492,540.8341,524.7492,544.8341,520.7492,540.8341' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='277.9402' x='520.7492' y='522.0251'>Sales Invoice wird aus der Sales Order erstellt</text><polygon fill='#181818' points='40.2379,568.5401,30.2379,572.5401,40.2379,576.5401,36.2379,572.5401' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='34.2379' x2='512.7492' y1='572.5401' y2='572.5401'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='152.5422' x='46.2379' y='566.7311'>Rechnung wird geschickt</text><rect fill='#EEEEEE' height='3' style='stroke:#EEEEEE;stroke-width:1.0;' width='805.6895' x='0' y='602.3931'/><line style='stroke:#000000;stroke-width:1.0;' x1='0' x2='805.6895' y1='602.3931' y2='602.3931'/><line style='stroke:#000000;stroke-width:1.0;' x1='0' x2='805.6895' y1='605.3931' y2='605.3931'/><rect fill='#EEEEEE' height='25.706' style='stroke:#000000;stroke-width:2.0;' width='85.812' x='359.9387' y='590.5401'/><text fill='#000000' font-family='sans-serif' font-size='13' font-weight='bold' lengthAdjust='spacing' textLength='68.432' x='365.9387' y='608.4371'>Bezahlung</text><polygon fill='#181818' points='501.7492,645.9521,511.7492,649.9521,501.7492,653.9521,505.7492,649.9521' style='stroke:#181818;stroke-width:1.0;'/><line style='stroke:#181818;stroke-width:1.0;' x1='29.2379' x2='507.7492' y1='649.9521' y2='649.9521'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='120.6531' x='36.2379' y='644.1431'>Rechnung bezahlen</text><line style='stroke:#181818;stroke-width:1.0;' x1='513.7492' x2='555.7492' y1='681.6582' y2='681.6582'/><line style='stroke:#181818;stroke-width:1.0;' x1='555.7492' x2='555.7492' y1='681.6582' y2='694.6582'/><line style='stroke:#181818;stroke-width:1.0;' x1='514.7492' x2='555.7492' y1='694.6582' y2='694.6582'/><polygon fill='#181818' points='524.7492,690.6582,514.7492,694.6582,524.7492,698.6582,520.7492,694.6582' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='265.8502' x='520.7492' y='675.8492'>Rechnung wird der Sales Order zugewiesen</text><line style='stroke:#181818;stroke-width:1.0;' x1='513.7492' x2='555.7492' y1='726.3642' y2='726.3642'/><line style='stroke:#181818;stroke-width:1.0;' x1='555.7492' x2='555.7492' y1='726.3642' y2='739.3642'/><line style='stroke:#181818;stroke-width:1.0;' x1='514.7492' x2='555.7492' y1='739.3642' y2='739.3642'/><polygon fill='#181818' points='524.7492,735.3642,514.7492,739.3642,524.7492,743.3642,520.7492,739.3642' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='179.5042' x='520.7492' y='720.5552'>Sales Order wird geschlossen</text><rect fill='#EEEEEE' height='3' style='stroke:#EEEEEE;stroke-width:1.0;' width='805.6895' x='0' y='769.2172'/><line style='stroke:#000000;stroke-width:1.0;' x1='0' x2='805.6895' y1='769.2172' y2='769.2172'/><line style='stroke:#000000;stroke-width:1.0;' x1='0' x2='805.6895' y1='772.2172' y2='772.2172'/><rect fill='#EEEEEE' height='25.706' style='stroke:#000000;stroke-width:2.0;' width='214.0181' x='295.8357' y='757.3642'/><text fill='#000000' font-family='sans-serif' font-size='13' font-weight='bold' lengthAdjust='spacing' textLength='196.6381' x='301.8357' y='775.2612'>Nebenl&#228;ufige Synchronisation</text><line style='stroke:#181818;stroke-width:1.0;' x1='513.7492' x2='555.7492' y1='834.4822' y2='834.4822'/><line style='stroke:#181818;stroke-width:1.0;' x1='555.7492' x2='555.7492' y1='834.4822' y2='847.4822'/><line style='stroke:#181818;stroke-width:1.0;' x1='514.7492' x2='555.7492' y1='847.4822' y2='847.4822'/><polygon fill='#181818' points='524.7492,843.4822,514.7492,847.4822,524.7492,851.4822,520.7492,847.4822' style='stroke:#181818;stroke-width:1.0;'/><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='227.0192' x='520.7492' y='810.9672'>Rechnung &amp; Belege werden an Datev</text><text fill='#000000' font-family='sans-serif' font-size='13' lengthAdjust='spacing' textLength='211.9392' x='520.7492' y='828.6732'>f&#252;r Vorsteueranmeldung geschickt</text></g></svg>