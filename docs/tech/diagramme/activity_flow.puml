@startuml
skinparam defaultFontName "Arial"

'v1 = breakthrough/Durchstich
'v2 = in naher Zukunft lösen
'v3 = in ferner Zukunft lösen (bis Oktober 2025),
'v4 = irgendwann mal
'grey = default = nicht Teil des Durchstichs
!define v1 #a8f0b1
!define v2 #f0e6a8
!define v3 #f0b4a8
!define v4 #a172b3
!define default #f1f1f1


start
v1:Handwerker öffnet die Handwerker-App;
split
    v1:Handwerker gibt seine Login-Daten ein;
    v1:Webseite authentifiziert Handwerker bei BC;
    if (ist Login erfolgreich?) then (ja)
    else (nein)
        stop
    endif
split again
    :Handwerker meldet sich über anderen Provider an (z.B. Google);
    note right: soll das möglich sein?
end split
v1:Handwerker ist angemeldet;
stop



start
v1:Kunde benötigt einen Handwerker;
split
    :Kunde ruft an;
    if (ist Kunde bereits im System?) then (ja)
        :CallCenter App fragt Kundendaten von BC ab;
        :Telefon-Mann legitimiert Kunden;
    else (nein)
        :Telefon-Mann erfasst Kundendaten;
        :CallCenter App sendet Kundendaten an BC;
        :BC legt neuen Kunden an;
        :BC führt Bonitätsprüfung aus;
    endif
    :Kunde sagt welche Dienstleistung er benötigt;
    :Telefon-Mann prüft ob die Dienstleistung angeboten wird;
    if (wird Dienstleistung angeboten?) then (nein)
        stop
    endif
split again
    split
        v1:Kunde besucht die Webseite;
    split again
        :Kunde installiert und öffnet die App;
    end split
    v2:Kunde sucht nach Dienstleistung;
    v2:Kunde wählt Dienstleistung aus;
    v2:if (wird Dienstleistung angeboten?) then (nein)
        stop
    endif
    v2:if (ist Kunde bereits angemeldet?) then (nein)
        v2:if (ist Kunde bereits registriert?) then (ja)
            split
                :Kunde gibt seine Login-Daten ein;
                :Webseite authentifiziert Kunden bei BC;
                if (ist Login erfolgreich?) then (ja)
                else (nein)
                    stop
                endif
            split again
                :Kunde meldet sich über anderen Provider an (z.B. Google);
            end split
        else (nein)
            v2:Kunde legt seine Login-Daten fest oder nutzt Provider (z.B. Google);
            v1:Kunde gibt seine Kundendaten ein;
            v2:Kunde gibt seine Kontodaten an;
            v1:Webseite sendet die Daten jeweils an BC;
            v2:BC führt Bonitätsprüfung aus;
        endif
    else (ja)
    endif
    v2:Kunde ist angemeldet;
end split

v3:Kunde wählt Zeitpunkt für die Ausführung;
v1:Anfrage wird an BC gesendet;
v3:BC ermittelt 3 verfügbare AK;
v3:BC ermittelt jeweils Anfahrt-Zeit;
v1:BC sendet Anfrage an Handwerker-App(s);
v1:Handwerker-App zeigt Benachrichtigung an;
switch (AK Reaktion)
case (nimmt Auftrag an)
    v1:Handwerker-App sendet "angenommen" an BC;
    v2:BC trägt Termin im Kalender des Handwerkers ein;
    v3:BC sendet "Auftrag bereits vergeben" an andere Handwerker-Apps;
case (sieht es zu spät)
    :Handwerker-App zeigt "Auftrag bereits vergeben" an;
    stop
case (lehnt Auftrag ab)
    :Handwerker-App sendet "abgelehnt" an BC;
    stop
endswitch
v1:BC sendet Kunden-Adresse an Handwerker-App;
v1:Handwerker-App zeigt Kunden-Adresse an;
note right: Wenn der Zeitpunkt "schnellstmöglich" ist, wird die Adresse als nächstes Ziel angezeigt.\nAnsonsten wird sie am Tag des Auftrags in einer Termin-Übersicht zur passenden Zeit angezeigt.
v3:Handwerker-App zeigt Navigations-Link zur Kunden-Adresse;

stop



start
v1:Handwerker kommt beim Kunden an;
if (ist Kunde vor Ort?) then (ja)
    v1:Handwerker wählt "Arbeit beginnen" in der Handwerker-App aus;
    v1:Handwerker-App sendet "checkin" an BC;
    note right: Falls keine Internet-Verbindung besteht, wird die Meldung in der App zwischen-\ngespeichert und gesendet, sobald eine Verbindung besteht.\nAusschalten des Smartphones oder Beenden der App, darf dies nicht verhindern.
    v1:Handwerker sieht den Arbeitsort (betritt das Haus);
    note: was ist ein "Kleinstauftrag"?
    if (will Kunde einen Kostenvoranschlag?) then (ja)
        note: TODO
    else (nein)
    endif
    if (ist Einkauf nötig?) then (ja)
        note: was ist, wenn der Einkauf / Bestellung mehrere Wochen dauert?
        v2:Handwerker wählt "Arbeit unterbrechen" in der Handwerker-App aus;
        v2:Handwerker-App sendet "Arbeit pausieren" an BC;
        note: wie wird die Einkaufszeit in Rechnung gestellt?
        v2:Handwerker fährt Einkaufen;
        v2:Handwerker wählt "Einkauf tätigen" in der Handwerker-App aus;
        v3:Handwerker-App zeigt Erinnerung "Denke daran für jeden Kunde einzeln zu bezahlen!";
        v2:Handwerker bezahlt mit Karte;
        split
            v2:Handwerker trägt Betrag manuell in der Handwerker-App ein;
            v2:Handwerker macht ein Foto vom Kassenzettel und wählt es in der Handwerker-App aus;
            note: ist ein Foto/Nachweis rechtlich notwendig oder welche Optionen gibt es?
        split again
            v3:Handwerker scannt den Kassenzettel (Foto);
            v3:Handwerker-App erfasst den Gesamtbetrag (Texterkennung);
            v3:Handwerker bestätigt korrekt erfassten Gesamtbetrag oder korrigiert manuell;
        end split
        v2:Handwerker fährt zum Kunden;
    else (nein)
    endif
    v1:Handwerker arbeitet beim Kunden;
    note: kann noch mehrmals unterbrochen werden
    note right: TODO: erneut einkaufen / mehrere Kassenzettel
    v1:Handwerker wählt in der Handwerker-App "Arbeit abschließen";
    v2:Handwerker macht Beweisfoto(s) vom Arbeitsergebnis;
    v1:Handwerker wählt in der Handwerker-App "Bestätigung durch den Kunden einholen";
    v1:Handwerker-App zeigt Unterschrift-Panel an;
    v1:Kunde unterschreibt auf dem iPad des Handwerker (in der Handwerker-App);
    note: was passiert mit dem Kunden, wenn er nicht unterschreibt?
    v1:Handwerker wählt in der Handwerker-App "Rechnung erstellen";
    v2:Handwerker-App sendet Beweisfoto(s) an BC;
    v2:BC speichert die Fotos;
    v1:Handwerker-App sendet "checkout" an BC;
    v1:Arbeitszeit endet offiziell;
    v1:BC speichert Auftrag als "abgeschlossen / noch nicht bezahlt";
    v1:BC berechnet Gesamtbetrag / erstellt Rechnung;
    note: was soll passieren, wenn es beim Kunden keine Internet-Verbindung gibt? -> Alternativ-Pfad: Rechnung später stellen -> Rechnung oder E-Mail?
    v1:BC sendet Gesamtbetrag an Handwerker-App;
    v2:Handwerker fragt, wie der Kunde bezahlen möchte;
    v2:Handwerker wählt (Zahlungsart) in der Handwerker-App aus;
    note: wie läuft die Rechnungsabwicklung mit der Kreditreform?
    switch (Zahlungsart)
    case (Karte)
        :Handwerker-App zeigt an "Bitte Kartenleser nutzen und danach hier bestätigen";
        :Handwerker holt Kartenleser;
        :Handwerker nutzt Kartenleser-App (Betrag eintippen);
        :Kartenleser zeigt Betrag an;
        :Kunde bezahlt mit Karte;
        :Kartenleser sendet Zahlungsbestätigung an Kartenleser-App;
        :Handwerker bestätigt die erfolgte Zahlung in der Handwerker-App;
        :Handwerker-App sendet "Kartenzahlung erfolgreich" an BC;
        :BC speichert Auftrag als "mit Karte bezahlt" ab;
        note: wie wird kontrolliert, ob das Geld tatsächlich eingegangen ist?
        :Handwerker-App zeigt "Zahlung erfolgreich" an;
    case (PayPal)
        :Handwerker-App sendet "pay with PayPal" an BC;
        :BC sendet "pay with PayPal" an Kunden-App oder Kunden-Email;
        :Kunde empfängt Zahlungsaufforderung;
        :Kunde zahlt auf dem eigenen Endgerät mit PayPal;
        :PayPal sendet Zahlungsbestätigung an BC;
        :BC speichert Auftrag als "mit PayPal bezahlt";
        :BC sendet Zahlungsbestätigung an Handwerker-App;
        :Handwerker-App zeigt "alles bezahlt" an;
    case (Klarna)
        :analog zu PayPal;
    case (Rechnung)
        v1:Handwerker-App sendet "pay with invoice" an BC;
        v1:BC prüft, ob eine E-Mail-Adresse vorliegt;
        if (ist E-Mail bekannt?) then (ja)
            v2:BC sendet "email available" an Handwerker-App;
            v2:Handwerker-App zeigt Optionen "Rechnung per E-Mail" und "Rechnung per Post" an;
            v2:Handwerker fragt Kunden und wählt die Option;
            v2:Handwerker-App sendet Auswahl an BC;
        else (nein)
            v2:BC sendet "email unknown" an Handwerker-App;
            v1:Handwerker-App zeigt Option "Rechnung per Post" an;
            v4:Handwerker-App zeigt Option "E-Mail-Adresse hinzufügen" an;
            v4:Handwerker fragt, ob der Kunde eine E-Mail-Adresse angeben möchte;
            if (soll E-Mail hinzugefügt werden?) then (ja)
                v4:Handwerker wählt "E-Mail-Adresse hinzufügen" aus;
                v4:Handwerker-App zeigt Eingabefeld für E-Mail an;
                v4:Kunde gibt E-Mail auf Handwerker Smartphone ein;
                v4:Handwerker drückt "absenden und für Rechnungszustellung verwenden";
                v4:Handwerker-App sendet "new email ..." an BC;
                v4:BC fügt E-Mail-Adresse zum Kundenkonto hinzu;
                v4:Handwerker-App sendet "Rechnung per E-Mail" an BC;
            else (nein)
                v4:Handwerker-App sendet Auswahl "Rechnung per Post" an BC;
            endif
        endif
        if (ist Auswahl) is (per Post) then
            v1:BC stößt postalischen Rechnungsdruck an;
        else (per E-Mail)
            v2:BC sendet Rechnung an die Kunden-Email;
        endif
    endswitch
else (nein)
    :Handwerker meldet den Kunden in der Handwerker-App als nicht angetroffen;
    :Handwerker-App sendet Abwesenheit an BC;
    :BC speichert den Vorfall;
    if (ist der Grenzwert überschritten?) then (yes)
        :Kunde wird in BC auf die rote Liste geschrieben / gesperrt;
        stop
    else (nein)
        :BC sendet Warnung an Kunden-App, dass zu viele Vorfälle zur Sperrung führen;
        :Kunden-App zeigt Notification an;
        :Kunden-App zeigt Warnung in der Übersicht an;
        stop
    endif
endif
stop


@enduml
