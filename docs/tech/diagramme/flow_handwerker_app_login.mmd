%%{init: {'theme': 'neutral', 'scale': 0.7}}%%
flowchart TD
    start([Start]) --> A[Handwerker öffnet die Handwerker-App]

    A --> B((Möglichkeiten))
    B --> C[Handwerker gibt seine Login-Daten ein]
    C --> D[Webseite authentifiziert Handwerker bei BC]

    D --> E{Ist Login erfolgreich?}
    E -- Ja --> F[Handwerker ist angemeldet]
    E -- Nein --> stop([Stop])

    B -- Soll das möglich sein? --> G[Handwerker meldet sich über anderen Provider an z.B. Google]
    G --> F

    F --> stop([Stop])