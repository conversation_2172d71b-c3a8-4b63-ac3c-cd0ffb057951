= Alles Könner: Technische Dokument
include::../config/header.adoc[]
:toc:
:toclevels: 2
:toc-title: Inhaltsverzeichnis


<<<

include::einleitung.adoc[]

<<<

include::business-central-konfiguration.adoc[]

<<<

include::design_entscheidungen/2024_10_21_entscheidung_für_nuxt.adoc[]

<<<

include::design_entscheidungen/2024_10_24_mockserver_nutzen_um_BC_zu_mocken.adoc[]

<<<

== Diagramme

Dieser Abschnitt enthält eine Reihe von Diagrammen, die helfen, Arbeitsschritte oder andere architektonische Entscheidungen visuell zu erklären.

<<<

=== Activity Flow Diagram

.Activity Flow Diagramm
[plantuml, target="diagramme/activity_flow", format="svg", align="center", scaledwidth="70%"]
....
include::diagramme/activity_flow.puml[]
....

<<<

=== Handwerker-App
[mermaid, align="center", scaledwidth="70%"]
....
include::diagramme/flow_handwerker_app_login.mmd[]
....

=== Kunden-App

==== Neukunden Bestellung

.Neu Kunden Bestellung
[plantuml, align="center"]
....
include::diagramme/neukunden_bestellung.puml[]
....

<<<

==== ERP Lead Erstellen

.ERP Lead Erstellen
[plantuml, target="diagramme/s7_lead_erp_erstellen", format="svg", align="center"]
....
include::diagramme/s7_lead_erp_erstellen.puml[]
....

<<<

include::fazit.adoc[]

<<<

include::appendix_tasks.adoc[]

