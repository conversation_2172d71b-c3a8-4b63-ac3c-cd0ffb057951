= Entscheidung für nuxt für Kunden Webseite

Datum: 2024-10-21

== Einführung
Die Kunden benötigen einen Einstiegspunkt, um eine Handwerker-Anfrage zu tätigen. Es soll sowohl eine Webseite, als auch eine App werden.

== Problemstellung
Da wir den Aufwand möglichst gering halten wollen, wollen wir für die Webseite und die Smartphone-App, wenn möglich, den gleichen Code benutzen. Hier stellt sich die Frage, welche Technologie wir dafür nutzen.

== Vorgeschlagene Lösung
Wir nutzen Nuxt, da es eine Lösung bietet, die sowohl das Frontend in Vue, als auch das Backend aus einem Guss bereitstellt. Da wir Entwickler viel Knowhow in Vue haben, entschieden wir uns für nuxt, damit wir schnell vorankommen. Auch kann die Webseite Serverside gerendert werden, was die Webseiten-Performance erheblich steigert und somit auch die Suchmaschinen-Optimierung begünstigt.

== Berücksichtigte Alternativen
Nachdem wir mit Flutter gestartet sind, haben wir gemerkt, dass eine Flutter Webseite nur für echte Applications (wie z.B. Google Earth oder Miro) sinnvoll ist und nicht für Webseiten, da diese sich nicht von Suchmaschinen-Crawlern indexieren lassen und somit Suchmaschinen-Optimierung (SEO) komplett unmöglich ist. Da dies unbedingt benötigt wird, mussten wir Flutter ausschließen.

