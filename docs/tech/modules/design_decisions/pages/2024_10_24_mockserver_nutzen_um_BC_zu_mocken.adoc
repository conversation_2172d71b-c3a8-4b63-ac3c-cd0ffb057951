= Mockserver nutzen um BC zu mocken

Datum: 2024-10-24

== Einführung
Da wir einen Frontend Client und einen Backend-Server mit nuxt in einem Verbund haben, wollen wir diese beiden Komponenten mit Integrationstests testen.

== Problemstellung
In den Tests spricht der Server aber auch mit anderen externen Servern, wie z.B. Business Central und der Microsoft Authentifizierung, welches nicht passieren sollte.

== Vorgeschlagene Lösung
Damit unsere Tests nicht externe Schnittstellen aufrufen, haben wir uns entschlossen "mock server" zu nutzen, der die externen Server wegmockt, sodass wir in den Tests z.B. keine echten Tokens holen oder BC-Anfragen machen.

== Berücksichtigte Alternativen
Es wurde specmatic ausprobiert, allerdings ist dies zu spezifisch auf Contract-Testing ausgelegt und schlechter zu konfigurieren, als mock server. Mock server entpuppte sich als die einfachere und flexiblere Lösung.

== Referenzen

* https://www.mock-server.com/

