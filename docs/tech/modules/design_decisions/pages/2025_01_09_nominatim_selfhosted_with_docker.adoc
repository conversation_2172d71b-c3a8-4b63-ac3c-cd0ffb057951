= Design Decision Template

Datum: 2025-01-09

== Einführung

nominatim ist ein Service, um anhand eines Strings Vorschläge für passende Adressen zu erhalten.

== Problemstellung

Der public server erlaubt nur 1000 requests pro Tag und 1 request pro Sekunde.
Dad<PERSON><PERSON> lassen sich keine nutzerfreundliche on-the-fly Vorschläge anzeigen.
Zudem wären 1000 requests pro Tag mittelfristig nicht ausreichend.

== Vorgeschlagene Lösung

Wir könnten eine eigene nominatim Instanz laufen lassen und darauf unbegrenzt und schneller als 1/Sekunde zugreifen.
Es gibt bereits eine vorgefertigte Lösung dafür:

https://github.com/mediagis/nominatim-docker/tree/master/4.5[nominatim-docker]

Man kann sie relativ unkompliziert mit docker installieren:

----
docker run -it \
  -e PBF_URL=https://download.geofabrik.de/europe/germany/brandenburg-latest.osm.pbf \
  -e REPLICATION_URL=https://download.geofabrik.de/europe/germany/brandenburg-updates/ \
  -p 8080:8080 \
  --name nominatim \
  mediagis/nominatim:4.5
----

bzw. zum kopieren ohne line-breaks:

----
docker run -it -e PBF_URL=https://download.geofabrik.de/europe/germany/brandenburg-latest.osm.pbf -e REPLICATION_URL=https://download.geofabrik.de/europe/germany/brandenburg-updates/ -p 8080:8080 --name nominatim mediagis/nominatim:4.5
----

Download und Installation können mitunter 30 Minuten oder länger dauern, sind jedoch nur ein mal notwendig.
Bei Bedarf könnten wir die Installation entsprechend in einer Pipeline laufen lassen und dann regelmäßig aktualisieren (z.B. einmal im Monat).

Der self-hosted nominatim Server kann sehr gut für die lokale Entwicklung genutzt werden, um Probleme durch das rate-limiting der public Lösung zu vermeiden.

Dazu muss natürlich auch die URL im Backend angepasst werden:

`customer_website/server/api/osm/suggestions.ts`

`url = 'http://localhost:8080/search?q=...'`

== Begründung

Da aktuell kein Bedarf besteht (die 1000 requests pro Tag dürften erstmal ausreichen) und die Qualität der nominatim Vorschläge noch Luft nach oben lässt, haben wir uns dagegen entschieden, die Lösung direkt zu hosten und in eine Pipeline einzubinden.

== Auswirkungen

-

== Berücksichtigte Alternativen

Man kann auch nominatim provider nutzen, die dann aber pro 1000 request einen Betrag von uns verlangen.
Da die User später beliebig nach Adressen suchen können, wäre dies ein finanzielles Risiko, dass wir nicht eingehen wollen.
Mit einer self-hosted Lösung würde bei übermäßig vielen Suchanfragen nur die Server-Auslastung ansteigen.

== Fazit

Man kann nominatim relativ unkompliziert selbst hosten.
Für lokale Entwicklungszwecke könnte dies auch interessant sein, wenn man Probleme durch das rate-limiting des public-servers von nominatim vermeiden will.

== Referenzen

* https://github.com/mediagis/nominatim-docker/tree/master/4.5
* https://nominatim.org/release-docs/latest/admin/Installation/
