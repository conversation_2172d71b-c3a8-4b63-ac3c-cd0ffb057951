= Why we send emails via nuxt

Date: 2024-12-11

== Introduction

The customer needs feedback for his/her order request.

== Problem definition

We want to trigger generating the html sending the confirmation email in a way that is comfortable and easy to do for a developer.

== Suggested solution

Because the nuxt backend is the only backend at this time, we will use it for email generation and sending.
Furthermore, the mjml library used to make beautiful emails was easily setup using node and would have not been as easy in other environments.

== Considered alternatives

We could have used Business Central as Backend, but then we could not have used the html generation in BC, because mjml is a node project.
We would have to use another backend or serverless function, which would result in another software component to maintain.
The amount of components is often good to keep low, because of the deployment, maintenance and onboarding overhead.
