== Design Entscheidung: Reverse Proxy for Requests to Business Central

=== Einführung
Da web browser darauf achten, dass die webapp nur mit Webseiten kommuniziert, die auf der gleichen Domain sind (CORS), kann sich unsere flutter web app derzeit nicht mit BC und der Microsoft Authentifizierung verbinden.

=== Vorgeschlagene Lösung
Derzeit schauen wir zu Azure Front Desk, um das zu lösen. Für die lokale Entwicklung probieren wir einen lokalen reverse proxy mit nginx oder versuchen die flutter web app über das npm package "http-server" mit proxy-Einstellungen zu serven.

=== Referenzen

* https://learn.microsoft.com/en-us/azure/frontdoor/front-door-overview

