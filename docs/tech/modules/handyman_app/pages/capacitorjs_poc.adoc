= CapacitorJS + Nuxt Proof of Concept

== PoC Summary

✅ Photo capture.

> Needs permissions in the Info.plist: `NSCameraUsageDescription`, `NSPhotoLibraryAddUsageDescription` and `NSPhotoLibraryUsageDescription`.

✅ Signature capture.

✅ Push notifications.

✅ GPS tracking.

> Needs permissions in the Info.plist: `NSLocationWhenInUseUsageDescription`.

⚠️ Webhook integration for payment updates.

> In CapacitorJS, silent push notifications/data-only notifications are https://capacitorjs.com/docs/apis/push-notifications#silent-push-notifications--data-only-notifications[not possible].
A potential solution could be using custom iOS code and Firebase Cloud Messaging for Android.
Another option is solving the issue with polling or using regular (non-silent) push notifications that open the app and fetch the data.

✅ PDF export for invoices.

⚠️ Video recording.

> Video recording could not be integrated into the native app because Capacitor does not offer an interface for this.
A possible workaround would be opening the camera app as a link within the Handyman app.
Another option would be manually recording a video using the camera app and uploading the video file within the Handyman app.

== Push Notifications

1. Set up Capacitor for the app as described in the documentation.
2. Enable push notifications in XCode.
3. Creating a certificate and send a notification via curl.
a. Create a Certificate Signing Request (CSR) in the admin user of the mac via the Keychain Access tool.
Go to `Keychain Access` > `Certificate Assistant` > `Request a Certificate From a Certificate Authority...`.
Fill in the email address and common name (e.g. com.alleskoenner24.handwerker.app), select "Save to disk".
Click "Continue" and save the CSR file.
This will automatically save the private key to the keychain access tool.
Find it and export this private key as a file (`privatekey.pem`).
b. Upload the CSR to the Apple Developer account and download the certificate (`aps.cer`, which is already in DER format).
c. Use the following command to send a push notification to the device.
Use the device token seen in the XCode console or in the profile page in the handyman app.
The device token for the iPhone is `F76057E...`.

    curl -v \
      --header "apns-topic: com.alleskoenner24.handwerker.app" \
      --header "apns-push-type: alert" \
      --cert ./aps_development.cer \
      --cert-type DER \
      --key privatekey.pem \
      --key-type PEM \
      --data '{"aps":{"alert":"test"}}' \
      --http2 https://api.push.apple.com/3/device/F76057E93FDAD76CE2949F2FE66CD2A88D9B784F71AB64CCE37E88C9F7A34BD6

> CSR + certificate + private key (for production certificate) can be found in the alleskoenner keepass in the intern git repository (passwortSafe/alleskoenner.kdbx).