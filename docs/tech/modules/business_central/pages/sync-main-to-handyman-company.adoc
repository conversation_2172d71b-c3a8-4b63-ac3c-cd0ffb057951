= Sync Data between Main and Handymen Companies
:navtitle: Sync Main and Handymen

== Setup

Our main company extension has a "Synchronization Setup" page.
Here you will need to select the client ID of the App Registration (from Azure) that should already exist in the "Microsoft Entra Applications" page in Business Central.

Now the API Client Secret must be set.
This can be done via the Actions menu and selecting "Set API Client Secret" and entering the client secret.
(The client secret was created in the Azure App Registration)

== Syncing Contact and Sales Order to handyman company after appointment request was confirmed

As soon as the handyman accepts an appointment request the sales order and the customer are copied from the main company to handyman's company.
This works by triggering a custom Business Central function via API.
This function internally makes an HTTP request to the handyman's company to create a customer and a sales order.
