= Environment Cleanup
:navtitle: Environment Cleanup

== Clean up customers and salesQuotes

You can run a clean-up script to automatically delete all customers and salesQuotes that have been created/used after 2024-10-11 (the date when we started to use Business Central).
To do so, run the dev-production script of the customer-website `npm run dev-production`.
Then use curl (or postman, ...) to send a `POST` request to `http://localhost:3000/api/clean-up` (no body).
This will trigger fetching all customers and salesQuotes, and delete them.
So after the script has run, the BC instance will look fresh and clean, with some sample customers and salesQuotes.
