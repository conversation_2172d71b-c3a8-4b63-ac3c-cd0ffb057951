= Business Central
:navtitle: Business Central

Information regarding developing, (local) setup of Business Central and learning sources.


== Get started with Business Central and AL

The application language (short: AL) is used to create extensions for Microsoft Dynamics 365 Business Central. The
learning pages provided by Microsoft are a good point to get started. Some useful links are listed below:

=== Introduction to AL (application language)

https://learn.microsoft.com/en-us/training/modules/introduction-development-process/1-introduction

part of the learning path\
https://learn.microsoft.com/en-us/training/paths/application-foundation-al-language/

=== Setup sandbox environment

https://learn.microsoft.com/en-us/dynamics365/business-central/dev-itpro/developer/devenv-get-started#steps-to-set-up-a-sandbox-environment-and-visual-studio-code
(basically step 1.)

Setup sandbox as local Docker\
(not yet attempted)
https://learn.microsoft.com/en-us/dynamics365/business-central/dev-itpro/developer/devenv-get-started-container-sandbox#set-up-a-local-hosted-container-sandbox

=== AL best practices

https://learn.microsoft.com/en-us/dynamics365/business-central/dev-itpro/compliance/apptest-bestpracticesforalcode
https://learn.microsoft.com/en-us/dynamics365/business-central/dev-itpro/webservices/odata-client-performance - How to make API reqeusts to BC performant


=== Use the Business Central Administration Shell

Important: Run the Administration Shell in Windows "as Administrator" to avoid "access denied" errors.

To install the Administration Shell locally (only available for Windows), it is sufficient to run the "Demo" installation, or only the Server part. If you encounter an error during installation, it might help to uninstall the PowerShell 7 and rerun Business Central installation. The BC installation will also install the PowerShell 7 everytime and fails to continue if it was installed already. (known issue)
