= Create new Environment and Company
:navtitle: New Environment and Company

This is a short guide what to consider when creating a new environment, setting up / updating the `BC_CLIENT_ID` for the customer_website, and how to create a new company within the BC user interface.

== Create new BC Environment

Visit the https://businesscentral.dynamics.com/b37c212c-6995-45f1-a9ef-cb27c169c009/admin[BC Admin Center] of our Tenant.

image::images/bc-admin-center-environments.png[]

Create a new Environment.

== Create new Company in BC

Visit the BC user interface and select the desired environment.

image::images/bc-select-environment.png[]

The URL might be something similar to https://businesscentral.dynamics.com/b37c212c-6995-45f1-a9ef-cb27c169c009/MainSandbox
Search for "companies" and open the Companies page.
Open the "New ..." drop down and click "Create New Company".
Follow the setup process and select "Production - Setup Data Only".
You don't need to add any users.
Wait until the company has been set up.

Then, GET all companies from https://api.businesscentral.dynamics.com/v2.0/b37c212c-6995-45f1-a9ef-cb27c169c009/MainSandbox/api/v2.0/companies, to obtain the company ID for the newly created company.

== Enable API access

A new BC Environment does not offer any API access, so request sent from the customer website or via postman will fail due to missing permissions.
In BC search for "entra app" and select "Microsoft Entra Applications".

=== Enable API access for customer website

Add a new Application (a new entry in the Microsoft Entra Applications list).
`Name: some name so you can identify the entry`
`Client ID: 5ca29e2f-8726-4b3f-963c-2e7b84152561` - must be identical with the BC_CLIENT_ID in configmap.yaml of the customer_website
`State: Enabled`
In the "User Permission Sets" section, add the permission sets `D365 ACCOUNTANTS`, and `D365 BUS FULL ACCESS`.
