= Kubernetes

== Relevant files for k8s

[source, shell]
----
.github/workflows/
    build-and-deploy-customer-website.yaml
...
customer_website/
    Dockerfile
...
k8s/
    configmap/
        configmap-test.yml <- this is a generated file. Do not change manually.
    cert-manager.yml
    deploy.yml
    ingress.yml
----

== How does the deployment work?

=== TL;DR

The github workflow builds and pushes the dockerfile and after ensuring that the environment variables are available in the k8s cluster it finally deploys the nuxt app by applying the deployment of the most recent docker image.

=== In depth explanation of github workflow

1. The github workflow builds a docker image via the Dockerfile contained in `customer_website/Dockerfile`.
2. It then is pushed to the github docker container registry with the `:latest` docker tag.
3. Now the configuration of our nuxt app is converted into a configmap for our environment variables:
   - A configmap is a simple kubernetes resource that just contains a map of values. This resource is then referenced when deploying our nuxt app so that it takes the key-value-pairs and sets them as **environment variables** for our app.
   - Via the command `kubectl create configmap alleskoenner-test-cfg --from-env-file=$envFilePath --dry-run=client -o yaml > $configmapFilePath
` the .env.production file is read and a configmap file `configmap-test.yml` is generated.
   - If the configuration changed it will now commit and push the changed configmap-text.yml.
   - Now this configmap is applied to the kubernetes cluster via the `kubectl apply` command.
   - The reason we push it to the git repository after applying it is that we want the config in the repo to be always in sync with what is deployed on the k8s cluster.
4. Finally, the docker image is deployed:
   - The `Azure/k8s-deploy` github action takes the deploy.yml and applies it to the kubernetes cluster
   - The deploy.yml contains what docker image to pull from the docker registry - in this case the `:latest` image - and deploy it.

== What about the secrets?

The secrets are not and **must not** be stored in the .env.properties or the configmap. They have to be manually entered as a kubernetes secret in the secret that is referenced in the `secretRef` property of the `deploy.yml`. Please speak to the kubernetes contact person when you want to add or change a secret.