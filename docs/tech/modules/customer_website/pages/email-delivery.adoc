= Email Delivery

== TL;DR

* Emails are sent via the https://portal.azure.com/#@klosebrothers.onmicrosoft.com/resource/subscriptions/74a56196-0b67-41ca-9884-42a53c4869b8/resourceGroups/alleskoenner/providers/Microsoft.Communication/CommunicationServices/alleskoenner-communication-service/resource_overview[Azure communication service]
* Email Templates are generated via https://mjml.io/[MJML] (please look at the `/email` folder at project root)
* Email Templates are automatically generated via https://github.com/klosebrothers/alleskoenner/blob/master/.github/workflows/generate-and-commit-email-templates.yaml["Generate Html Emails" GitHub workflow] and committed to the repo.

== Email generation and frontend integration

* In the project root there is a folder called `email`.
* The node script inside will generate a json file based on the MJML templates located in the `/email/mjmlTemplates` folder.
* The json file is written into the frontend so that it can use those HTML templates to send emails via Azure.
* Before the email is sent the html templates are filled with data using https://handlebarsjs.com/guide/[handlebarjs]

The following script is a shortcut to generate that json file (if you're executing it from the IDE):

[source,bash]
----
cd ../../../../../email && \
npm install && \
npm run generateEmailHtmlTemplates;
----

== Automatic email generation

* We have a https://github.com/klosebrothers/alleskoenner/blob/master/.github/workflows/generate-and-commit-email-templates.yaml["Generate Html Emails" GitHub workflow] which is only triggered when something in the `email` folder changes.
* It triggers the node script which goes through the `mjmlTemplate` folder and transforms the mjml to html and puts it into a json object.
* The json object is written to the frontend and if it has changed the changes automatically get committed and pushed.
