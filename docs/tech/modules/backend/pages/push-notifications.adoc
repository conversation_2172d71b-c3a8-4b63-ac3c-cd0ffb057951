= Push Notifications

== General

* The certificate and its private key are stored in the azure keyvault.
* We use the https://github.com/jchambers/pushy[pushy library] to send push notifications to the devices.
* In our backend we fetch the certificate and private key from keyvault, build a pushy client and send push notifications.
* We use a custom property named "route" to let our app know to which screen it should navigate when the user clicks on the notification.
