= Calendar Availability

== Business Logic

* We receive a list of users and the requested time.
* For each user we get the available timeslots and check if all timeslots are free.
* As of February 2025 the timeslots we check include a fixed travel time and a fixed appointment duration.
* Only available handymen are returned.
* If we search for the availability of a non-existing handyman he will be ignored.

== Users

We have two test users in outlook that have their own calendar:

* <EMAIL>
* <EMAIL>

User credentials are stored in the keepass.
The Alleskönner organisation requires 2FA, so we've added the TOTP secret in the keepass as well.

== Authentication

=== App Registration

To connect to Outlook, we needed to create an app registration in Microsoft Entra Admin Center.
It can access the Microsoft Graph API without a connected user.
The app registration provides a client ID and a client secret.
These will be needed to authenticate with the Microsoft Graph API.

As of February 2025 our current app registration is called `alleskoenner-backend-graph-api-principle` with the Application client ID `f5ebc6c4-a71f-4d3b-bb09-357e80626db1`.
It is located in the Alleskoenner Tenant (with the tenant ID `056998c4-a0f4-4928-9603-ff846f225d5a`) and can be found https://entra.microsoft.com/#view/Microsoft_AAD_RegisteredApps/ApplicationsListBlade/quickStartType~/null/sourceType/Microsoft_AAD_IAM[here].
It has the following permissions:

* User.Read.All
* Calendars.ReadWrite

It expires on 2026-02-10.

Credentials are stored in the alleskoenner keepass.

=== Microsoft Graph API SDK

We use the https://learn.microsoft.com/en-us/graph/sdks/sdk-installation#install-the-microsoft-graph-java-sdk[Microsoft Graph API Java SDK] to connect to the Microsoft Graph API.
