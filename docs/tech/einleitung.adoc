== Einleitung
In diesem technischen Dokument werden die wesentlichen Designentscheidungen und technischen Diagramme des Projekts festgehalten. Es dient als zentrale Informationsquelle für alle Beteiligten und ermöglicht eine transparente Nachverfolgung der Entwicklungen und Änderungen im Projektverlauf. Die regelmäßige Aktualisierung der Diagramme gewährleistet, dass alle Teammitglieder stets über den aktuellen Stand der technischen Architektur informiert sind.

Die Dokumentation ist in einem Git-Repository gehostet, was eine einfache Versionierung und Zusammenarbeit ermöglicht. Durch die Nutzung von Git können Änderungen nachverfolgt und frühere Versionen bei Bedarf wiederhergestellt werden. Dieses Dokument ist somit ein lebendiges Element des Projekts, das kontinuierlich angepasst wird, um den sich ändernden Anforderungen und Erkenntnissen gerecht zu werden.