# Techncial Documentation site

## TL;DR

Just run this command and open the html file shown in your shell:

```shell
npm install; && \
npx antora -v && \
npx antora generate antora-playbook.yml
```

IntelliJ under Windows:

```shell
npm install;
npx antora -v
npx antora generate antora-playbook.yml
```

## Setup

* Preferably use nvm lts
* I have setup the project so that it is not setup globally
* In this directory run the following to setup the infrastructure

```shell
npm install
```

* Check installed correctly

```shell
npx antora -v
```

* You should have the following output

```
@antora/cli: 3.1.9
@antora/site-generator: 3.1.9
```

* Generate the documentation by running this command:

```shell
npx antora generate antora-playbook.yml
```

* Open the html page presented to you in the command line

## Upgrade

* https://docs.antora.org/antora/latest/install/upgrade-antora/
