== 2024-11-05 Odoo Meeting

* *General*

* *Main instance*
** All customer data

* *Handyman instance*
** Each Handyman has their own instance
** Can be created automatically
*** Similar to accounting

* *SAAS*
** Can't customize much
** No development
** No bi-directional communication

* *odoo.sh / On Premise*
** Can do development
** odoo.sh bi-directional possible

* *Commercial*
** Usually each company should have their own subscription
** [ ] *Odoo TODO:* Alleskoenner wants to be responsible for the subscriptions
** We need to pay for a user and an instance if we have an odoo.sh per handyman
** Odoo represented in most countries except where there are sanctions
** [ ] *Odoo TODO:* Would it be possible to have all the handyman on one odoo.sh instance as "sub-companies"?

* *API's*
** No need to purchase an extra license for the use of the API's
** [ ] *Odoo TODO:* Datev Integration
*** Please check if the integration is fully automated
*** We don't want to download and upload csv files manually! :-)
** [ ] *Odoo TODO:* Creditreform Integration
*** Please check if the integration is fully automated
