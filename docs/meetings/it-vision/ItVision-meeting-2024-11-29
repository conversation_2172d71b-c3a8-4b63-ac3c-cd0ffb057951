Questions:

- Already have experience with setting up multi company instance?
  - Limitations on number of companies or customers?
  -> no limitations, just performance issues at some point (but 1.000.000 customers should be possible)
  -> each production environment cost around 250€
  - Any other limitations?
  

- What licenses will we need?
  - We will have one or two API users
  - Every company will have one user and should only see his company
  - A handful admins should see everything
- What are best practices when setting up our BC instance? 
  - Any settings we should know of that can't be changed afterwards?
  - Any special knowledge necessary for setting up sales process?
- How are changes to BC rolled out? Any best practices? Staging? CI/CD?
- Any limit of number of customers, sales orders, etc? Database Size?
- Do you have experience with Power Automate? Should we rather use this or our own Code? What are pros and cons?
- Do you have experience in scanning receipts and automatically add it to the inventory?
  -> yes but it is complex and a lot of work due to item details (measurement unit,...). We do not need an inventory / stock.

Technical Questions:

- Extend api of customer by a boolean flag "hasAcceptedTerms"

- Copy sales quote from one company to another?
