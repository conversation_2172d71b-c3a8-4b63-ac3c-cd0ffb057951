= IT Vision meeting 2024-12-19

== Agenda

* Show and Tell
* Requirements for Development
* Discussion about appointment coordination logic

=== Show and Tell

Notes

*

=== Requirements for Development

We would want to ask if you could develop a simple extension for us and then explain the code.

* Do you generally have capacity to do so (until Jan 17th (preferably sooner))
* We would like to take your extension and use it as a starting point.
* How can we collaborate on this?
* Where do you store the code? git?

==== Create order API endpoint

Current State:

* We use the BC customer endpoint to create customer
* Then we create sales order with the customer id and add the order description to a sales order line

Desired Outcome:

* We have one "order" endpoint in the Main BC instance which does the following:
** Create a customer
** Create a sales order for that contact
** Create an "appointment request" linked to the sales order

Input Arguments:

    {
        displayName, //string -> customer
        addressLine1, //string -> customer
        addressLine2, //string -> customer
        city, //string -> customer
        postalCode, //string -> customer
        email, //string -> customer
        phoneNumber, //string -> customer
        type, //string -> customer ("Company" or "Person")
        taskDescription, //string --> description of a single sales order line
        isConditionsChecked, //boolean -> customer
        appointmentRequestDate, //Date -> appointmentRequest
        requestedHandymanSkill, //handymanSkill -> appointmentRequest
    }

Where no extra validation is done on BC side, except for the isConditionsChecked flag (which must be set to true).

Output:

    {
        salesOderNumber,
        displayName,
        addressLine1,
        addressLine2,
        city,
        postalCode,
        email,
        phoneNumber,
        type,
        taskDescription,
        isConditionsChecked,
        appointmentRequestDate,
    };

Data structure of appointment request:

    {
        date, // date + time
        status, // enum of pending, confirmed, canceled
        handymanSkill, // relation to a handymanSkill
        salesOder, // relation to a sales order
        customer, // is this really needed? Sales Order already has relation to customer
        requested handymen, // as list of relations to handyman companies
        confirmed handymen, // as relation to single handyman company
    };

BC page of appointment requests is called "Appointment Requests" and shows a table with following columns:

* date
* status
* skill
* customer
* customer phone number
* sales order
* requested handymen (as handyman company)
* confirmed handymen (as handyman company)
* [sales order line description] // optional

The sales order, sales order line and customer should be clickable to let the user go to those resources.

At this point of time we don't need a "detail page" of a single appointment request.

==== Extend customer to show and set Terms and Conditions flag

BC page of customer shows Terms and Conditions flag (read only).

Terms and Conditions flag is set while creating order (with the above endpoint).

=== Discussion about appointment coordination logic

* We need a table of handyman companies with their respective skills, postal codes and eventually also some reference to the handyman's calendar in the main company.
* The skills must be a fixed set of skills (not just text) -> enum / extra table?
* After order creation an event must be triggered on which following business logic must be made
** handyman companies must be filtered by skill and postal code
** request to microsoft graph api must be made for all fitting handymen to find out which handymen are available
** multiple requests to handyman apps must be made

* New endpoint for accepting appointment responses
* should save handyman that was the first to accept the order in the "confirmed handymen" field of the appointment request
* should change the status of appointment request to "confirmed"
