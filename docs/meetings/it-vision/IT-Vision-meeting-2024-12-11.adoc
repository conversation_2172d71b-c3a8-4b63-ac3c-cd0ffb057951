= IT Vision meeting 2024-12-11

== Agenda

* Call Center & Syncing Data
* Sales Orders & Sales Invoices
* Accounting
* Handymen Coordination
// * Purchase of Material
// * Credit rating / Creditworthiness

== General Flow

* Customer makes request (via website/app/call center)
* Create Sales Quote & Customer in main company
* Automatically find out appointments from all handyman companies
** based on skills, postal codes and availability
* Calendars of handymen (smartphone calendar or app) are in sync with handyman BC company
* Notification is sent to a certain amount of handymen that are available
* Handyman that accepts notification must get Contact, Sales Quote from main company converts to confirmed Sales Order on the handyman company.
* Appointment must be saved to handyman calendar & simple estimating of travel time (e.g. adjacent postal codes -> 15 mins)
* Handyman visits customer, finishes work, customer signs off
* Invoice must be created
* Material must be added to invoice
* Invoice must be sent to customer
* Invoice must be synced to main company (?)

== Call Center & Syncing Data

A call center worker needs to be able to:

* see and search through all customers
* see all customers / sales quotes / orders / invoices / banking statements from both main and handyman companies
** -> All data from the handymen companies must be synced to main company

Questions:

* Is it possible to have licenses for them to use BC without being tied to the specific name of call center worker?
** Are there like account seats that can be used by multiple call center workers?
** We may have to build an own app that uses the API if licensing is difficult.

* Is there something like an event based solution (like kafka) to facilitate syncing data?
** Does it make sense to hold all data in the main company for better search?
** Or do we need own fast access database?

**Outcome**

* It probably makes sense to use Team-Licenses for the call center workers.

== Sales Orders & Sales Invoices

Questions:

* How does the general sales flow work usually?
* We need globally identifiable invoices. -> every handyman company must have an own shorthand symbol
* Is there support for an Integrity Checksum for invoices?
** Directive 2014/55/EU
* What format does BC write the invoices. Are PDFs supported?
** We will probably have to use an extension to create machine readably invoices like X-Rechnung
** E-Documents app?

**Outcome**

* Globally identifiable documents are possible -> every handyman gets his own prefix
* Simple sales flow should suffice
* Microsoft will implement features in BC before they are mandatory by German regulations

== Accounting

Note:

* Every handyman bank account has an own bank account.

Questions:

* Reconciliate bank account with invoices -> How does this work in general?
** Handyman will get a payment -> must automatically be mapped to invoice.
* Handymen will need to transfer money to the main company, for example commissions, credit back payments, etc.
** How does this get transferred into BC accounting?

**Outcome**

* We need a bank that supports semi-real time access to bank accounts via API
* Still some things to figure out

== Handymen Coordination

Flow:

* BC main company has access to all calendars of handymen
* Automatically checks what handymen are suitable (have the right skills)
* Then checks all calendars and finds 1-5 free handymen
* Handymen get a notification on their phone
* First Handyman accepts
* [Handyman instance now gets customer & sales quote -> syncing]
* ...

Notes:

* Every Handyman will have an Office 365 Calendar
* BC will need to:
** Check all companies for suitable handyman skills
** Access all calendars of suitable handymen
** Send request to handymen phone
** Sync Sales Quote and Customer with handyman instance
** Turn Sales Quote into Sales Order

Questions:

* Is processing power of BC backend powerful enough?
* Are there limits of how many outgoing requests BC is able to do?
* How would you implement this?
* Should we implement our own backend for this handyman coordination?

**Outcome**

* We would need an external system to calculate free appointments based on skill and postal code of handymen

// == Purchase of Material
//
// Questions:
//
// * What is the inventory? Do we need it? Can we add it later?
// * Use case:
// - Handyman visits customer
// - sees that he needs material
// - buys material
// - adds material to invoice
// * How to automate this? (reminder: Handyman will not login to BC, but will have an app)
//
// == Credit rating / Creditworthiness
//
// * As soon as customer is created the credit rating is fetched vor that customer
// * We would like to use `COSMO CrefoDynamics` for the credit rating check
// * After success the handymen coordination is automatically triggered
// * After failure an email is sent



