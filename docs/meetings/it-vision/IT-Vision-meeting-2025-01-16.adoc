= IT Vision meeting 2025-01-16

== Agenda

* Show and Tell
* Requirements for Development


=== Show and Tell

What happened so far:

* Order POST endpoint (create customer, sales order, and appointment request (+ archive functionality))

Notes

*

=== Requirements for Development

* MainInstance must have a representation of the handyman companies, including their postal codes
* Clarify how appointment requests reference "Requested handymen" and "Confirmed handyman"
* GET request for all handyman companies by postal code
* GET appointment requests by handyman (BC goes through all appointment requests and looks for those who have this handyman in the "requestedHandymen" field)
* PATCH confirm appointment request
* PATCH reject appointment request

=== Activity Diagram

image::IT-Vision-meeting-2024-12-19-activity-diagram.jpg[]

```plantuml
@startuml
actor "Customer Website" as CW
actor "<PERSON><PERSON><PERSON><PERSON><PERSON>" as AK
actor "<PERSON><PERSON>" as HA
actor "Business Central" as BC
actor "Outlook Calendar" as OC

CW -> AK: Send order request
AK -> BC: Create order (POST to BC order endpoint)
AK -> BC: Search available handymen (GET all handymen by postal code)
AK -> OC: Search available handymen (via Outlook Calendar)
AK -> HA: Send push notification for appointment request

HA -> BC: GET own appointment requests (via the "requestedHandymen" field)
HA -> BC: PATCH Send confirmation/rejection request for appointment request

@enduml

```