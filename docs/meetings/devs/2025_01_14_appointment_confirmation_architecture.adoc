= appointment confirmation architecture meeting

Datum: 14.01.2025
Kontext: Entwicklerteam denkt über technische Architektur des appointment confirmation Prozesses nach.

== Anforderungen

Main Instance braucht information über alle Handwerker.
Jede Handyman Company hat eine Adresse zu dem eigenen Outlook Kalender hinterlegt.
Jede Handyman Company hat eine Liste von PLZs hinterlegt, die er bedienen kann.

== Ablauf im Backend

1. Holt sich alle Handwerker und filtert nach PLZ - Wird das getriggered von BC/Customer App?
2. Sucht über Outlook Kalender nach verfügbaren Handwerkern
3. Schickt anfragen Handwerker App -> push notifications -> Zertifikats-Gedöns
4. Muss Antworten abfangen
5. Triggert Speicherung des beauftragten Handwerkers bei erster zustimmenden Antwort.
   Triggert in BC, dass Customer und Sales Order auch in der Handyman Company gespeichert wird.
6. Sendet Absagen an andere Handwerker
7. Schließt Anfrage ab. (Appointment Request ist completed)

== Ablauf in Handyman App

1. Bekommt appointment request per push notification -> wird auf eigener Seite angezeigt mit Beschreibung des Auftrags
2. Wartet 5 Minuten, ansonsten automatische Absage -> weitere Handymen anfragen - eigentlich neuen Termin vorschlagen
3. Bei Zusage wird ein request an BC geschickt. ->
4. Bei Business-seitiger Absage, den request nicht mehr anzeigen.

=== Auftrag angenommen

1. Zeigt alle Infos des Auftrags an (Adresse, usw.)


== Ablauf in Business Central

1. Stellt Liste von Handwerk bereit (evtl. gefiltert nach PLZ)
2. Wenn Handwerker zustimmt -> aktualisiert appointment request & speichert Customer und Sales Order in der Handyman Company

== Ablauf in Customer App

1. Order request wird gesendet
2. E-Mail mit Summary wird an Kunde geschickt
3. E-Mail mit Auftragsbestätigung (oder Absage) wird an Kunde geschickt

== Unklarheiten

* Wird der appointment coordination Prozess von BC oder von Customer App getriggered?
  - was soll der order request wirklich machen?
* Wo ist die Liste aller Handyman-Companies? Wie wissen wir, welche Outlook Kalender wir durchsuchen müssen?
* Was passiert, wenn kein Handyman die Anfragen annimmt

== Ergebnis

* Backend für den Prozess soll ein Spring Service werden
  - Modularer Monolith
  - Skalierbar von Anfang an. Im Backend wird kein state gehalten.


== Nächste Aufgaben

* Technische Know-How-Grundlage für modularen, skalierbaren Monolithen aufbauen
* Basic Spring Service aufsetzen
   - in monorepo
   - mit gradle
   - spring modulith + spring sleuth
   - ein modul für appointment coordination
* order endpunkt als eigenständiges modulith Modul im Backend
* Änderung der Customer App: order Endpunkt muss nun auf das neue alleskoenner backend umgestellt werden

* appointment coordination endpunkt als eigenständiges spring modulith Modul im alleskoenner backend und hat Grundgerüst für folgende Aufgaben:
   1. Holt sich alle Handwerker und filtert nach PLZ - Wird das getriggered von BC/Customer App?
   2. Sucht über Outlook Kalender nach verfügbaren Handwerkern
   3. Schickt anfragen Handwerker App -> push notifications -> Zertifikats-Gedöns
   4. Muss Antworten abfangen
   5. Triggert Speicherung des beauftragten Handwerkers bei erster zustimmenden Antwort.
   Triggert in BC, dass Customer und Sales Order auch in der Handyman Company gespeichert wird.
   6. Sendet Absagen an andere Handwerker
   7. Schließt Anfrage ab. (Appointment Request ist completed)

* email service als eigenständiges spring modulith Modul im alleskoenner backend (von nuxt backend nehmen bzw. über https://github.com/danihodovic/mjml-server[mjml server] nachdenken)
* Handyman app Grundgerüst bauen.
* Handyman app appointment request empfangen.
* Handyman app appointment screen bauen.


== Technische Grundlagen - weiterführendes Material

* Spring Modulith
   - https://spring.io/projects/spring-modulith
   - https://www.youtube.com/watch?v=Pae2D4XcEIg
* Hexagonal Architecture
   - https://www.happycoders.eu/de/software-craftsmanship/hexagonale-architektur/
* Spring Webflux
   - https://www.codecentric.de/wissens-hub/blog/reactive-programming-spring-webflux
   - https://javafullstackdev.medium.com/unlocking-the-power-of-spring-webflux-an-in-depth-guide-to-reactive-web-development-91c124506642
   - https://docs.spring.io/spring-framework/reference/web/webflux.html
