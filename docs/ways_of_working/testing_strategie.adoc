== Testing Strategie

=== Testing Ziele

* Tests helfen uns zu entwickeln
* Tests müssen WICHTIGE Dinge testen...
** was für Konsequenzen gibt es wenn diese Funktion nicht läuft?
** wie wahrscheinlich ist es, dass diese Funktion fehlschlagen wird?

* Wir wollen sicherstellen, dass die Kernfunktionalität getestet wird
** Wir wollen keine Zeit mit unnötigen Dingen verschwenden, z.B. Design-Themen, z.B. angezeigte Emojis usw.
** Teste keine Dinge, die zu einfach sind, um zu scheitern, z.B. Typüberprüfungen in TypeScript

=== Test Types

*Unit*

** Komponententests
*** Wir werden hier vorerst kein Cypress verwenden, da es nur mit Nuxt 2 (alt) funktioniert?
*** Technologie: Vitest

*Integration (inkl. API)*

* Versuche, so wenige wie möglich zu haben
* Wir werden es erlauben, die Tests gegen den Mock-Server auszuführen,
** aber nicht gegen die Sandbox, Live- / Produktionsumgebung...
* Technologie: Cypress

*End-To-End*

* Versuche, so wenige wie möglich zu haben
* Nächtlicher Lauf gegen die Sandbox

*Sekurität*

* Captcha (Stop die Bots!)
* TODO: Finde geeignete Werkzeuge zu einem späteren Zeitpunkt, wenn wir Live-Kunden haben...?

*Performance*

* TODO: Später...

=== General Code Quality

* Static Code Coverage
** Sonarqube

* Code Guidelines
** TODO: Sonarlint? Prettier?...

=== Sag nein!

* Gauge
