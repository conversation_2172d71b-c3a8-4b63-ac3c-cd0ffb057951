== Definition of Done

Die Definition of Done (DoD) ist ein gemeinsames Verständnis dafür, was es bedeutet, dass ein Arbeitsergebnis als vollständig betrachtet wird. Dies stellt sicher, dass alle Teammitglieder und Stakeholder ein klares und konsistentes Verständnis der erforderlichen Qualität und Vollständigkeit der Liefergegenstände haben.

=== Aktuelle Definition of Done
* Alle Akzeptanzkriterien sind erfüllt
* Die Dokumentation ist aktualisiert (z. B. README, API-Dokumentation).
* Der Code wird von mindestens einem anderen Teammitglied überprüft.
* User Story und Code ist mit sinnvollen Tests abgedeckt.
* Die Story wurde dem PO präsentiert und vom PO abgesegnet.
* Build lauft durch (Grün).

=== Aktuelle Green Build
* Der Code wird in den "main branch gemergt".
* Alle Integration- und Unit-Tests sind erfolgreich. (Cypress und Vitests laufen grün durch)
* Projekt wird erfolgreich gebaut.

=== Definition of Done Backlog
* Der Code entspricht den Code-Standards des Teams.
** Coding Standards Green
** eslint, sonarlint, sonarqube

* Feedback wird gesammelt und für zukünftige Verbesserungen dokumentiert.

=== Green Build Backlog
* Die Accessibility (Barrierefreiheit) ist gewährleistet.
* Die neue Version wird auf der dev-Instanz deployt
* Die neue Version wird auf der prod-Instanz deployt