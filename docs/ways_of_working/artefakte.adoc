== Appendix: Artefakte

=== Spike: [Titel des Spikes]

*Zusammenfassung:*
Eine kurze Beschreibung des Problems oder der Frage, die untersucht werden soll.

*Kontext:*

* Warum ist dieser Spike notwendig?
* Auf welches <PERSON>, welche User Story oder welchen technischen Bereich bezieht er sich?

*Ziele:*

* Welche Ergebnisse werden von diesem Spike erwartet?
* Definieren Sie ggf. Liefergegenstände wie Dokumentation, Prototypen oder Empfehlungen.

*Akzeptanzkriterien:*

* Definieren Sie, was den Erfolg dieses Spikes ausmacht.
* Beispiel:
** Recherche durchgeführt und dokumentiert.
**Prototyp entwickelt, um die Machbarkeit zu beweisen.

*Details:*

* [Optional] Ausführlichere Erklärung des Problems oder des Untersuchungsbereichs.
* Fügen Sie relevante Referenzen oder Links hinzu, falls erforderlich.

*Zeitbox:*
Dieser Spike sollte innerhalb von `X` Stunden oder `Y` Tagen abgeschlossen sein.

*Ergebnis:*

[Auszufüllen nach Abschluss des Spikes]
* Zusammenfassung der Ergebnisse des Spikes.
* Nennen Sie wichtige Erkenntnisse, Herausforderungen oder Entscheidungen.


=== Story Beispiel

*Beschreibung*

* *Als* Verwaltungsmitarbeiter,
* *möchte ich* mich mit meiner E-Mail-Adresse und meinem Passwort anmelden,\
* *damit* ich auf mein persönliches Dashboard und die Funktionen des ERP-Systems zugreifen kann.

*Akzeptanz-kriterien/tests*

* [ ] ...

*Anmerkungen*

* ...

*Sub-Tasks*

* [ ] ...


=== Task

**Ziel-Beschreibung**

* *Ich möchte* ...
* *Gemessen an...*
** ... (X/Y)
** ... (X/Y)

*Akzeptanz-kriterien/tests*

* [ ] ...

*Anmerkungen*

* ...

*Sub-Tasks*

* [ ] ...

=== Bugs

*Beschreibung*

* ...

*Schritte zur Reproduktion*

* ...
* ...
* ...

*Tatsächliches Ergebnis*

* ...

*Referenzen*

* link:stories/login.adoc[Link zur ursprünglichen User-Story]

*Anhänge*

* link:images/screenshot1.png[]

*Anmerkungen*

* ...

=== Kaizen

*Hintergrund*

* ...

*Ist/Ziel Zustand*

* ...

*Analyse*

* ...

*Sub-Tasks*

* ...

*Anmerkungen/Learnings*

* ...

