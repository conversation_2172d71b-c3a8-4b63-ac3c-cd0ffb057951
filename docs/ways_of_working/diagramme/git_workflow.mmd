gitGraph LR:
    commit
    commit
    branch pbi1
    checkout pbi1
    commit id: "pbi1_commit1"
    commit id: "pbi1_commit2"
    checkout main
    branch pbi2
    checkout pbi2
    commit
    commit
    checkout main
    merge pbi2
%% Rebase will move all the commits in the pbi1 branch to afer the pbi2 merge
    checkout pbi1
    merge main
    commit id: "pbi1_commit1: Shifted after rebase"
    commit id: "pbi1_commit2: Shifted after rebase"
    checkout main
    merge pbi1
    commit
    commit

