meta {
  name: Handyman PostCodes Query
  type: http
  seq: 3
}

get {
  url: https://api.businesscentral.dynamics.com/v2.0/{{EnvName}}/api/ITV/handyman/v2.0/companies({{CompanyId}})/handymanQuery?$filter=postCode eq '10589'
  body: json
  auth: bearer
}

params:query {
  $filter: postCode eq '10589'
}

auth:bearer {
  token: {{access_token_set_by_collection_script}}
}

body:json {
  {
    "displayName": "John Doe",
    "addressLine1": "123 Main St",
    "addressLine2": "Apt 4B",
    "city": "Springfield",
    "postalCode": "12345",
    "email": "<EMAIL>",
    "phoneNumber": "+1234567890",
    "customerType": "Person",
    "taskDescription": "Fix the leaking faucet in the kitchen.",
    "isConditionsChecked": true,
    "appointmentRequestDate": "2025-01-15",
    "requestedHandymanSkill": "PLUMB"
  }
}
