meta {
  name: Handyman_1
  type: http
  seq: 2
}

post {
  url: https://api.businesscentral.dynamics.com/v2.0/{{EnvName}}/api/ITV/handyman/v2.0/companies({{CompanyId}})/handyman
  body: json
  auth: none
}

body:json {
  { "code": "TESTapi",
              "companyID": "a9d86760-fe94-ef11-8a6e-6045bdc8a215",
              "companyName": "CRONUS DE",
              "deviceToken": "",
              "environmentName": "HandymanSandbox",
              "name": "",
              "outlookEMailAddress": ""}
}
