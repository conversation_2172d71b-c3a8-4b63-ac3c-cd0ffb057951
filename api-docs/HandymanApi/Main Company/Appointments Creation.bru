meta {
  name: Appointments Creation
  type: http
  seq: 1
}

post {
  url: https://api.businesscentral.dynamics.com/v2.0/{{EnvName}}/api/ITV/appointments/v2.0/companies({{CompanyId}})/orders
  body: json
  auth: none
}

body:json {
  {
    "shipToName": "Test Shipping Person",
    "shipToAddressLine1": "123 Main St",
    "shipToAddressLine2": "Apt 4B",
    "shipToCity": "Springfield",
    "shipToPostalCode": "12345",
    "shipToEmail": "<EMAIL>",
    "shipToPhoneNumber": "+*********",
    "billToName": "Test Billing Person",
    "billToAddressLine1": "Billing St 1",
    "billToAddressLine2": "Suite 2",
    "billToCity": "Springfield",
    "billToPostalCode": "54321",
    "billToEmail": "<EMAIL>",
    "billToPhoneNumber": "+*********",
    "email": "<EMAIL>",
    "customerType": "Person",
    "taskDescription": "Fix the leaking faucet in the kitchen.",
    "isConditionsChecked": true,
    "asSoonAsPossible": false,
    "appointmentRequestDateTime": "2025-01-15T10:00:00+00:00",
    "requestedHandymanSkill": "PLUMB"

  }
}
