meta {
  name: Main Company
}

auth {
  mode: oauth2
}

auth:oauth2 {
  grant_type: client_credentials
  access_token_url: https://login.microsoftonline.com/b37c212c-6995-45f1-a9ef-cb27c169c009/oauth2/v2.0/token
  client_id: 5ca29e2f-8726-4b3f-963c-2e7b84152561
  client_secret: ****************************************
  scope: https://api.businesscentral.dynamics.com/.default
}

script:post-response {
  if(req.getAuthMode() == 'oauth2' && res.body.access_token) {
      bru.setVar('access_token_set_by_collection_script', res.body.access_token);
  }
}
