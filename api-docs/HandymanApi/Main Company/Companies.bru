meta {
  name: Companies
  type: http
  seq: 7
}

get {
  url: https://api.businesscentral.dynamics.com/v2.0/{{EnvName}}/api/ITV/appointments/v2.0/companies
  body: json
  auth: none
}

body:json {
  {
    "displayName": "<PERSON>",
    "addressLine1": "123 Main St",
    "addressLine2": "Apt 4B",
    "city": "Springfield",
    "postalCode": "12345",
    "email": "<EMAIL>",
    "phoneNumber": "+1234567890",
    "customerType": "Person",
    "taskDescription": "Fix the leaking faucet in the kitchen.",
    "isConditionsChecked": true,
    "appointmentRequestDate": "2025-01-15",
    "requestedHandymanSkill": "PLUMB"
  }
}
