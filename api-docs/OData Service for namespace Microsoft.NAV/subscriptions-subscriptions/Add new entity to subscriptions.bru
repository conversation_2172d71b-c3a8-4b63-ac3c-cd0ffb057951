meta {
  name: Add new entity to subscriptions
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/subscriptions
  body: json
  auth: none
}

body:json {
  {
    "subscriptionId": "",
    "notificationUrl": "",
    "resource": "",
    "timestamp": "",
    "userId": "",
    "lastModifiedDateTime": "",
    "clientState": "",
    "expirationDateTime": "",
    "systemCreatedAt": "",
    "systemCreatedBy": "",
    "systemModifiedAt": "",
    "systemModifiedBy": ""
  }
}
