meta {
  name: Update entity in subscriptions
  type: http
  seq: 4
}

patch {
  url: {{baseUrl}}/subscriptions(:subscriptionId)
  body: json
  auth: none
}

params:path {
  subscriptionId: 
}

body:json {
  {
    "subscriptionId": "",
    "notificationUrl": "",
    "resource": "",
    "timestamp": "",
    "userId": "",
    "lastModifiedDateTime": "",
    "clientState": "",
    "expirationDateTime": "",
    "systemCreatedAt": "",
    "systemCreatedBy": "",
    "systemModifiedAt": "",
    "systemModifiedBy": ""
  }
}
