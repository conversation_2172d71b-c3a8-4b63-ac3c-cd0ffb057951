meta {
  name: Update entity in externaleventsubscriptions
  type: http
  seq: 4
}

patch {
  url: {{baseUrl}}/externaleventsubscriptions(:id)
  body: json
  auth: none
}

params:path {
  id: 
}

body:json {
  {
    "id": "",
    "companyId": "",
    "timestamp": "",
    "appId": "",
    "eventName": "",
    "companyName": "",
    "userId": "",
    "notificationUrl": "",
    "lastModifiedDateTime": "",
    "clientState": "",
    "subscriptionType": "",
    "eventVersion": "",
    "subscriptionState": "",
    "systemCreatedAt": "",
    "systemCreatedBy": "",
    "systemModifiedAt": "",
    "systemModifiedBy": ""
  }
}
