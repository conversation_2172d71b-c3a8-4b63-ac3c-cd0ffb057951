meta {
  name: Add new entity to externaleventsubscriptions
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/externaleventsubscriptions
  body: json
  auth: none
}

body:json {
  {
    "id": "",
    "companyId": "",
    "timestamp": "",
    "appId": "",
    "eventName": "",
    "companyName": "",
    "userId": "",
    "notificationUrl": "",
    "lastModifiedDateTime": "",
    "clientState": "",
    "subscriptionType": "",
    "eventVersion": "",
    "subscriptionState": "",
    "systemCreatedAt": "",
    "systemCreatedBy": "",
    "systemModifiedAt": "",
    "systemModifiedBy": ""
  }
}
