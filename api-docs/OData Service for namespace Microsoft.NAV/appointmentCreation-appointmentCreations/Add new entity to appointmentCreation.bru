meta {
  name: Add new entity to appointmentCreation
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/appointmentCreation
  body: json
  auth: none
}

body:json {
  {
    "id": "",
    "addressLine1": "",
    "addressLine2": "",
    "appointmentRequestDate": "",
    "city": "",
    "displayName": "",
    "customerNo": "",
    "customerID": "",
    "appointmentNo": "",
    "appointmentSystemID": "",
    "salesOrderNo": "",
    "salesOrderID": "",
    "email": "",
    "isConditionsChecked": "",
    "phoneNumber": "",
    "postalCode": "",
    "requestedHandymanSkill": "",
    "taskDescription": "",
    "customerType": ""
  }
}
