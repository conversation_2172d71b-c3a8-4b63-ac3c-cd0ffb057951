meta {
  name: Update entity in appointmentCreation
  type: http
  seq: 4
}

patch {
  url: {{baseUrl}}/appointmentCreation(:id)
  body: json
  auth: none
}

params:path {
  id: 
}

body:json {
  {
    "id": "",
    "addressLine1": "",
    "addressLine2": "",
    "appointmentRequestDate": "",
    "city": "",
    "displayName": "",
    "customerNo": "",
    "customerID": "",
    "appointmentNo": "",
    "appointmentSystemID": "",
    "salesOrderNo": "",
    "salesOrderID": "",
    "email": "",
    "isConditionsChecked": "",
    "phoneNumber": "",
    "postalCode": "",
    "requestedHandymanSkill": "",
    "taskDescription": "",
    "customerType": ""
  }
}
