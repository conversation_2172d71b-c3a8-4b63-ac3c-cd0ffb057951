meta {
  name: Update the navigation property appointmentCreation in companies
  type: http
  seq: 4
}

patch {
  url: {{baseUrl}}/companies(:id)/appointmentCreation(:id1)
  body: json
  auth: none
}

params:path {
  id: 
  id1: 
}

body:json {
  {
    "id": "",
    "addressLine1": "",
    "addressLine2": "",
    "appointmentRequestDate": "",
    "city": "",
    "displayName": "",
    "customerNo": "",
    "customerID": "",
    "appointmentNo": "",
    "appointmentSystemID": "",
    "salesOrderNo": "",
    "salesOrderID": "",
    "email": "",
    "isConditionsChecked": "",
    "phoneNumber": "",
    "postalCode": "",
    "requestedHandymanSkill": "",
    "taskDescription": "",
    "customerType": ""
  }
}
