meta {
  name: Create new navigation property to appointmentCreation for companies
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/companies(:id)/appointmentCreation
  body: json
  auth: none
}

params:path {
  id: 
}

body:json {
  {
    "id": "",
    "addressLine1": "",
    "addressLine2": "",
    "appointmentRequestDate": "",
    "city": "",
    "displayName": "",
    "customerNo": "",
    "customerID": "",
    "appointmentNo": "",
    "appointmentSystemID": "",
    "salesOrderNo": "",
    "salesOrderID": "",
    "email": "",
    "isConditionsChecked": "",
    "phoneNumber": "",
    "postalCode": "",
    "requestedHandymanSkill": "",
    "taskDescription": "",
    "customerType": ""
  }
}
