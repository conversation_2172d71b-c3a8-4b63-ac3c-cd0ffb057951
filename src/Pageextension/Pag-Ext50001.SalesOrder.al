namespace ITV.Appointments.Synchronization;

using Microsoft.Sales.Document;

pageextension 50001 "Sales Order" extends "Sales Order"
{
    layout
    {
        addafter(Status)
        {
            field("Requested Delivery Datetime"; Rec."Requested Delivery Datetime")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Requested Delivery Datetime field.', Comment = '%';
            }
            field("As Soon As Possible"; Rec."As Soon As Possible")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the As Soon As Possible field.', Comment = '%';
            }

            field("Sent to Handyman"; Rec."Sent to Handyman")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Sent to Handyman field.', Comment = '%';
            }
        }
    }
}
