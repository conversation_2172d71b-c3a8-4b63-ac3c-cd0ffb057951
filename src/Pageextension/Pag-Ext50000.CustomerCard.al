namespace appointments.appointments;

using Microsoft.Sales.Customer;

pageextension 50000 "Customer Card" extends "Customer Card"
{
    layout
    {
        addlast(General)
        {

            field("Is Conditions Checked"; Rec."Is Conditions Checked")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Is Conditions Checked field.', Comment = '%';
            }
            field("Type"; Rec."Type")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Type field.', Comment = '%';
            }
        }
    }
}
