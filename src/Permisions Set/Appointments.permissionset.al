namespace ITV.Appointments;

using ITV.Appointments;
using ITV.Appointments.Setup;
using ITV.Appointments.Creation;
using ITV.Appointments.Installation;
using appointments.appointments;
using ITV.Appointments.Handyman;
using ITV.Appointments.Synchronization;
using ITV.Appointments.Synchronization.API;
using ITV.Appointments.Synchronization.Setup;

permissionset 50000 Appointments
{
    Assignable = true;
    Permissions = tabledata "Order" = RIMD,
        tabledata "Appointment Requests" = RIMD,
        tabledata "Appointment Requests Archive" = RIMD,
        tabledata "Appointments Setup" = RIMD,
        table "Order" = X,
        table "Appointment Requests" = X,
        table "Appointment Requests Archive" = X,
        table "Appointments Setup" = X,
        codeunit "Appointments Mgt." = X,
        codeunit "Installation Codeunit" = X,
        page Orders = X,
        page "Appointment Requests" = X,
        page "Appointment Requests Archive" = X,
        page "Appointments Setup" = X,
        tabledata Handyman = RIMD,
        tabledata "Handyman Skills" = RIMD,
        tabledata "Synchronization Setup" = RIMD,
        table Handyman = X,
        table "Handyman Skills" = X,
        table "Synchronization Setup" = X,
        codeunit "Appointments API Actions" = X,
        codeunit "Handyman Synchronization" = X,
        codeunit "Http Handler" = X,
        page "Appointment Requests API" = X,
        page "Handyman List" = X,
        page "Handyman Skills" = X,
        page "Set API Key" = X,
        page "Synchronization Setup" = X,
        tabledata "Handyman Post Code" = RIMD,
        table "Handyman Post Code" = X,
        page "Handyman Post Codes" = X,
        page "Handyman Post Codes API" = X,
        page "Handyman API" = X,
        query "Handyman Query API" = X,
        tabledata "Requested Handymen" = RIMD,
        table "Requested Handymen" = X,
        page "Requested Handymen" = X,
        page "Requested Handymen API" = X,
        query "Handyman Appointment Query" = X;

}