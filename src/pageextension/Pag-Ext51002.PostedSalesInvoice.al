pageextension 50002 "Posted Sales Invoice Ext" extends "Posted Sales Invoice"
{
    layout
    {
        addlast(General)
        {
            group("Signature")
            {
                field("Customer Signature Hash"; Rec."Customer Signature Hash")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
            }
        }

        addlast(FactBoxes)
        {
            part(CustomerSignaturePart; "Posted Customer Signature")
            {
                ApplicationArea = All;
                SubPageLink = "No." = FIELD("No.");
                Caption = 'Customer Signature';
            }
        }
    }
}


page 50008 "Posted Customer Signature"
{
    PageType = CardPart;
    SourceTable = "Sales Invoice Header";

    layout
    {
        area(Content)
        {
            field(CustomerSignature; Rec."Customer Signature")
            {
                ApplicationArea = All;
                ShowCaption = false;
                Editable = false;
            }
        }
    }
}
