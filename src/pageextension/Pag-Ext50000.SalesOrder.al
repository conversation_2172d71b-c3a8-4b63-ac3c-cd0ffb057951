namespace ITV.HandymanApp.Data;

using Microsoft.Sales.Document;

pageextension 50000 "Sales Order" extends "Sales Order"
{
    layout
    {
        addafter(Status)
        {

            field("As Soon As Possible"; Rec."As Soon As Possible")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the As Soon As Possible field.', Comment = '%';
            }
            field("Requested Delivery Datetime"; Rec."Requested Delivery Datetime")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Requested Delivery Datetime field.', Comment = '%';
            }
            field("Customer Signature Hash"; Rec."Customer Signature Hash")
            {
                ApplicationArea = All;
                Editable = false;
            }
        }

        addlast(FactBoxes)
        {
            part(CustomerSignaturePart; "Customer Signature")
            {
                ApplicationArea = All;
                SubPageLink = "Document Type" = FIELD("Document Type"), "No." = FIELD("No.");
            }
        }
    }
}

page 50007 "Customer Signature"
{
    PageType = CardPart;
    SourceTable = "Sales Header";

    layout
    {
        area(Content)
        {
            field(CustomerSignature; Rec."Customer Signature")
            {
                ApplicationArea = All;
                ShowCaption = false;
                Editable = false;
            }
        }
    }
}
