namespace ITV.HandymanApp.Data;

using Microsoft.Sales.Document;
using Microsoft.Sales.History;

pageextension 50001 "Sales Invoice" extends "Sales Invoice"
{
    layout
    {
        addafter(Status)
        {

            field("As Soon As Possible"; Rec."As Soon As Possible")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the As Soon As Possible field.', Comment = '%';
            }
            field("Requested Delivery Datetime"; Rec."Requested Delivery Datetime")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Requested Delivery Datetime field.', Comment = '%';
            }
            field("Customer Signature Hash"; Rec."Customer Signature Hash")
            {
                ApplicationArea = All;
                Editable = false;
            }
        }
    }
}
