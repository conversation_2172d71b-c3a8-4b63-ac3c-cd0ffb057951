namespace ITV.Appointments.Handyman;



query 50000 "Handyman Query API"
{
    APIGroup = 'handyman';
    APIPublisher = 'itv';
    APIVersion = 'v2.0';
    EntityName = 'handymanQuery';
    EntitySetName = 'handymanQuery';
    QueryType = API;

    elements
    {
        dataitem(handyman; Handyman)
        {
            column(handymanSystemId; SystemId)
            {
            }
            column("loginEmail"; "LoginEmail")
            {
            }
            column(companyID; "Company ID")
            {
            }
            column(companyName; "Company Name")
            {
            }
            column(deviceToken; "Device Token")
            {
            }
            column(environmentName; "Environment Name")
            {
            }
            column(name; Name)
            {
            }
            column(outlookEMailAddress; "Outlook E-Mail Address")
            {
            }
            dataitem(handymanPostCode; "Handyman Post Code")
            {
                DataItemLink = "LoginEmail" = handyman.LoginEmail;
                SqlJoinType = InnerJoin;

                column(postCode; "Post Code")
                {
                }
                column(active; Active)
                {
                }
            }
        }
    }

    trigger OnBeforeOpen()
    begin

    end;
}
