namespace ITV.Appointments.Handyman;

using Microsoft.Sales.Document;

query 50001 "Handyman Appointment Query"
{
    APIGroup = 'handyman';
    APIPublisher = 'itv';
    APIVersion = 'v2.0';
    EntityName = 'handymanAppointmentQuery';
    EntitySetName = 'handymanAppointmentQuery';
    QueryType = API;

    elements
    {

        dataitem(requestedHandymen; "Requested Handymen")
        {

            column(systemCreatedAt; SystemCreatedAt)
            {
            }
            column(loginEmail; LoginEmail)
            {
            }
            column(requestedHandymanAppointmentNo; "Appointment No.")
            {
            }
            column(reason; Reason)
            {
            }
            column(handymenState; State)
            {
            }
            column(requestedHandymenSystemId; SystemId)
            {
            }

            dataitem(appointmentRequests; "Appointment Requests")
            {
                SqlJoinType = InnerJoin;
                DataItemLink = "No." = requestedHandymen."Appointment No.";
                column(asSoonAsPossible;
                "As Soon As Possible")
                {
                }
                column(confirmedHandyman; "Confirmed Handyman")
                {
                }
                column(customerNo; "Customer No.")
                {
                }
                column(handymanOrderNo; "Handyman Order No.")
                {
                }
                column(appointmentNo; "No.")
                {
                }
                column(requestedDeliveryDatetime; "Requested Delivery Datetime")
                {
                }

                column(requestedHandymanSkill; "Requested Handyman Skill")
                {
                }
                column(appointmentStatus; Status)
                {
                }
                column(appointmentSystemId; SystemId)
                {
                }
                column(taskDescription; "Task Description")
                {
                }
                dataitem(salesOrder; "Sales Header")
                {
                    SqlJoinType = InnerJoin;
                    DataItemLink = "No." = appointmentRequests."Sales Order No.";
                    DataItemTableFilter = "Document Type" = const("Order");


                    column(salesOrderNo; "No.")
                    {
                    }
                    column(documentType; "Document Type")
                    {
                    }
                    column(salesOrderSystemId; SystemId)
                    {
                    }
                    column(sellToName; "Sell-to Customer Name")
                    {
                    }
                    column(shipToName; "Ship-to Name")
                    {
                    }
                    column(addressLine1; "Ship-to Address")
                    {
                    }
                    column(addressLine2; "Ship-to Address 2")
                    {
                    }
                    column(city; "Ship-to City")
                    {
                    }
                    column(postCode; "Ship-to Post Code")
                    {
                    }
                    column(email; "Sell-to E-Mail")
                    {
                    }
                    column(sellToPhoneNumber; "Sell-to Phone No.")
                    {
                    }
                    column(shipToPhoneNumber; "Ship-to Phone No.")
                    {
                    }


                }
            }
        }

    }
    var
        Test2: Text;
}

