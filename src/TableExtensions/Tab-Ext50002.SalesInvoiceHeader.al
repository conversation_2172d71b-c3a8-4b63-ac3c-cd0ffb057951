namespace ITV.Appointments.Synchronization;

using Microsoft.Sales.History;

tableextension 50002 "Sales Invoice Header" extends "Sales Invoice Header"
{
    fields
    {
        field(50000; "Sent to Handyman"; Boolean)
        {
            Caption = 'Sent to Handyman';
            DataClassification = ToBeClassified;
            Editable = false;
        }
        field(50001; "As Soon As Possible"; Boolean)
        {
            Caption = 'As Soon As Possible';

        }
        field(50002; "Requested Delivery Datetime"; DateTime)
        {
            Caption = 'Requested Delivery Datetime';

        }
    }
}
