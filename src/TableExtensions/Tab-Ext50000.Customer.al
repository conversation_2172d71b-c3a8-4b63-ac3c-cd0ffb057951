namespace ITV.Appointments;

using Microsoft.Sales.Customer;
using ITV.Appointments.Creation;

tableextension 50000 Customer extends Customer
{
    fields
    {
        field(50000; "Type"; Enum "Customer Type")
        {
            Caption = 'Type';
            DataClassification = ToBeClassified;
        }
        field(50001; "Is Conditions Checked"; Boolean)
        {
            Caption = 'Is Conditions Checked';
            DataClassification = ToBeClassified;
        }

    }
}
