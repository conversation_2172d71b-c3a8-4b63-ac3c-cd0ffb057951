namespace ITV.Appointments.Synchronization;

using Microsoft.Sales.Document;

tableextension 50001 "Sales Header" extends "Sales Header"
{
    fields
    {
        field(50000; "Sent to Handyman"; Boolean)
        {
            Editable = false;
            Caption = 'Sent to Handyman';
            DataClassification = ToBeClassified;
        }
        field(50001; "As Soon As Possible"; Bo<PERSON>an)
        {
            Caption = 'As Soon As Possible';

        }
        field(50002; "Requested Delivery Datetime"; DateTime)
        {
            Caption = 'Requested Delivery Datetime';

        }
    }
}
