namespace ITV.Appointments;
using ITV.Appointments.Handyman;

codeunit 50002 "Appointments API Actions"
{

    procedure archiveAppointmentRequest(AppointmentSystemID: Guid): Boolean
    var
        Appointment: Record "Appointment Requests";
    begin
        Appointment.GetBySystemId(AppointmentSystemID);
        ArchiveAppointmentRequestFunction(Appointment);
        exit(true);
    end;

    procedure ArchiveAppointmentRequestFunction(var AppointmentRequest: Record "Appointment Requests")
    var
        AppointmentRequestArchive: Record "Appointment Requests Archive";
        RequestedHandymen: Record "Requested Handymen";
        Err1: Label 'Appointment in status %1 can not be archived';
    begin
        if not (AppointmentRequest.Status in [AppointmentRequest.Status::Canceled, AppointmentRequest.Status::Confirmed, AppointmentRequest.Status::"Timed Out", AppointmentRequest.Status::Rejected]) then
            Error(Err1, AppointmentRequest.Status);

        AppointmentRequestArchive.TransferFields(AppointmentRequest);
        AppointmentRequestArchive.Insert();

        RequestedHandymen.SetFilter("Appointment No.", '%1', AppointmentRequest."No.");
        RequestedHandymen.DeleteAll();

        AppointmentRequest.Delete();
    end;
}
