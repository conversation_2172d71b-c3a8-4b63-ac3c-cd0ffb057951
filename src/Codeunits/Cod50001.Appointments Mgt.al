namespace ITV.Appointments.Creation;
using ITV.Appointments;
using Microsoft.Sales.Customer;
using Microsoft.Sales.Document;
using ITV.Appointments.Setup;

codeunit 50001 "Appointments Mgt."
{
    procedure CreateAppointment(var Order: Record "Order")
    var
        AppointmentRequest: Record "Appointment Requests";
    begin
        AppointmentRequest."Customer No." := Order."Customer No.";
        AppointmentRequest."Sales Order No." := Order."Sales Order No.";
        AppointmentRequest."Requested Delivery Datetime" := Order."Appointment Request Date Time";
        AppointmentRequest."Requested Handyman Skill" := Order."Requested Handyman Skill";
        AppointmentRequest."As Soon As Possible" := Order."As Soon As Possible";
        AppointmentRequest."Task Description" := Order."Task Description";
        AppointmentRequest."Task Category" := Order."Task Category";
        AppointmentRequest.Insert(true);

        Order."Appointment System ID" := AppointmentRequest.SystemId;
        Order."Appointment No." := AppointmentRequest."No.";
    end;

    procedure CreateCustomer(var Order: Record "Order")
    var
        Customer: Record Customer;
        CustomerTemplate: Record "Customer Templ.";
        CustomerTemplMgt: Codeunit "Customer Templ. Mgt.";
        IsHandled: Boolean;
    begin
        if Customer.FindByEmail(Customer, Order."Email") then begin
            Order."Customer No." := Customer."No.";
            Order."Customer ID" := Customer.SystemId;
            exit;
        end;
        AppointmentSetup.FindFirst();
        case
            Order."Customer Type" of
            "Customer Type"::Company:
                CustomerTemplate.Get(AppointmentSetup."Customer Company Template");
            "Customer Type"::Person:
                CustomerTemplate.Get(AppointmentSetup."Customer Person Template");
        end;

        CustomerTemplMgt.CreateCustomerFromTemplate(Customer, IsHandled, CustomerTemplate.Code);
        Customer.Name := Order."Bill-to Name";
        Customer.Address := Order."Bill-to Address Line 1";
        Customer."Address 2" := Order."Bill-to Address Line 2";
        Customer.City := Order."Bill-to City";
        Customer."Post Code" := Order."Bill-to Postal Code";
        Customer."E-Mail" := Order."Bill-to Email";
        Customer."Phone No." := Order."Bill-to Phone Number";
        Customer."Is Conditions Checked" := Order."Is Conditions Checked";
        Customer.Type := Order."Customer Type";
        Customer.Modify();
        Order."Customer No." := Customer."No.";
        Order."Customer ID" := Customer.SystemId;
    end;


    procedure CreateSalesOrder(var Order: Record "Order")
    var
        SalesHeader: Record "Sales Header";
    begin
        SalesHeader."Document Type" := SalesHeader."Document Type"::Order;
        SalesHeader.Validate("Sell-to Customer No.", Order."Customer No.");
        SalesHeader.Validate("Posting Date", Today);
        SalesHeader.Validate("Document Date", Today);

        SalesHeader.Validate("Ship-to Name", Order."Ship-to Name");
        SalesHeader.Validate("Ship-to Address", Order."Ship-to Address Line 1");
        SalesHeader.Validate("Ship-to Address 2", Order."Ship-to Address Line 2");
        SalesHeader.Validate("Ship-to City", Order."Ship-to City");
        SalesHeader.Validate("Ship-to Post Code", Order."Ship-to Postal Code");
        SalesHeader.Validate("Ship-to Phone No.", Order."Ship-to Phone Number");

        SalesHeader.Validate("Bill-to Name", Order."Bill-to Name");
        SalesHeader.Validate("Bill-to Address", Order."Bill-to Address Line 1");
        SalesHeader.Validate("Bill-to Address 2", Order."Bill-to Address Line 2");
        SalesHeader.Validate("Bill-to City", Order."Bill-to City");
        SalesHeader.Validate("Bill-to Post Code", Order."Bill-to Postal Code");

        SalesHeader.Validate("Requested Delivery Datetime", Order."Appointment Request Date Time");
        SalesHeader.Validate("As Soon As Possible", Order."As Soon As Possible");
        SalesHeader.Insert(true);
        SalesHeader.SetWorkDescription(Order."Task Description");
        Order."Sales Order No." := SalesHeader."No.";
        Order."Sales Order ID" := SalesHeader.SystemId;
    end;



    var
        AppointmentSetup: Record "Appointments Setup";

}
