codeunit 50010 "TestApptReqTimeout"
{
    Subtype = Test;
    [Test]
    procedure TestTimeoutPendingRequests()
    var
        AppointmentsSetup: Record "Appointments Setup";
        AppointmentRequest: Record "Appointment Requests";
        TimeoutCU: Codeunit "Appointment Request Timeout";
        PendingRequestNo: Code[20];
        TimedOutRequestNo: Code[20];
        dtNow: DateTime;
        ActualTimeoutCount: Integer;
    begin
        // Clean up
        AppointmentsSetup.DeleteAll();
        AppointmentRequest.DeleteAll();

        // Insert setup with 10 minute timeout
        AppointmentsSetup.Init();
        AppointmentsSetup."Code" := 'DEFAULT';
        AppointmentsSetup."Appointment Request Timeout" := 10;
        AppointmentsSetup.Insert();

        dtNow := CurrentDateTime();

        // Insert a request that should NOT time out
        AppointmentRequest.Init();
        AppointmentRequest."No." := 'REQ1';
        AppointmentRequest."Requested Delivery Datetime" := dtNow;
        AppointmentRequest.Status := AppointmentRequest.Status::Pending;
        AppointmentRequest.Insert();
        PendingRequestNo := AppointmentRequest."No.";

        // Insert a request that SHOULD time out (older than 10 min)
        AppointmentRequest.Init();
        AppointmentRequest."No." := 'REQ2';
        AppointmentRequest."Requested Delivery Datetime" := dtNow - (11 * 60000);
        AppointmentRequest.Status := AppointmentRequest.Status::Pending;
        AppointmentRequest.Insert();
        TimedOutRequestNo := AppointmentRequest."No.";

        // Run the timeout logic
        ActualTimeoutCount := TimeoutCU.CheckAndTimeoutRequests();
        IntegerAreEqual(1, ActualTimeoutCount, 'One request should be timed out.');
        // Assert: REQ1 is still Pending
        AppointmentRequest.Get(PendingRequestNo);
        AreEqual(AppointmentRequest.Status::Pending, AppointmentRequest.Status, 'REQ1 should still be Pending');

        // Assert: REQ2 is Timed Out
        AppointmentRequest.Get(TimedOutRequestNo);
        AreEqual(AppointmentRequest.Status::"Timed Out", AppointmentRequest.Status, 'REQ2 should be Timed Out');
    end;

    procedure IntegerAreEqual(Expected: Integer; Actual: Integer; Message: Text)
    begin
        if Expected <> Actual then
            Error('%1 - Expected: %2, Actual: %3', Message, Format(Expected), Format(Actual));
    end;

    procedure AreEqual(Expected: Enum "Appointment Status"; Actual: Enum "Appointment Status"; Message: Text)
    begin
        if Expected <> Actual then
            Error('%1 - Expected: %2, Actual: %3', Message, Format(Expected), Format(Actual));
    end;
}
