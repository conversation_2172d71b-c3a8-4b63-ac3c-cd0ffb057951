namespace HandymanApp.HandymanApp;
using Microsoft.Sales.Document;
using System.Utilities;
using System.Text;

// This Codunit provides a method to set the customer signature and the signature hash on a given sales order.
// It decodes a Base64 payload because ODATA does not support binary data directly.
// The payload must be a JSON object with the following structure:
// {
//   "base64Payload": "ewogIC...QiCn0="
// }
//
// The Base64 payload should contain a JSON object with the following structure:
// {
//   "salesOrderNumber":  "101002",
//   "signatureBase64": "/9j/4AAQSkZJRgAB...AAAAAH//2Q==",
//   "signatureHash": "b3b0c44298fc1c149afbf4c8996fb924"
// }


codeunit 50030 "Customer Signature Upload"
{
    procedure UploadCustomerSignature(Base64Payload: Text): Boolean
    var
        SalesHeader: Record "Sales Header";
        TempBlob: Codeunit "Temp Blob";
        Base64Convert: Codeunit "Base64 Convert";
        InStr: InStream;
        OutStr: OutStream;
        PayloadBase64: Text;
        PayloadJson: JsonObject;
        SalesOrderNumberToken: JsonToken;
        SignatureBase64Token: JsonToken;
        SignatureHashToken: JsonToken;
        SalesOrderNumber: Code[20];
        SignatureBase64: Text;
        SignatureHash: Text;
    begin

        PayloadBase64 := Base64Convert.FromBase64(Base64Payload);

        if not PayloadJson.ReadFrom(PayloadBase64) then
            Error('Invalid JSON payload.');

        if not PayloadJson.Get('salesOrderNumber', SalesOrderNumberToken) then
            Error('Missing "salesOrderNumber" in payload.');

        if not PayloadJson.Get('signatureBase64', SignatureBase64Token) then
            Error('Missing "signatureBase64" in payload.');

        if not PayloadJson.Get('signatureHash', SignatureHashToken) then
            Error('Missing "signatureHash" in payload.');

        SalesOrderNumber := SalesOrderNumberToken.AsValue().AsText();
        SignatureBase64 := SignatureBase64Token.AsValue().AsText();
        SignatureHash := SignatureHashToken.AsValue().AsText();

        SalesHeader.Reset();
        SalesHeader.SetRange("No.", SalesOrderNumber);
        if not SalesHeader.FindFirst() then
            Error('Sales Order %1 not found.', SalesOrderNumber);

        TempBlob.CreateOutStream(OutStr);
        Base64Convert.FromBase64(SignatureBase64, OutStr);

        TempBlob.CreateInStream(InStr);
        SalesHeader."Customer Signature".ImportStream(InStr, 'signature.jpg');

        SalesHeader."Customer Signature Hash" := SignatureHash;

        SalesHeader.Modify(true);

        exit(true);
    end;
}
