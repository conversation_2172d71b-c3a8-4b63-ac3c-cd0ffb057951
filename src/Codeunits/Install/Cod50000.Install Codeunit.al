namespace ITV.Appointments.Installation;
using ITV.Appointments.Setup;
using ITV.Appointments.Synchronization;

codeunit 50000 "Installation Codeunit"
{
    Subtype = Install;

    trigger OnInstallAppPerCompany()
    var
        AppointmentSetup: Record "Appointments Setup";
        SynchronizationSetup: Record "Synchronization Setup";
        TimeoutJob: Codeunit "ApptReqTimeoutJob";
    begin
        if not AppointmentSetup.FindFirst() then
            AppointmentSetup.Insert();
        if not SynchronizationSetup.FindFirst() then
            SynchronizationSetup.Insert();
        TimeoutJob.RegisterJob();
    end;
}