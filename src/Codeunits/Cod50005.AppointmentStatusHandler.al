namespace ITV.Appointments;
using ITV.Appointments.Handyman;
codeunit 50005 "Appointment Status Handler"
{
    procedure UpdateAppointmentStatusIfAllRejected(AppointmentNo: Code[20])
    var
        RequestedHandymen: Record "Requested Handymen";
        AppointmentRequest: Record "Appointment Requests";
        AllRejected: Boolean;
    begin
        // Check if all Requested Handymen for the given Appointment No. are Rejected
        RequestedHandymen.SetRange("Appointment No.", AppointmentNo);
        AllRejected := true;

        if RequestedHandymen.FindSet() then
            repeat
                if RequestedHandymen.State <> RequestedHandymen.State::Rejected then begin
                    AllRejected := false;
                    break;
                end;
            until RequestedHandymen.Next() = 0;

        // If all are rejected, update the Appointment Request status to Rejected
        if AllRejected then begin
            if AppointmentRequest.Get(AppointmentNo) then begin
                AppointmentRequest.Status := AppointmentRequest.Status::Rejected;
                AppointmentRequest.Modify();
            end;
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Requested Handymen", 'OnAfterModifyEvent', '', true, true)]
    local procedure OnRequestedHandymenModified(var Rec: Record "Requested Handymen"; RunTrigger: Boolean)
    begin
        if Rec.State = Rec.State::Rejected then
            UpdateAppointmentStatusIfAllRejected(Rec."Appointment No.");
    end;
}