namespace ITV.Appointments.Synchronization;
using Microsoft.Sales.Customer;
using System.Security.Authentication;
using ITV.Appointments.Handyman;
using System.Azure.Identity;
using Microsoft.Sales.Document;
using System.RestClient;
using ITV.Appointments.Synchronization.API;

codeunit 50003 "Handyman Synchronization"
{
    procedure SendSalesOrder(var AppointmentRequest: Record "Appointment Requests"): JsonToken
    var
        Body: JsonObject;
        SalesHeader: Record "Sales Header";
        Handyman: Record Handyman;
        HttpResponse: Codeunit "Http Response Message";
        HttpContent: Codeunit "Http Content";
        ResponseToken: JsonToken;
    begin
        Clear(RestClient);
        AppointmentRequest.TestField("Confirmed Handyman");
        Handyman.Get(AppointmentRequest."Confirmed Handyman");
        Handyman.TestField("Company ID");
        SalesHeader.get(SalesHeader."Document Type"::Order, AppointmentRequest."Sales Order No.");
        SalesHeader.TestField("Sent to Handyman", false);
        Initialize();
        PrepareAuthorization();
        PrepareCustomerData(Body, SalesHeader."Sell-to Customer No.");
        PrepareSalesHeaderData(Body, SalesHeader);
        Handyman.Get(AppointmentRequest."Confirmed Handyman");
        HttpContent.Create(Body);
        HttpResponse := RestClient.Post(GetCustomerOrderCreationAPIPath(Handyman), HttpContent);
        if HttpResponse.GetIsSuccessStatusCode() then begin
            SalesHeader."Sent to Handyman" := true;
            SalesHeader.Modify();
            SetHandymanOrderNoOnAppointment(AppointmentRequest, HttpResponse.GetContent().AsJson());
            ResponseToken := HttpResponse.GetContent().AsJson();
            exit(ResponseToken);
        end else begin
            Error(HttpResponse.GetContent().AsText());
        end;
    end;

    local procedure SetHandymanOrderNoOnAppointment(var AppointmentRequest: record "Appointment Requests"; Response: JsonToken)
    var
        Jtoken: JsonToken;
    begin
        if Response.IsObject then
            if Response.AsObject().Get('salesOrderNo', Jtoken) then
                if Jtoken.IsValue then begin
                    AppointmentRequest."Handyman Order No." := Jtoken.AsValue().AsText();
                    AppointmentRequest.Modify();
                end;
    end;

    local procedure PrepareCustomerData(var Body: JsonObject; CustomerNo: Code[20])
    var
        Customer: Record Customer;
    begin
        Customer.get(CustomerNo);
        Body.Add('customerName', Customer.Name);
        Body.Add('customerID', Customer.SystemId);
        Body.Add('customerAddress', Customer.Address);
        Body.Add('customerAddress2', Customer."Address 2");
        Body.Add('customerCity', Customer.City);
        Body.Add('customerEMail', Customer."E-Mail");
        Body.Add('customerPhoneNo', Customer."Phone No.");
        Body.Add('customerPostCode', Customer."Post Code");
        Body.Add('genBusPostingGroup', Customer."Gen. Bus. Posting Group");
        Body.Add('customerPostingGroup', Customer."Customer Posting Group");
        Body.Add('customerPaymentTermsCode', Customer."Payment Terms Code");
        Body.Add('customerLanguageCode', Customer."Language Code");
        Body.Add('customerFormatRegion', Customer."Format Region");

    end;

    local procedure PrepareSalesHeaderData(var Body: JsonObject; var SalesHeader: Record "Sales Header")
    var
    begin
        Body.Add('shipToName', SalesHeader."Ship-to Name");
        Body.Add('shipToAddress', SalesHeader."Ship-to Address");
        Body.Add('shipToAddress2', SalesHeader."Ship-to Address 2");
        Body.Add('shipToCity', SalesHeader."Ship-to City");
        Body.Add('shipToPostCode', SalesHeader."Ship-to Post Code");
        Body.Add('shipToPhoneNo', SalesHeader."Ship-to Phone No.");

        Body.Add('billToName', SalesHeader."Bill-to Name");
        Body.Add('billToAddress', SalesHeader."Bill-to Address");
        Body.Add('billToAddress2', SalesHeader."Bill-to Address 2");
        Body.Add('billToCity', SalesHeader."Bill-to City");
        Body.Add('billToPostCode', SalesHeader."Bill-to Post Code");

        Body.Add('salesOrderID', SalesHeader.SystemId);
        Body.Add('workDescription', SalesHeader.GetWorkDescription());
        Body.Add('postingDate', SalesHeader."Posting Date");
        Body.Add('documentDate', SalesHeader."Document Date");
        if SalesHeader."Requested Delivery Datetime" <> 0DT then
            Body.Add('requestedDeliveryDatetime', SalesHeader."Requested Delivery Datetime");
        Body.Add('asSoonAsPossible', SalesHeader."As Soon As Possible");
    end;

    procedure PrepareAuthorization()
    var
        AuthSecret: SecretText;
    begin
        AuthSecret := SecretStrSubstNo('Bearer %1', GetToken());
        RestClient.SetAuthorizationHeader(AuthSecret);
    end;

    local procedure GetCustomerOrderCreationAPIPath(Handyman: Record Handyman): Text
    var
    begin
        exit(StrSubstNo('https://api.businesscentral.dynamics.com/v2.0/%1/api/ITV/handymanApp/v2.0/companies(%2)/customerOrderCreation', Handyman."Environment Name", DelChr(Format(Handyman."Company ID"), '=', '{}')));
    end;

    procedure GetToken(): SecretText;
    var
        Oauth: Codeunit OAuth2;
        Token: SecretText;
        SynchronizationSetup: Record "Synchronization Setup";
    begin
        SynchronizationSetup.FindFirst();
        // CheckClientSecret(false);
        Oauth.AcquireTokenWithClientCredentials(SynchronizationSetup."Entra Application Client ID", GetClientSecret(), GetAuthUrl(), '', 'https://api.businesscentral.dynamics.com/', Token);
        exit(Token);
    end;

    procedure SetClientSecret(ClientSecret: Text)
    var
        ClientSecretSecretText: SecretText;
    begin
        ClientSecretSecretText := ClientSecret;
        IsolatedStorage.Set('SynchClientSecret', ClientSecretSecretText, DataScope::Company);
    end;

    local procedure GetClientSecret(): SecretText
    var
        ClientSecret: SecretText;
    begin

        if IsolatedStorage.Get('SynchClientSecret', DataScope::Company, ClientSecret) then
            exit(ClientSecret);
    end;

    local procedure GetAuthUrl(): Text
    var
        AzureAdTenant: Codeunit "Azure AD Tenant";
    begin
        exit(StrSubStNo('https://login.microsoftonline.com/%1/oauth2/token', AzureAdTenant.GetAadTenantId()));
    end;

    procedure Initialize()
    var
        HttpClientHandler: Codeunit "Http Handler";
    begin
        RestClient.Initialize(HttpClientHandler);
    end;

    var
        RestClient: Codeunit "Rest Client";
}
