codeunit 50007 "ApptReqTimeoutJob"
{
    Subtype = Normal;
    SingleInstance = true;

    trigger OnRun()
    var
        TimeoutCU: Codeunit "Appointment Request Timeout";
    begin
        TimeoutCU.CheckAndTimeoutRequests();
    end;

    procedure RegisterJob()
    var
        JobQueueEntry: Record "Job Queue Entry";
    begin
        JobQueueEntry.SetRange(Description, 'Appointment Request Timeout');
        if not JobQueueEntry.FindFirst() then begin
            JobQueueEntry.Init();
            JobQueueEntry.Description := 'Appointment Request Timeout';
            JobQueueEntry."Object Type to Run" := JobQueueEntry."Object Type to Run"::Codeunit;
            JobQueueEntry."Object ID to Run" := CODEUNIT::"ApptReqTimeoutJob";
            JobQueueEntry."Earliest Start Date/Time" := CurrentDateTime();
            JobQueueEntry."No. of Minutes between Runs" := 1;
            JobQueueEntry.Status := JobQueueEntry.Status::Ready;
            JobQueueEntry."Scheduled" := true;
            JobQueueEntry."Recurring Job" := true;
            JobQueueEntry.Insert();
        end;
    end;
}
