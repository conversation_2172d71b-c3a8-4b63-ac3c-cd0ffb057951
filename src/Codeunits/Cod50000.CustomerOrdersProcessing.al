namespace HandymanApp.HandymanApp;
using ITV.Handyman.OrderCreation;
using Microsoft.Sales.Customer;
using Microsoft.Sales.Document;

codeunit 50000 "Customer Orders Processing"
{
    procedure ProcessRequest(var CustOrderCreation: Record "Customer Orders Creation"; WorkDescription: Text)
    var
        SalesHeader: Record "Sales Header";
    begin
        CreateCustomer(CustOrderCreation);
        CreateSalesOrder(CustOrderCreation, WorkDescription);
    end;

    local procedure CreateSalesOrder(var CustOrderCreation: Record "Customer Orders Creation"; WorkDescription: Text)
    var
        SalesHeader: Record "Sales Header";
    begin
        SalesHeader."Document Type" := SalesHeader."Document Type"::Order;
        SalesHeader.Validate("Sell-to Customer No.", CustOrderCreation."Customer No.");
        SalesHeader."Main System ID" := CustOrderCreation."Sales Order ID";
        SalesHeader.Validate("Posting Date", CustOrderCreation."Posting Date");
        SalesHeader.Validate("Document Date", CustOrderCreation."Document Date");
        SalesHeader."As Soon As Possible" := CustOrderCreation."As Soon As Possible";
        SalesHeader."Requested Delivery Datetime" := CustOrderCreation."Requested Delivery Datetime";

        SalesHeader.Validate("Ship-to Name", CustOrderCreation."Ship-to Name");
        SalesHeader.Validate("Ship-to Address", CustOrderCreation."Ship-to Address");
        SalesHeader.Validate("Ship-to Address 2", CustOrderCreation."Ship-to Address 2");
        SalesHeader.Validate("Ship-to City", CustOrderCreation."Ship-to City");
        SalesHeader.Validate("Ship-to Post Code", CustOrderCreation."Ship-to Post Code");
        SalesHeader.Validate("Ship-to Phone No.", CustOrderCreation."Ship-to Phone No.");

        SalesHeader.Validate("Bill-to Name", CustOrderCreation."Bill-to Name");
        SalesHeader.Validate("Bill-to Address", CustOrderCreation."Bill-to Address");
        SalesHeader.Validate("Bill-to Address 2", CustOrderCreation."Bill-to Address 2");
        SalesHeader.Validate("Bill-to City", CustOrderCreation."Bill-to City");
        SalesHeader.Validate("Bill-to Post Code", CustOrderCreation."Bill-to Post Code");

        SalesHeader.Insert(true);
        SalesHeader.SetWorkDescription(WorkDescription);
        CustOrderCreation."Sales Order No." := SalesHeader."No.";
        CustOrderCreation."Handyman Sales Order ID" := SalesHeader.SystemId;
    end;

    local procedure CreateCustomer(var CustOrderCreation: Record "Customer Orders Creation")
    var
        Customer: Record Customer;
    begin
        Customer.SetRange("Main System ID", CustOrderCreation."Customer ID");
        if Customer.FindFirst() then begin
            CustOrderCreation."Customer No." := Customer."No.";
            exit
        end;
        Customer.Init();
        Customer.Validate(Name, CustOrderCreation."Customer Name");
        Customer.Validate("Main System ID", CustOrderCreation."Customer ID");
        Customer.Validate("E-Mail", CustOrderCreation."Customer E-Mail");
        Customer.Validate(Address, CustOrderCreation."Customer Address");
        Customer.Validate("Address 2", CustOrderCreation."Customer Address 2");
        Customer.Validate("Post Code", CustOrderCreation."Customer Post Code");
        Customer.Validate("Phone No.", CustOrderCreation."Customer Phone No.");
        Customer.Validate("Gen. Bus. Posting Group", CustOrderCreation."Gen. Bus. Posting Group");
        Customer.Validate("Customer Posting Group", CustOrderCreation."Customer Posting Group");
        Customer.Validate("Payment Terms Code", CustOrderCreation."Customer Payment Terms Code");
        Customer.Validate("Language Code", CustOrderCreation."Customer Language Code");
        Customer.Validate("Format Region", CustOrderCreation."Customer Format Region");
        Customer.Validate(Type, CustOrderCreation.Type);
        Customer.Validate("Is Conditions Checked", CustOrderCreation."Is Conditions Checked");
        Customer.Insert(true);
        CustOrderCreation."Customer No." := Customer."No.";
    end;
}
