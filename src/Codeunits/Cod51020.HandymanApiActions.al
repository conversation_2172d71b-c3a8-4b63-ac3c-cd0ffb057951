namespace HandymanApp.HandymanApp;
using Microsoft.Sales.Document;
using System.Utilities;
using System.Text;

// This Codeunit provides API actions which are available via the ODATA endpoints.
// This Codeunit must be manually registered in the "Web Services" page to be accessible via ODATA.

codeunit 50020 "Handyman API Actions"
{
    [ServiceEnabled]
    procedure UploadSignature(Base64Payload: Text): Boolean
    var
        CustomerSignatureUpload: Codeunit "Customer Signature Upload";
    begin
        if not CustomerSignatureUpload.UploadCustomerSignature(Base64Payload) then
            Error('Failed to upload customer signature.');
        exit(true);
    end;
}
