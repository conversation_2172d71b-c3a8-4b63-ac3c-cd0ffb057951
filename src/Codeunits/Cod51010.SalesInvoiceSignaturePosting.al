// This Codeunit handles copying the customer signature from the Sales Header to the Sales Invoice Header during the posting process.

codeunit 50010 "Customer Signature Posting"
{
    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post", 'OnAfterSalesInvHeaderInsert', '', false, false)]
    local procedure OnAfterSalesInvHeaderInsert(var SalesInvHeader: Record "Sales Invoice Header"; SalesHeader: Record "Sales Header")
    var
        TempBlob: Codeunit "Temp Blob";
        OutStream: OutStream;
        InStream: InStream;
    begin
        if not SalesHeader."Customer Signature".HasValue() then
            exit;

        if SalesInvHeader.Get(SalesInvHeader."No.") then begin
            TempBlob.CreateOutStream(OutStream);
            SalesHeader."Customer Signature".ExportStream(OutStream);

            TempBlob.CreateInStream(InStream);

            SalesInvHeader."Customer Signature".ImportStream(InStream, 'CustomerSignature.png');

            // Hier kein Modify() nötig, da dieses Event innerhalb des Insert-Prozess getriggert wird.
        end;
    end;
}