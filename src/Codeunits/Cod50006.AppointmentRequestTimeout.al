codeunit 50006 "Appointment Request Timeout"
{
    SingleInstance = true;
    Subtype = Normal;

    procedure CheckAndTimeoutRequests() : Integer
    var
        AppointmentRequest: Record "Appointment Requests";
        AppointmentsSetup: Record "Appointments Setup";
        TimeoutMinutes: Integer;
        TimeoutDateTime: DateTime;
        TimedOutCount: Integer;
    begin
        if not AppointmentsSetup.FindFirst() then
            Error('The Timeout Minutes value is invalid. Please correct it in the Appointments Setup.');
        
        TimeoutMinutes := AppointmentsSetup."Appointment Request Timeout";
        if TimeoutMinutes <= 0 then
            Error('The Timeout Minutes value is invalid. Please correct it in the Appointments Setup.');
        
        TimeoutDateTime := CurrentDateTime() - (TimeoutMinutes * 60000);
        
        AppointmentRequest.SetRange(Status, AppointmentRequest.Status::Pending);
        AppointmentRequest.SetFilter(SystemCreatedAt, '<%1', TimeoutDateTime);

        TimedOutCount := 0;
        if AppointmentRequest.FindSet() then
            repeat
                AppointmentRequest.Status := AppointmentRequest.Status::"Timed Out";
                AppointmentRequest.Modify(true);
                TimedOutCount += 1;
            until AppointmentRequest.Next() = 0;

        exit(TimedOutCount);
    end;
}
