namespace appointments.appointments;

using ITV.Appointments;
using ITV.Appointments.Creation;

page 50001 "Orders"
{
    APIGroup = 'appointments';
    APIPublisher = 'itv';
    APIVersion = 'v2.0';
    ApplicationArea = All;
    Caption = 'orders';
    DelayedInsert = true;
    EntityName = 'order';
    EntitySetName = 'orders';
    PageType = API;
    SourceTable = Order;
    SourceTableTemporary = true;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(shipToName; Rec."Ship-to Name")
                {
                    Caption = 'Ship-to Name';
                }
                field(shipToAddressLine1; Rec."Ship-to Address Line 1")
                {
                    Caption = 'Ship-to Address Line 1';
                }
                field(shipToAddressLine2; Rec."Ship-to Address Line 2")
                {
                    Caption = 'Ship-to Address Line 2';
                }
                field(shipToCity; Rec."Ship-to City")
                {
                    Caption = 'Ship-to City';
                }
                field(shipToPostalCode; Rec."Ship-to Postal Code")
                {
                    Caption = 'Ship-to Postal Code';
                }
                field(shipToEmail; Rec."Ship-to Email")
                {
                    Caption = 'Ship-to Email';
                }
                field(shipToPhoneNumber; Rec."Ship-to Phone Number")
                {
                    Caption = 'Ship-to Phone Number';
                }
                field(billToName; Rec."Bill-to Name")
                {
                    Caption = 'Bill-to Name';
                }
                field(billToAddressLine1; Rec."Bill-to Address Line 1")
                {
                    Caption = 'Bill-to Address Line 1';
                }
                field(billToAddressLine2; Rec."Bill-to Address Line 2")
                {
                    Caption = 'Bill-to Address Line 2';
                }
                field(billToCity; Rec."Bill-to City")
                {
                    Caption = 'Bill-to City';
                }
                field(billToPostalCode; Rec."Bill-to Postal Code")
                {
                    Caption = 'Bill-to Postal Code';
                }
                field(billToEmail; Rec."Bill-to Email")
                {
                    Caption = 'Bill-to Email';
                }
                field(billToPhoneNumber; Rec."Bill-to Phone Number")
                {
                    Caption = 'Bill-to Phone Number';
                }
                field(email; Rec.Email)
                {
                    Caption = 'Email';
                }
                field(appointmentRequestDateTime; Rec."Appointment Request Date Time")
                {
                    Caption = 'Appointment Request Date Time';
                }
                field(customerNo; Rec."Customer No.")
                {
                    Caption = 'Customer No.';
                }
                field(customerID; Rec."Customer ID")
                {
                    Caption = 'Customer ID';
                }
                field(appointmentNo; Rec."Appointment No.")
                {
                    Caption = 'Appointment No.';
                }
                field(appointmentSystemID; Rec."Appointment System ID")
                {
                    Caption = 'Appointment ID';
                }
                field(salesOrderNo; Rec."Sales Order No.")
                {
                    Caption = 'Sales Order No.';
                }
                field(salesOrderID; Rec."Sales Order ID")
                {
                    Caption = 'Sales Order ID';
                }
                field(isConditionsChecked; Rec."Is Conditions Checked")
                {
                    Caption = 'Is Conditions Checked';
                }
                field(requestedHandymanSkill; Rec."Requested Handyman Skill")
                {
                    Caption = 'Requested Handyman Skill';
                }
                field(taskDescription; Rec."Task Description")
                {
                    Caption = 'Task Description';
                }
                field(taskCategory; Rec."Task Category")
                {
                    Caption = 'Task Category';
                }
                field("customerType"; Rec."Customer Type")
                {
                    Caption = 'Type';
                }
                field(asSoonAsPossible; Rec."As Soon As Possible")
                {
                    Caption = 'As Soon As Possible';
                }
            }
        }
    }
    trigger OnInsertRecord(BelowxRec: Boolean): Boolean
    var
    begin
        AppointmentMgt.CreateCustomer(Rec);
        AppointmentMgt.CreateSalesOrder(Rec);
        AppointmentMgt.CreateAppointment(Rec);
    end;

    var
        AppointmentMgt: Codeunit "Appointments Mgt.";

        TaskDescription: Text;
}
