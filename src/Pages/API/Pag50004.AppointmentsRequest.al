namespace ITV.Appointments;
using ITV.Appointments.Creation;
using ITV.Appointments.Synchronization;
using ITV.Appointments.Handyman;

page 50004 "Appointment Requests API"
{
    APIGroup = 'appointments';
    APIPublisher = 'itv';
    APIVersion = 'v2.0';
    ApplicationArea = All;
    Caption = 'appointment';
    DelayedInsert = true;
    EntityName = 'appointmentRequest';
    EntitySetName = 'appointmentRequests';
    PageType = API;
    SourceTable = "Appointment Requests";
    ODataKeyFields = SystemId;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(no; Rec."No.")
                {
                    Caption = 'No.';
                }

                field(systemId; Rec.SystemId)
                {
                    Caption = 'SystemId';
                }
                field(requestedDeliveryDatetime; Rec."Requested Delivery Datetime")
                {
                    Caption = 'Date';
                }

                field(status; Rec.Status)
                {
                    Caption = 'Status';
                }
                field(customerNo; Rec."Customer No.")
                {
                    Caption = 'Customer';
                }
                field(salesOrderNo; Rec."Sales Order No.")
                {
                    Caption = 'Sales Order No.';
                }
                field(confirmedHandyman; Rec."Confirmed Handyman")
                {
                    Caption = 'Confirmed Handyman';
                }
                field(asSoonAsPossible; Rec."As Soon As Possible")
                {
                    Caption = 'As Soon As Possible';
                }
                field(taskDescription; Rec."Task Description")
                {
                    Caption = 'Task Description';
                }
                field(taskCategory; Rec."Task Category")
                {
                    Caption = 'Task Category';
                }
            }
        }
    }

    [ServiceEnabled]
    procedure sendOrderToHandyman(): Text
    var
        SynchCodeunit: Codeunit "Handyman Synchronization";
        HandymanResponseToken: JsonToken;
        ResponseText: Text;
    begin
        HandymanResponseToken := SynchCodeunit.SendSalesOrder(Rec);
        HandymanResponseToken.WriteTo(ResponseText);
        exit(ResponseText);
    end;

    [ServiceEnabled]
    procedure assignHandymen(handymenArrayText: Text): text
    var
        RequestedHandymen: Record "Requested Handymen";
        HandymenArray: JsonArray;
        Jtoken: JsonToken;
    begin
        HandymenArray.ReadFrom(HandymenArrayText);
        foreach Jtoken in HandymenArray do begin
            Clear(RequestedHandymen);
            RequestedHandymen.Validate("Appointment No.", Rec."No.");
            RequestedHandymen.Validate(LoginEmail, Jtoken.AsValue().AsText());
            RequestedHandymen.Insert();
        end;
        Exit('Handymen assigned successfully');

    end;

}
