page 50004 "PostedSalesOrderInformation"
{
    PageType = API;
    SourceTable = "Sales Invoice Header";
    APIPublisher = 'itv';
    APIGroup = 'handymanApp';
    APIVersion = 'v2.0';
    EntityName = 'postedSalesOrderInformation';
    EntitySetName = 'postedSalesOrderInformationList';
    DelayedInsert = true;
    ApplicationArea = All;

    layout
    {
        area(content)
        {
            repeater(group)
            {
                field(orderNo; Rec."No.") { Caption = 'Order No.'; }
                field(salesOrderNo; Rec."Order No.") { Caption = 'Sales Order No.'; }
                field(asSoonAsPossible; Rec."As Soon As Possible") { }
                field(requestedDeliveryDatetime; Rec."Requested Delivery Datetime") { }
                field(sellToCustomerNo; Rec."Sell-to Customer No.") { }
                field(amountIncludingVAT; Rec."Amount Including VAT") { }
                field(amountExcludingVAT; Rec.Amount) { }

                field(sellToCustomerName; Rec."Sell-to Customer Name") { }
                field(sellToCustomerName2; Rec."Sell-to Customer Name 2") { }
                field(sellToAddress; Rec."Sell-to Address") { }
                field(sellToCity; Rec."Sell-to City") { }
                field(sellToPostCode; Rec."Sell-to Post Code") { }
                field(sellToPhoneNo; Rec."Sell-to Phone No.") { }
                field(sellToEmail; Rec."Sell-to E-Mail") { }

                field(shipToName; Rec."Ship-to Name") { }
                field(shipToAddress; Rec."Ship-to Address") { }
                field(shipToAddress2; Rec."Ship-to Address 2") { }
                field(shipToCity; Rec."Ship-to City") { }
                field(shipToPostCode; Rec."Ship-to Post Code") { }
                field(shipToPhoneNo; Rec."Ship-to Phone No.") { }

                field(billToName; Rec."Bill-to Name") { }
                field(billToAddress; Rec."Bill-to Address") { }
                field(billToAddress2; Rec."Bill-to Address 2") { }
                field(billToCity; Rec."Bill-to City") { }
                field(billToPostCode; Rec."Bill-to Post Code") { }

                field(systemId; Rec.SystemId) { }
                field(systemCreatedAt; Rec.SystemCreatedAt) { }
                field(workDescription; WorkDescriptionAsText)
                {
                    Caption = 'Work Description';
                }
            }

            part(SalesLines; "PostedSOInformationLine")
            {
                SubPageLink = "Document No." = field("No.");
            }

        }
    }

    var
        WorkDescriptionAsText: Text[10000];

    trigger OnAfterGetRecord()
    begin
        Clear(WorkDescriptionAsText);
        WorkDescriptionAsText := Rec.GetWorkDescription();
    end;
}
