namespace ITV.Appointments.Handyman;

page 50009 "Handyman Post Codes API"
{
    APIGroup = 'handyman';
    APIPublisher = 'itv';
    APIVersion = 'v2.0';
    ApplicationArea = All;
    Caption = 'handymanPostCodesAPI';
    DelayedInsert = true;
    EntityName = 'handymanPostCode';
    EntitySetName = 'handymanPostCodes';
    PageType = API;
    SourceTable = "Handyman Post Code";
    ODataKeyFields = SystemId;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(active; Rec.Active)
                {
                    Caption = 'Active';
                }
                field(loginEmail; Rec.LoginEmail)
                {
                    Caption = 'LoginEmail';
                }
                field(postCode; Rec."Post Code")
                {
                    Caption = 'Postal Code';
                }
                field(systemCreatedAt; Rec.SystemCreatedAt)
                {
                    Caption = 'SystemCreatedAt';
                }
                field(systemCreatedBy; Rec.SystemCreatedBy)
                {
                    Caption = 'SystemCreatedBy';
                }
                field(systemId; Rec.SystemId)
                {
                    Caption = 'SystemId';
                }
                field(systemModifiedAt; Rec.SystemModifiedAt)
                {
                    Caption = 'SystemModifiedAt';
                }
                field(systemModifiedBy; Rec.SystemModifiedBy)
                {
                    Caption = 'SystemModifiedBy';
                }
            }
        }
    }

}
