namespace ITV.Appointments.Handyman;


page 50010 "Handyman API"
{
    APIGroup = 'handyman';
    APIPublisher = 'itv';
    APIVersion = 'v2.0';
    ApplicationArea = All;
    Caption = 'handymanAPI';
    DelayedInsert = true;
    EntityName = 'handyman';
    EntitySetName = 'handyman';
    PageType = API;
    SourceTable = Handyman;
    ODataKeyFields = SystemId;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(companyID; Rec."Company ID")
                {
                    Caption = 'Company ID';
                }
                field(companyName; Rec."Company Name")
                {
                    Caption = 'Company Name';
                }
                field(deviceToken; Rec."Device Token")
                {
                    Caption = 'Device Token';
                }
                field(environmentName; Rec."Environment Name")
                {
                    Caption = 'Environment Name';
                }
                field(name; Rec.Name)
                {
                    Caption = 'Name';
                }
                field(outlookEMailAddress; Rec."Outlook E-Mail Address")
                {
                    Caption = 'Outlook E-Mail Address';
                }
                field(loginEmail; Rec.LoginEmail)
                {
                    Caption = 'Login E-Mail Address';
                }
                field(systemCreatedAt; Rec.SystemCreatedAt)
                {
                    Caption = 'SystemCreatedAt';
                }
                field(systemCreatedBy; Rec.SystemCreatedBy)
                {
                    Caption = 'SystemCreatedBy';
                }
                field(systemId; Rec.SystemId)
                {
                    Caption = 'SystemId';
                }
                field(systemModifiedAt; Rec.SystemModifiedAt)
                {
                    Caption = 'SystemModifiedAt';
                }
                field(systemModifiedBy; Rec.SystemModifiedBy)
                {
                    Caption = 'SystemModifiedBy';
                }
            }
        }
    }
}
