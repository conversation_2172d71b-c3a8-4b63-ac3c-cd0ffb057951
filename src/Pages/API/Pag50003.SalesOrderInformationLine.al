page 50003 "SalesOrderInformationLine"
{
    PageType = API;
    SourceTable = "Sales Line";
    APIPublisher = 'itv';
    APIGroup = 'handymanApp';
    APIVersion = 'v2.0';
    EntityName = 'salesOrderLine';
    EntitySetName = 'salesOrderLines';
    DelayedInsert = true;
    ApplicationArea = All;

    layout
    {
        area(content)
        {
            repeater(group)
            {
                field(systemId; Rec.SystemId) { }
                field(lineNo; Rec."Line No.") { }
                field(type; Rec.Type) { }
                field(no; Rec."No.") { }
                field(description; Rec.Description) { }
                field(quantity; Rec.Quantity) { }
                field(unitOfMeasure; Rec."Unit of Measure") { }
                field(unitPrice; Rec."Unit Price") { }
                field(amount; Rec.Amount) { }
                field(vatProdPostingGroup; Rec."VAT Prod. Posting Group") { }
                field(vatPercent; Rec."VAT %") { }
                field(vatAmount; Rec."Amount Including VAT") { }
            }
        }
    }
}