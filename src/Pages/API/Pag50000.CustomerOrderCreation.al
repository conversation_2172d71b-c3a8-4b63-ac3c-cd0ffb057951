namespace ITV.HandymanApp;

using ITV.Handyman.OrderCreation;
using Microsoft.Sales.Customer;
using HandymanApp.HandymanApp;

page 50000 "Customer Order Creation"
{
    APIGroup = 'handymanApp';
    APIPublisher = 'itv';
    APIVersion = 'v2.0';
    ApplicationArea = All;
    Caption = 'customerOrderCreation';
    DelayedInsert = true;
    EntityName = 'customerOrderCreation';
    EntitySetName = 'customerOrderCreation';
    PageType = API;
    SourceTable = "Customer Orders Creation";
    SourceTableTemporary = true;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(customerID; Rec."Customer ID")
                {
                    Caption = 'Customer ID';
                }
                field(customerName; Rec."Customer Name")
                {
                    Caption = 'Customer Name';
                }
                field(customerNo; Rec."Customer No.")
                {
                    Caption = 'Customer No.';
                }
                field(id; Rec.ID)
                {
                    Caption = 'ID';
                }
                field(salesOrderID; Rec."Sales Order ID")
                {
                    Caption = 'Sales Order ID';
                }
                field(salesOrderNo; Rec."Sales Order No.")
                {
                    Caption = 'Sales Order No.';
                }
                field(customerAddress; Rec."Customer Address")
                {
                    Caption = 'Address';
                }
                field(customerCity; Rec."Customer City")
                {
                    Caption = 'City';
                }
                field(customerAddress2; Rec."Customer Address 2")
                {
                    Caption = 'Address 2';
                }
                field(customerEMail; Rec."Customer E-Mail")
                {
                    Caption = 'E-Mail';
                }
                field(customerPhoneNo; Rec."Customer Phone No.")
                {
                    Caption = 'Phone No.';
                }
                field(customerPostCode; Rec."Customer Post Code")
                {
                    Caption = 'Post Code';
                }
                field(customerPostingGroup; Rec."Customer Posting Group")
                {
                    Caption = 'Customer Posting Group';
                }
                field(customerPaymentTermsCode; Rec."Customer Payment Terms Code")
                {
                    Caption = 'Customer Payment Terms Code';
                }
                field(customerLanguageCode; Rec."Customer Language Code")
                {
                    Caption = 'Customer Language Code';
                }
                field(customerFormatRegion; Rec."Customer Format Region")
                {
                    Caption = 'Customer Format Region';
                }
                field(genBusPostingGroup; Rec."Gen. Bus. Posting Group")
                {
                    Caption = 'Gen. Bus. Posting Group';
                }
                field(workDescription; WorkDescription)
                {
                    Caption = 'Work Description';
                }
                field(postingDate; Rec."Posting Date")
                {
                    Caption = 'Posting Date';
                }
                field(documentDate; Rec."Document Date")
                {
                    Caption = 'Document Date';
                }
                field("type"; Rec."Type")
                {
                    Caption = 'Type';
                }
                field(isConditionsChecked; Rec."Is Conditions Checked")
                {
                    Caption = 'Is Conditions Checked';
                }
                field(asSoonAsPossible; Rec."As Soon As Possible")
                {
                    Caption = 'As Soon As Possible';
                }
                field(requestedDeliveryDatetime; Rec."Requested Delivery Datetime")
                {
                    Caption = 'Requested Delivery Datetime';
                }
                field(handymanSalesOrderId; Rec."Handyman Sales Order ID")
                {
                    Caption = 'Handyman Sales Order ID';
                }
                field(companyId; CompanyProperty.ID())
                {
                    Caption = 'Company ID';
                }
                field(shipToName; Rec."Ship-to Name")
                {
                    Caption = 'Ship-to Name';
                }
                field(shipToAddress; Rec."Ship-to Address")
                {
                    Caption = 'Ship-to Address';
                }
                field(shipToAddress2; Rec."Ship-to Address 2")
                {
                    Caption = 'Ship-to Address 2';
                }
                field(shipToCity; Rec."Ship-to City")
                {
                    Caption = 'Ship-to City';
                }
                field(shipToPostCode; Rec."Ship-to Post Code")
                {
                    Caption = 'Ship-to Post Code';
                }
                field(shipToPhoneNo; Rec."Ship-to Phone No.")
                {
                    Caption = 'Ship-to Phone No.';
                }
                field(billToName; Rec."Bill-to Name")
                {
                    Caption = 'Bill-to Name';
                }
                field(billToAddress; Rec."Bill-to Address")
                {
                    Caption = 'Bill-to Address';
                }
                field(billToAddress2; Rec."Bill-to Address 2")
                {
                    Caption = 'Bill-to Address 2';
                }
                field(billToCity; Rec."Bill-to City")
                {
                    Caption = 'Bill-to City';
                }
                field(billToPostCode; Rec."Bill-to Post Code")
                {
                    Caption = 'Bill-to Post Code';
                }

            }
        }
    }
    trigger OnInsertRecord(BelowxRec: Boolean): Boolean
    var
        CustomerOrderProcessing: Codeunit "Customer Orders Processing";
    begin
        CustomerOrderProcessing.ProcessRequest(Rec, WorkDescription);
    end;

    var
        WorkDescription: Text;
}
