namespace ITV.Appointments.Handyman;


page 50011 "Requested Handymen API"
{
    APIGroup = 'handyman';
    APIPublisher = 'itv';
    APIVersion = 'v2.0';
    ApplicationArea = All;
    Caption = 'requestedHandymenAPI';
    DelayedInsert = true;
    EntityName = 'requestedHandymen';
    EntitySetName = 'requestedHandymen';
    PageType = API;
    SourceTable = "Requested Handymen";
    ODataKeyFields = SystemId;
    layout
    {
        area(Content)
        {
            repeater(General)
            {

                field(systemId; Rec.SystemId)
                {
                    Caption = 'SystemId';
                }
                field(appointmentNo; Rec."Appointment No.")
                {
                    Caption = 'Appointment No.';
                }
                field(loginEmail; Rec.LoginEmail)
                {
                    Caption = 'LoginEmail';
                }
                field(reason; Rec.Reason)
                {
                    Caption = 'Reason';
                }
                field(state; Rec.State)
                {
                    Caption = 'State';
                }
            }
        }
    }
}
