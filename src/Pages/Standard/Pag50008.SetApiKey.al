namespace ITV.Appointments.Synchronization;
page 50028 "Set API Key"
{
    PageType = Card;
    layout
    {
        area(Content)
        {
            field(APIKey; ApiKey)
            {
                ApplicationArea = all;
                ExtendedDatatype = Masked;
            }
        }
    }

    procedure GetApiKey(): Text;
    begin
        exit(APIKey);
    end;

    var
        APIKey: Text;
}