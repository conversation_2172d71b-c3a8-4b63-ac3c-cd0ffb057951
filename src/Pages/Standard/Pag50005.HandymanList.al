namespace appointments.appointments;

using ITV.Appointments.Handyman;

page 50005 "Handyman List"
{
    ApplicationArea = All;
    Caption = 'Handyman List';
    PageType = List;
    SourceTable = Handyman;
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("LoginEmail"; Rec.LoginEmail)
                {
                    ToolTip = 'Specifies the value of the Login E-Mail Address field.', Comment = '%';
                }
                field(Name; Rec.Name)
                {
                    ToolTip = 'Specifies the value of the Name field.', Comment = '%';
                }
                field("Environment Name"; Rec."Environment Name")
                {
                    ToolTip = 'Specifies the value of the Environment Name field.', Comment = '%';
                }
                field("Company Name"; Rec."Company Name")
                {
                    ToolTip = 'Specifies the value of the Company Name field.', Comment = '%';
                }
                field("Company ID"; Rec."Company ID")
                {
                    ToolTip = 'Specifies the value of the Company ID field.', Comment = '%';
                }
                field("Outlook E-Mail Address"; Rec."Outlook E-Mail Address")
                {
                    ToolTip = 'Specifies the value of the Outlook E-Mail Address field.', Comment = '%';
                }
                field("Device Token"; Rec."Device Token")
                {
                    ToolTip = 'Specifies the value of the Device Token field.', Comment = '%';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action("Handyman Post Codes")
            {
                ApplicationArea = All;
                Caption = 'Handyman Post Codes';
                RunObject = page "Handyman Post Codes";
                RunPageLink = LoginEmail = FIELD(LoginEmail);
                Image = Addresses;
            }

        }
        area(Promoted)
        {
            actionref(HandymanPostCodesRef; "Handyman Post Codes")
            {

            }
        }
    }
}

