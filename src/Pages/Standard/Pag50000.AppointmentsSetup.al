namespace ITV.Appointments.Setup;


page 50000 "Appointments Setup"
{
    ApplicationArea = All;
    Caption = 'Appointments Setup';
    PageType = Card;
    SourceTable = "Appointments Setup";
    InsertAllowed = false;
    DeleteAllowed = false;
    UsageCategory = Administration;
    layout
    {
        area(Content)
        {
            group(Customers)
            {
                Caption = 'Customers';

                field("Customer Company Template"; Rec."Customer Company Template")
                {

                }
                field("Customer Person Template"; Rec."Customer Person Template")
                {

                }

            }
            group(Appointments)
            {
                field("Appointment No. Series"; Rec."Appointment No. Series")
                {
                    ToolTip = 'Specifies the value of the Appointment No. Series field.', Comment = '%';
                }
                field("Appointment Request Timeout"; Rec."Appointment Request Timeout")
                {
                    ToolTip = 'Specifies the number of minutes after which an appointment request times out. Please make sure this value aligns to the values in the backend and customer website.';
                }
            }
        }
    }
}
