namespace ITV.Appointments;
using Microsoft.Sales.Document;
using ITV.Appointments.Setup;
using ITV.Appointments.Synchronization;
using ITV.Appointments.Handyman;
using Microsoft.Foundation.NoSeries;

page 50002 "Appointment Requests"
{
    ApplicationArea = All;
    Caption = 'Appointment Requests';
    PageType = List;
    SourceTable = "Appointment Requests";
    UsageCategory = Lists;
    Editable = true;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the value of the No. field.', Comment = '%';
                }
                field("Requested Delivery Datetime"; Rec."Requested Delivery Datetime")
                {
                    ToolTip = 'Specifies the value of the Date field.', Comment = '%';
                }
                field("As Soon As Possible"; Rec."As Soon As Possible")
                {
                    ToolTip = 'Specifies the value of the As Soon As Possible field.', Comment = '%';
                }
                field(Status; Rec.Status)
                {
                    ToolTip = 'Specifies the value of the Status field.', Comment = '%';
                }
                field("Customer No."; Rec."Customer No.")
                {
                    ToolTip = 'Specifies the value of the Customer field.', Comment = '%';
                }
                field("Sales Order No."; Rec."Sales Order No.")
                {
                    ToolTip = 'Specifies the value of the Sales Order No. field.', Comment = '%';
                    trigger OnDrillDown()
                    var
                        SalesHeader: Record "Sales Header";
                        SalesOrder: Page "Sales Order";
                    begin
                        SalesHeader.get(SalesHeader."Document Type"::Order, Rec."Sales Order No.");
                        SalesOrder.SetRecord(SalesHeader);
                        SalesOrder.RunModal();
                    end;
                }
                field("Handyman Order No."; Rec."Handyman Order No.")
                {
                    ToolTip = 'Specifies the value of the Handyman Order No. field.', Comment = '%';
                }
                field("Requested Handyman Skill"; Rec."Requested Handyman Skill")
                {
                    ToolTip = 'Specifies the value of the Requested Handyman Skill field.', Comment = '%';
                }
                field("Confirmed Handyman"; Rec."Confirmed Handyman")
                {
                    ToolTip = 'Specifies the value of the Confirmed Handyman field.', Comment = '%';
                }
                field("System Id"; Rec."SystemId")
                {
                    ToolTip = 'Specifies the value of the SystemId field.', Comment = '%';
                }
                field("Task Category"; Rec."Task Category")
                {
                    ToolTip = 'Specifies the value of the Task Category.', Comment = '%';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Archive)
            {
                Caption = 'Archive Appointment';
                trigger OnAction()
                var
                    AppointmentsAction: Codeunit "Appointments API Actions";
                begin
                    AppointmentsAction.ArchiveAppointmentRequestFunction(Rec);
                end;
            }
            action(SendOrderToHandyman)
            {
                Caption = 'Send Order to Handyman';
                trigger OnAction()
                var
                    SynchronizationCodeunit: Codeunit "Handyman Synchronization";
                begin
                    SynchronizationCodeunit.SendSalesOrder(Rec);
                end;
            }


            action("Requested Handymen")
            {
                ApplicationArea = All;
                Caption = 'Requested Handymen';
                RunObject = page "Requested Handymen";
                RunPageLink = "Appointment No." = FIELD("No.");
                Image = Employee;
            }
        }

        area(Promoted)
        {
            actionref("Requested Handymen Ref"; "Requested Handymen")
            {

            }
        }
    }

}
