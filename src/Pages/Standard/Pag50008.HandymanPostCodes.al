namespace ITV.Appointments.Handyman;

page 50008 "Handyman Post Codes"
{
    ApplicationArea = All;
    Caption = 'Handyman Post Codes';
    PageType = List;
    SourceTable = "Handyman Post Code";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(Active; Rec.Active)
                {
                }
                field("Post Code"; Rec."Post Code")
                {
                }
            }
        }
    }
}
