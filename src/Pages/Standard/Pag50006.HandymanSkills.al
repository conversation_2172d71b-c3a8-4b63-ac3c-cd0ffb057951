namespace appointments.appointments;

using ITV.Appointments.Handyman;

page 50006 "Handyman Skills"
{
    ApplicationArea = All;
    Caption = 'Handyman Skills';
    PageType = List;
    SourceTable = "Handyman Skills";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("LoginEmail"; Rec.LoginEmail)
                {
                }
                field(Description; Rec.Description)
                {
                }
            }
        }
    }
}
