namespace ITV.Appointments.Handyman;

using ITV.Appointments.Handyman;

page 50012 "Requested Handymen"
{
    ApplicationArea = All;
    Caption = 'Requested Handymen';
    PageType = List;
    SourceTable = "Requested Handymen";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("LoginEmail"; Rec.LoginEmail)
                {
                }
                field(State; Rec.State)
                {
                }
                field(Reason; Rec.Reason)
                {
                }

            }
        }
    }
}
