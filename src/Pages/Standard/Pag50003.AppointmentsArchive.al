namespace ITV.Appointments;

page 50003 "Appointment Requests Archive"
{
    ApplicationArea = All;
    Caption = 'Appointments Requests Archive';
    PageType = List;
    SourceTable = "Appointment Requests Archive";
    UsageCategory = Lists;
    Editable = false;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the value of the No. field.', Comment = '%';
                }
                field("Requested Delivery Datetime"; Rec."Requested Delivery Datetime")
                {
                    ToolTip = 'Specifies the value of the Date field.', Comment = '%';
                }
                field("As Soon As Possible"; Rec."As Soon As Possible")
                {
                    ToolTip = 'Specifies the value of the As Soon As Possible field.', Comment = '%';
                }


                field(Status; Rec.Status)
                {
                    ToolTip = 'Specifies the value of the Status field.', Comment = '%';
                }
                field(Customer; Rec.Customer)
                {
                    ToolTip = 'Specifies the value of the Customer field.', Comment = '%';
                }
                field("Sales Order No."; Rec."Sales Order No.")
                {
                    ToolTip = 'Specifies the value of the Sales Order No. field.', Comment = '%';
                }
                field("Handyman Order No."; Rec."Handyman Order No.")
                {
                    ToolTip = 'Specifies the value of the Handyman Order No. field.', Comment = '%';
                }
                field("Confirmed Handyman"; Rec."Confirmed Handyman")
                {
                    ToolTip = 'Specifies the value of the Confirmed Handyman field.', Comment = '%';
                }
            }
        }
    }
}
