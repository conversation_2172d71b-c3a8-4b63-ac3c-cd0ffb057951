namespace ITV.Appointments.Synchronization.Setup;

using ITV.Appointments.Synchronization;

page 50007 "Synchronization Setup"
{
    ApplicationArea = All;
    Caption = 'Synchronization Setup';
    PageType = Card;
    SourceTable = "Synchronization Setup";
    InsertAllowed = false;
    DeleteAllowed = false;
    UsageCategory = Administration;
    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("Entra Application Client ID"; Rec."Entra Application Client ID")
                {
                    ToolTip = 'Specifies the value of the Entra Application field.', Comment = '%';
                }
                field("Client Secret Expiration Date"; Rec."Client Secret Expiration Date")
                {
                    ToolTip = 'Specifies the value of the Client Secret Expiration Date field.', Comment = '%';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(SetAPIClientSecret)
            {
                ApplicationArea = all;
                Caption = 'Set API Client Secret';
                trigger OnAction()
                var
                    SynchronizationCodeunit: Codeunit "Handyman Synchronization";
                    SetApiKey: Page "Set API Key";
                begin
                    SetApiKey.LookupMode(true);
                    if SetApiKey.RunModal() = Action::LookupOK then
                        SynchronizationCodeunit.SetClientSecret(SetApiKey.GetApiKey());
                end;
            }
        }
    }
}
