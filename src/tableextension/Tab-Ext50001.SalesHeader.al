namespace ITV.HandymanApp.Data;

using Microsoft.Sales.Document;

tableextension 50001 "Sales Header" extends "Sales Header"
{
    fields
    {
        field(50000; "Main System ID"; Guid)
        {
            Caption = 'Main System ID';
            DataClassification = ToBeClassified;
        }
        field(50001; "As Soon As Possible"; Boolean)
        {
            Caption = 'As Soon As Possible';

        }
        field(50002; "Requested Delivery Datetime"; DateTime)
        {
            Caption = 'Requested Delivery Datetime';

        }
        field(50003; "Customer Signature"; Media)
        {
            Caption = 'Customer Signature';
        }
        field(50004; "Customer Signature Hash"; Text[64])
        {
            Caption = 'Customer Signature Hash';
        }
    }
}
