namespace ITV.HandymanApp.Data;

using Microsoft.Sales.Customer;
using ITV.HandymanApp;

tableextension 50000 Customer extends Customer
{
    fields
    {
        field(50000; "Main System ID"; Guid)
        {
            Caption = 'Main System ID';
            DataClassification = ToBeClassified;
        }
        field(50001; "Type"; Enum "Customer Type")
        {
            Caption = 'Type';
            DataClassification = ToBeClassified;
        }
        field(50002; "Is Conditions Checked"; Boolean)
        {
            Caption = 'Is Conditions Checked';
            DataClassification = ToBeClassified;
        }
    }
}
