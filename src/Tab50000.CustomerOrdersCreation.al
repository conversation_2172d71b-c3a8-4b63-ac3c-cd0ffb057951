namespace ITV.Handyman.OrderCreation;

using ITV.HandymanApp;
table 50000 "Customer Orders Creation"
{
    Caption = '"Customer Orders Creation"';
    DataClassification = ToBeClassified;
    TableType = Temporary;
    fields
    {
        field(1; ID; Integer)
        {
            Caption = 'ID';
        }
        field(2; "Customer ID"; Guid)
        {
            Caption = 'Customer ID';
        }
        field(3; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
        }
        field(4; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
        }
        field(5; "Sales Order ID"; Guid)
        {
            Caption = 'Sales Order ID';
        }
        field(21; "Handyman Sales Order ID"; Guid)
        {
            Caption = 'Handyman Sales Order ID';
        }
        field(6; "Sales Order No."; Code[20])
        {
            Caption = 'Sales Order No.';
        }
        field(7; "Customer Address"; Text[100])
        {
            Caption = 'Address';
        }
        field(8; "Customer Address 2"; Text[100])
        {
            Caption = 'Address 2';
        }
        field(9; "Customer City"; Text[50])
        {
            Caption = 'City';
        }
        field(10; "Customer Post Code"; Code[20])
        {
            Caption = 'Post Code';
        }
        field(11; "Customer E-Mail"; Text[80])
        {
            Caption = 'E-Mail';
        }
        field(12; "Customer Phone No."; Text[30])
        {
            Caption = 'Phone No.';
        }
        field(13; "Customer Posting Group"; Code[20])
        {
            Caption = 'Customer Posting Group';

        }
        field(14; "Gen. Bus. Posting Group"; Code[20])
        {
            Caption = 'Gen. Bus. Posting Group';

        }
        field(15; "Posting Date"; Date)
        {
            Caption = 'Posting Date';

        }
        field(16; "Document Date"; Date)
        {
            Caption = 'Document Date';

        }
        field(17; "Type"; Enum "Customer Type")
        {
            Caption = 'Type';
        }
        field(18; "Is Conditions Checked"; Boolean)
        {
            Caption = 'Is Conditions Checked';
        }
        field(19; "As Soon As Possible"; Boolean)
        {
            Caption = 'As Soon As Possible';

        }
        field(20; "Requested Delivery Datetime"; DateTime)
        {
            Caption = 'Requested Delivery Datetime';

        }
        field(22; "Ship-to Name"; Text[100])
        {
            Caption = 'Ship-to Name';
        }
        field(23; "Ship-to Address"; Text[100])
        {
            Caption = 'Ship-to Address';
        }
        field(24; "Ship-to Address 2"; Text[100])
        {
            Caption = 'Ship-to Address 2';
        }
        field(25; "Ship-to City"; Text[50])
        {
            Caption = 'Ship-to City';
        }
        field(26; "Ship-to Post Code"; Code[20])
        {
            Caption = 'Ship-to Post Code';
        }
        field(27; "Ship-to Phone No."; Text[30])
        {
            Caption = 'Ship-to Phone No.';
        }
        field(28; "Bill-to Name"; Text[100])
        {
            Caption = 'Bill-to Name';
        }
        field(29; "Bill-to Address"; Text[100])
        {
            Caption = 'Bill-to Address';
        }
        field(30; "Bill-to Address 2"; Text[100])
        {
            Caption = 'Bill-to Address 2';
        }
        field(31; "Bill-to City"; Text[50])
        {
            Caption = 'Bill-to City';
        }
        field(32; "Bill-to Post Code"; Code[20])
        {
            Caption = 'Bill-to Post Code';
        }
        field(33; "Customer Payment Terms Code"; Code[20])
        {
            Caption = 'Customer Payment Terms Code';
        }
        field(34; "Customer Language Code"; Code[20])
        {
            Caption = 'Customer Language Code';
        }
        field(35; "Customer Format Region"; Text[80])
        {
            Caption = 'Customer Format Region';
        }

    }
    keys
    {
        key(PK; ID)
        {
            Clustered = true;
        }
    }
}
