namespace ITV.Appointments.Handyman;
using Microsoft.Foundation.Address;
table 50004 Handyman
{
    Caption = 'Handyman';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "LoginEmail"; Code[100])
        {
            ExtendedDatatype = EMail;
            Caption = 'LoginEmail';
        }
        field(2; Name; Text[100])
        {
            Caption = 'Name';
        }
        field(3; "Company Name"; Text[30])
        {
            Caption = 'Company Name';
        }
        field(4; "Company ID"; Guid)
        {
            Caption = 'Company ID';
        }
        field(5; "Environment Name"; Text[100])
        {
            Caption = 'Environment Name';
        }
        field(6; "Outlook E-Mail Address"; Text[80])
        {
            ExtendedDatatype = EMail;
            Caption = 'Outlook E-Mail Address';
        }
        field(8; "Device Token"; Text[1000])
        {
            Caption = 'Device Token';
        }
    }
    keys
    {
        key(PK; LoginEmail)
        {
            Clustered = true;
        }
    }
}
