table 50003 "Appointment Requests Archive"
{
    Caption = 'Appointment Requests Archive';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
        }
        field(2; "Requested Delivery Datetime"; DateTime)
        {
            Caption = 'Requested Delivery Datetime';
        }
        field(3; Customer; Code[20])
        {
            Caption = 'Customer';
        }
        field(4; Status; Enum "Appointment Status")
        {
            Caption = 'Status';
        }
        field(5; "Sales Order No."; Code[20])
        {
            Caption = 'Sales Order No.';
        }
        field(7; "Confirmed Handyman"; Code[100])
        {
            Caption = 'Confirmed Handyman';
        }
        field(8; "Requested Handyman Skill"; code[100])
        {
            TableRelation = "Handyman Skills".LoginEmail;
            Caption = 'Requested Handyman Skill';
        }
        field(9; "Handyman Order No."; code[20])
        {
            Caption = 'Handyman Order No.';
        }
        field(10; "As Soon As Possible"; Boolean)
        {
            Caption = 'As Soon As Possible';
        }
        field(11; "Task Description"; Text[500])
        {
            Caption = 'Task Description';
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
}
