namespace ITV.Appointments.Handyman;
table 50005 "Handyman Skills"
{
    Caption = 'Handyman Skills';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "LoginEmail"; Code[100])
        {
            ExtendedDatatype = EMail;
            Caption = 'LoginEmail';
        }
        field(2; Description; Text[300])
        {
            Caption = 'Description';
        }
    }
    keys
    {
        key(PK; LoginEmail)
        {
            Clustered = true;
        }
    }
}
