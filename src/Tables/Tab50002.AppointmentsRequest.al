table 50002 "Appointment Requests"
{
    Caption = 'Appointment Requests';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
        }
        field(2; "Requested Delivery Datetime"; DateTime)
        {
            Caption = 'Requested Delivery Datetime';
        }
        field(3; "Customer No."; Code[20])
        {
            Caption = 'Customer';
            TableRelation = Customer where("No." = field("Customer No."));
        }
        field(4; Status; Enum "Appointment Status")
        {
            Caption = 'Status';
        }
        field(5; "Sales Order No."; Code[20])
        {
            Caption = 'Sales Order No.';
            TableRelation = "Sales Header"."No." where("Document Type" = const(Order), "No." = field("Sales Order No."));
        }
        field(7; "Confirmed Handyman"; Code[100])
        {
            Caption = 'Confirmed Handyman';
            TableRelation = Handyman.LoginEmail;
        }
        field(8; "Requested Handyman Skill"; code[100])
        {
            TableRelation = "Handyman Skills".LoginEmail;
            Caption = 'Requested Handyman Skill';
        }
        field(9; "Handyman Order No."; code[20])
        {
            Caption = 'Handyman Order No.';
        }
        field(10; "As Soon As Possible"; Boolean)
        {
            Caption = 'As Soon As Possible';
        }
        field(11; "Task Description"; Text[500])
        {
            Caption = 'Task Description';
        }
        field(12; "Task Category"; Enum "Task Category")
        {
            Caption = 'Task Category';
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        NoSeriesMgt: Codeunit "No. Series";
    begin
        if Rec."No." = '' then begin
            AppointmentsSetup.FindFirst();
            AppointmentsSetup.TestField("Appointment No. Series");
            Rec."No." := NoSeriesMgt.GetNextNo(AppointmentsSetup."Appointment No. Series");
        end;
    end;

    var

        AppointmentsSetup: Record "Appointments Setup";
}
