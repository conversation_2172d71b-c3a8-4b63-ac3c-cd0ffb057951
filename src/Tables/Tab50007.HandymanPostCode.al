namespace ITV.Appointments.Handyman;
table 50007 "Handyman Post Code"
{
    Caption = 'Handyman Post Code';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "LoginEmail"; Code[100])
        {
            ExtendedDatatype = EMail;
            Caption = 'LoginEmail';
            TableRelation = Handyman.LoginEmail;
        }
        field(2; "Post Code"; Code[20])
        {
            Caption = 'Postal Code';
        }
        field(3; Active; Boolean)
        {
            Caption = 'Active';
            InitValue = true;
        }
    }
    keys
    {
        key(PK; LoginEmail, "Post Code")
        {
            Clustered = true;
        }
    }
}
