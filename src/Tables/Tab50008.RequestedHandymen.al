namespace ITV.Appointments.Handyman;
table 50008 "Requested Handymen"
{
    Caption = 'Requested Handymen';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "Appointment No."; Code[20])
        {
            Caption = 'Appointment No.';
            TableRelation = "Appointment Requests"."No.";
        }
        field(2; "LoginEmail"; Code[100])
        {
            ExtendedDatatype = EMail;
            Caption = 'LoginEmail';
            TableRelation = Handyman.LoginEmail;
        }
        field(3; State; Enum "Handyman Appointment Status")
        {
            Caption = 'State';
        }
        field(4; Reason; Text[250])
        {
            Caption = 'Reason';
        }
    }
    keys
    {
        key(PK; "Appointment No.", LoginEmail)
        {
            Clustered = true;

        }
    }
}
