namespace ITV.Appointments.Setup;
using Microsoft.Sales.Customer;
using Microsoft.Foundation.NoSeries;
table 50000 "Appointments Setup"
{
    Caption = 'Appointments Setup';
    DataClassification = ToBeClassified;
    fields
    {
        field(1; "Code"; Code[10])
        {
            Caption = 'Code';
        }
        field(2; "Customer Company Template"; Code[20])
        {
            Caption = 'Customer Company Template';
            TableRelation = "Customer Templ.".Code;
        }
        field(3; "Customer Person Template"; Code[20])
        {
            Caption = 'Customer Person Template';
            TableRelation = "Customer Templ.".Code;
        }
        field(4; "Appointment No. Series"; Code[20])
        {
            TableRelation = "No. Series".Code;
            Caption = 'Appointment No. Series';
        }
        field(5; "Appointment Request Timeout"; Integer)
        {
            Caption = 'Appointment Request Timeout in Minutes';
            DataClassification = SystemMetadata;
        }
    }
    keys
    {
        key(PK; "Code")
        {
            Clustered = true;
        }
    }
}
