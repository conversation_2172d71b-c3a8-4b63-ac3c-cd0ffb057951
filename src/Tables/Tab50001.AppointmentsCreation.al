namespace ITV.Appointments;
using ITV.Appointments.Creation;
using ITV.Appointments.Handyman;
using Microsoft.Sales.Customer;
using Microsoft.Sales.Document;
table 50001 "Order"
{
    Caption = 'Order';
    DataClassification = CustomerContent;
    TableType = Temporary;
    fields
    {
        field(1; ID; Integer)
        {
            Caption = 'ID';
            AutoIncrement = true;
        }
        field(2; "Ship-to Name"; Text[100])
        {
            Caption = 'Ship-to Name';
        }
        field(3; "Ship-to Address Line 1"; Text[100])
        {
            Caption = 'Ship-to Address Line 1';
        }
        field(4; "Ship-to Address Line 2"; Text[50])
        {
            Caption = 'Ship-to Address Line 2';
        }
        field(5; "Ship-to City"; Text[30])
        {
            Caption = 'Ship-to City';
        }
        field(6; "Ship-to Postal Code"; Code[20])
        {
            Caption = 'Ship-to Postal Code';
        }
        field(7; Email; Text[80])
        {
            Caption = 'Email';
            ExtendedDatatype = EMail;
        }
        field(8; "Ship-to Phone Number"; Text[30])
        {
            Caption = 'Ship-to Phone Number';
            ExtendedDatatype = PhoneNo;
        }
        field(9; "Customer Type"; Enum "Customer Type")
        {
            Caption = 'Type';
        }
        field(10; "Task Description"; Text[500])
        {
            Caption = 'Task Description';
        }
        field(11; "Is Conditions Checked"; Boolean)
        {
            Caption = 'Is Conditions Checked';
        }
        field(12; "Appointment Request Date Time"; DateTime)
        {
            Caption = 'Appointment Request Date Time';
        }
        field(13; "Requested Handyman Skill"; Code[20])
        {
            Caption = 'Requested Handyman Skill';
            TableRelation = "Handyman Skills".LoginEmail;
        }
        field(14; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';

        }
        field(15; "Appointment System ID"; Guid)
        {
            Caption = 'Appointment ID';

        }
        field(16; "Sales Order No."; Code[20])
        {
            Caption = 'Sales Order No.';

        }
        field(17; "Appointment No."; Code[20])
        {
            Caption = 'Appointment No.';


        }
        field(18; "Customer ID"; Guid)
        {
            Caption = 'Customer ID';


        }
        field(19; "Sales Order ID"; Guid)
        {
            Caption = 'Sales Order ID';

        }
        field(20; "As Soon As Possible"; Boolean)
        {
            Caption = 'As Soon As Possible';

        }
        field(21; "Ship-to Email"; Text[80])
        {
            Caption = 'Ship-to Email';
            ExtendedDatatype = EMail;
        }
        field(22; "Bill-to Name"; Text[100])
        {
            Caption = 'Bill-to Name';
        }
        field(23; "Bill-to Address Line 1"; Text[100])
        {
            Caption = 'Bill-to Address Line 1';
        }
        field(24; "Bill-to Address Line 2"; Text[50])
        {
            Caption = 'Bill-to Address Line 2';
        }
        field(25; "Bill-to City"; Text[30])
        {
            Caption = 'Bill-to City';
        }
        field(26; "Bill-to Postal Code"; Code[20])
        {
            Caption = 'Bill-to Postal Code';
        }
        field(27; "Bill-to Email"; Text[80])
        {
            Caption = 'Bill-to Email';
            ExtendedDatatype = EMail;
        }
        field(28; "Bill-to Phone Number"; Text[30])
        {
            Caption = 'Bill-to Phone Number';
            ExtendedDatatype = PhoneNo;
        }
        field(29; "Task Category"; Enum "Task Category")
        {
            Caption = 'Task Category';
        }
    }
    keys
    {
        key(PK; ID)
        {
            Clustered = true;
        }
    }
}
