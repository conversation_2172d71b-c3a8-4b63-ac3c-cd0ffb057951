namespace ITV.Appointments.Synchronization;

using System.Environment.Configuration;
table 50006 "Synchronization Setup"
{
    Caption = 'Synchronization Setup';
    DataClassification = ToBeClassified;

    fields
    {
        field(1; "Code"; Code[20])
        {
            Caption = 'Code';
        }
        field(2; "Entra Application Client ID"; Guid)
        {
            Caption = 'Entra Application';
            TableRelation = "AAD Application"."Client Id";
        }
        field(3; "Client Secret Expiration Date"; Date)
        {
            Caption = 'Client Secret Expiration Date';
        }


    }
    keys
    {
        key(PK; "Code")
        {
            Clustered = true;
        }
    }
}
