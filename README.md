# alleskoenner

Repo für das alleskoenner24 Projekt

## IntelliJ Setup

For all developers to have the same code style you have to setup your intellij project to use the default project config.

> Go to Intellij Settings -> Editor -> Code Style: Select "Project" as Scheme.

This will tell IntelliJ to use the config which os persisted in the .idea/codeStyles folder and included in the git repository.

### Enable Actions on Save

To ensure that newly written code is formatted correctly (and will not be formatted by one of your colleagues in future commits that touch files that you have
been working on), it is required for every developer to enable the actions on save as follows:
![Enable Actions on Save in Settings: Reformat Code, Optimize imports, Rearrange code, Run code cleanup, Run eslint --fix, Run Prettier](readme_images/actions-on-save.png)

## Mkdocs

Our documentation can be found in the `technical_documantation` folder and is served with mkdocs.
