name: "Customer Website CI-CD"

on:
  push:
    paths:
      - customer_website/**
  workflow_dispatch:
    inputs:
      deploy:
        type: boolean
        description: "Deploy to deinhandwerker365.klosebrothers.de"
        required: false
        default: false
      branch:
        description: "Branch to use"
        required: false
        default: "master"

permissions:
  contents: write
  pull-requests: write
  issues: write
  repository-projects: write
  packages: write

jobs:
  run-vitest-unit-tests:
    runs-on: githubrunner-kb-bi
    defaults:
      run:
        working-directory: ./customer_website

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Dependencies
        run: npm install

      - name: Run unit tests
        run: npm run test:unit

  install-and-run-cypress-tests:
    runs-on: githubrunner-kb-bi
    defaults:
      run:
        working-directory: ./customer_website

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Here Maps Dependencies
        run: npm run add-npm-repository-for-here-maps

      - name: Install Dependencies
        run: npm install

      - name: Load environment variables
        run: |
          cat .env.ci | grep -v '^#' | xargs -L1 >> $GITHUB_ENV

      - name: Build the app with Vite
        run: npm run build

      - name: Run Cypress tests
        run: npm run test:e2e

      - name: Upload Cypress screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: cypress-screenshots
          path: |
            ./customer_website/cypress/screenshots

  build-push:
    runs-on: githubrunner-kb-bi
    if: github.ref == 'refs/heads/master' || github.event.inputs.deploy == 'true'
    needs: [ install-and-run-cypress-tests, run-vitest-unit-tests ]
    env:
      GITHUB_ACTIONS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_ACTOR: ${{ github.actor }}
      GITHUB_SHA: ${{ github.sha }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: login to github registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push vue app
        uses: docker/build-push-action@v6
        with:
          context: ./customer_website
          push: true
          file: ./customer_website/Dockerfile
          tags: |
            ghcr.io/klosebrothers/alleskoenner:${{ github.sha }}
            ghcr.io/klosebrothers/alleskoenner:latest

  deploy-to-k8s:
    runs-on: githubrunner-kb-bi
    needs: build-push

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: login to github registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: set context
        uses: azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.OVH_KB_K8S_KUBECONFIG }}

      - name: Apply the ConfigMap YAML to k8s
        run: |
          kubectl apply -f ./k8s/configmap/configmap.yaml --namespace=kunden

      - name: deploy to k8s
        uses: Azure/k8s-deploy@v5
        with:
          namespace: 'kunden'
          manifests: |
            ./k8s/deploy.yml
          images: |
            ghcr.io/klosebrothers/alleskoenner:${{ github.sha }}

  tag-deployed-commit:
    runs-on: githubrunner-kb-bi
    needs: deploy-to-k8s

    steps:
      - name: Get current date for tagging commit
        id: date
        run: echo "current_date=$(date +'%Y-%m-%dT%H-%M')" >> $GITHUB_ENV

      - name: tag commit with deployed
        uses: actions/github-script@v7
        with:
          github-token: ${{ github.token }}
          script: |
            github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: "refs/tags/alleskoenner-frontend${{ env.current_date }}",
              sha: context.sha
            })
