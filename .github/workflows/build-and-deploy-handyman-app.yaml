name: "Handyman App CI-CD"

on:
  push:
    paths:
      - handyman_app/**
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to use"
        required: false
        default: "master"

permissions:
  contents: write
  pull-requests: write
  issues: write
  repository-projects: write
  packages: write

jobs:
  run-vitest-unit-tests:
    runs-on: githubrunner-kb-bi
    defaults:
      run:
        working-directory: ./handyman_app

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Dependencies
        run: npm install

      - name: Run unit tests
        run: npm run test:unit

  install-and-run-cypress-tests:
    runs-on: githubrunner-kb-bi
    defaults:
      run:
        working-directory: ./handyman_app

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Dependencies
        run: npm install

      - name: Load environment variables
        run: |
          cat .env.ci | grep -v '^#' | xargs -L1 >> $GITHUB_ENV

      - name: Build the app with Vite
        run: npm run build

      - name: Run Cypress tests
        run: npm run test:e2e

      - name: Upload Cypress screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: cypress-screenshots
          path: |
            ./handyman_app/cypress/screenshots
