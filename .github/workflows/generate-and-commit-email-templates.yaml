name: "Generate Html Emails"

on:
  push:
    paths:
      - email/**
  workflow_dispatch:

jobs:
  generate-and-commit-html-email-template-json:
    runs-on: githubrunner-kb-bi
    defaults:
      run:
        working-directory: ./email
    env:
      GITHUB_ACTIONS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_ACTOR: ${{ github.actor }}
      GITHUB_SHA: ${{ github.sha }}
      generatedHtmlEmailTemplates: '../customer_website/server/email/templating/generatedHtmlEmailTemplates.json'

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Dependencies
        run: npm install

      - name: Start Nuxt App
        run: |
          npm run generateEmailHtmlTemplates

      - name: Commit email template json if it has changed
        run: |
          if [ -n "$(git status --porcelain $generatedHtmlEmailTemplates)" ]; then
            git config user.name "github-actions[bot]"
            git config user.email "github-actions[bot]@users.noreply.github.com"
            git add $generatedHtmlEmailTemplates
            git commit -m "dev: update email templates"
            git push
          else
            echo "Mjml templates have not changed, therefore there are no changes to commit."
          fi
