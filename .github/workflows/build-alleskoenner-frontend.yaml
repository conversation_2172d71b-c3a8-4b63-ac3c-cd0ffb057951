name: "Build Alleskoenner Frontend"

# This workflow uses the following GitHub secrets:
# - AZURE_CREDENTIALS_LIVE (See keepass. This is a JSON object containing the service principal credentials. See https://github.com/Azure/login?tab=readme-ov-file#creds)

on:
  #  push:
  #    paths:
  #      - customer_website/**
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to build"
        required: false
        default: "master"

env:
  AZURE_CONTAINER_REGISTRY_NAME: alleskoennerlive
  DOCKER_IMAGE_NAME: alleskoenner-frontend

permissions:
  contents: write
  pull-requests: write
  issues: write
  repository-projects: write
  packages: write
  checks: write
  id-token: write

jobs:
  run-vitest-unit-tests:
    runs-on: githubrunner-kb-bi
    defaults:
      run:
        working-directory: ./customer_website

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Dependencies
        run: npm install

      - name: Run unit tests
        run: npm run test:unit

  install-and-run-cypress-tests:
    runs-on: githubrunner-kb-bi
    defaults:
      run:
        working-directory: ./customer_website

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install Here Maps Dependencies
        run: npm run add-npm-repository-for-here-maps

      - name: Install Dependencies
        run: npm install

      - name: Load environment variables
        run: |
          cat .env.ci | grep -v '^#' | xargs -L1 >> $GITHUB_ENV

      - name: Build the app with Vite
        run: npm run build

      - name: Run Cypress tests
        run: npm run test:e2e

      - name: Upload Cypress screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: cypress-screenshots
          path: |
            ./customer_website/cypress/screenshots


  build-and-push-acr-image:
    name: Build and push image to ACR
    runs-on: githubrunner-kb-bi
    needs: [ install-and-run-cypress-tests, run-vitest-unit-tests ]
    defaults:
      run:
        working-directory: ./customer_website

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Get current commit hash
        id: get_commit_hash
        run: echo "commit_hash=$(git rev-parse HEAD)" >> $GITHUB_ENV

      - name: Login to Azure via Service Principal
        uses: azure/login@v2
        with:
          # Make sure the service principal has the necessary Azure Roles:
          # Subscription: Reader
          # Azure Container Registry: Container Registry Repository Contributor
          # Azure Container Registry: Container Registry Repository Catalog Lister
          # and also this:
          # az role assignment create --assignee <service-principal-id> \
          #    --role "AcrPush" \
          #    --scope /subscriptions/<subscription-id>/resourceGroups/alleskoenner-live/providers/Microsoft.ContainerRegistry/registries/<registry-name>
          creds: ${{ secrets.AZURE_CREDENTIALS_LIVE }}

      - name: Login to Azure Container Registry
        run: |
          az acr login --name ${{ env.AZURE_CONTAINER_REGISTRY_NAME }}

      - name: Build Docker image
        run: |
          docker build \
            -t ${{ env.AZURE_CONTAINER_REGISTRY_NAME }}.azurecr.io/${{ env.DOCKER_IMAGE_NAME }}:${{ env.commit_hash }} \
            -t ${{ env.AZURE_CONTAINER_REGISTRY_NAME }}.azurecr.io/${{ env.DOCKER_IMAGE_NAME }}:latest \
            .

      - name: Push Docker images
        run: |
          docker push ${{ env.AZURE_CONTAINER_REGISTRY_NAME }}.azurecr.io/${{ env.DOCKER_IMAGE_NAME }}:${{ env.commit_hash }}
          docker push ${{ env.AZURE_CONTAINER_REGISTRY_NAME }}.azurecr.io/${{ env.DOCKER_IMAGE_NAME }}:latest
