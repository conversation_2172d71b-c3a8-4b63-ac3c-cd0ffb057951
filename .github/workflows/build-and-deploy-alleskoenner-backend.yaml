name: "<PERSON><PERSON><PERSON><PERSON><PERSON> Backend CI-CD"

on:
  push:
    paths:
      - alleskoenner_backend/**
  workflow_dispatch:
    inputs:
      deploy:
        type: boolean
        description: "Deploy to k8s"
        required: false
        default: false
      branch:
        description: "Branch to deploy (only used when manually triggered)"
        required: false
        default: "master"

permissions:
  contents: write
  pull-requests: write
  issues: write
  repository-projects: write
  packages: write
  checks: write

jobs:

  build-project-and-run-unit-tests:
    name: Build project and run unit tests
    runs-on: githubrunner-kb-bi
    defaults:
      run:
        working-directory: ./alleskoenner_backend

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: '23'
          distribution: 'corretto'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Build project and run unit tests
        run: ./gradlew build

      - name: Report Test Results
        if: always()
        uses: dorny/test-reporter@v2
        with:
          name: JUnit Test Results
          path: '**/build/test-results/test/TEST-*.xml'
          reporter: java-junit

  build-and-push-docker-image:
    name: Build and push docker image
    runs-on: githubrunner-kb-bi
    if: |
      (github.ref == 'refs/heads/master') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy == 'true')
    needs: [ build-project-and-run-unit-tests ]
    env:
      GITHUB_ACTIONS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_ACTOR: ${{ github.actor }}
      GITHUB_SHA: ${{ github.sha }}
    defaults:
      run:
        working-directory: ./alleskoenner_backend

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Get current commit hash
        id: get_commit_hash
        run: echo "commit_hash=$(git rev-parse HEAD)" >> $GITHUB_ENV

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: '23'
          distribution: 'corretto'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Build project and run unit tests
        run: ./gradlew build

      - name: login to github registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push spring app
        uses: docker/build-push-action@v6
        with:
          context: ./alleskoenner_backend
          push: true
          file: ./alleskoenner_backend/Dockerfile
          tags: |
            ghcr.io/klosebrothers/alleskoenner-backend:${{ env.commit_hash }}
            ghcr.io/klosebrothers/alleskoenner-backend:latest

  deploy-to-k8s:
    name: Deploy to k8s
    runs-on: githubrunner-kb-bi
    needs: build-and-push-docker-image

    steps:
      - name: checkout repo
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Get current commit hash
        id: get_commit_hash
        run: echo "commit_hash=$(git rev-parse HEAD)" >> $GITHUB_ENV

      - name: login to github registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: set context
        uses: azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.OVH_KB_K8S_KUBECONFIG }}

      - name: Apply the ConfigMap YAML to k8s
        run: |
          kubectl apply -f ./k8s/configmap/configmap.yaml --namespace=kunden

      - name: Apply the service to k8s
        run: |
          kubectl apply -f ./k8s/alleskoenner_backend/service.yml --namespace=kunden

      - name: Apply the ingress to k8s
        run: |
          kubectl apply -f ./k8s/ingress.yml --namespace=kunden

      - name: deploy to k8s
        uses: Azure/k8s-deploy@v5
        with:
          namespace: 'kunden'
          manifests: |
            ./k8s/alleskoenner_backend/deploy.yml
          images: |
            ghcr.io/klosebrothers/alleskoenner-backend:${{ env.commit_hash }}

  tag-deployed-commit:
    runs-on: githubrunner-kb-bi
    needs: deploy-to-k8s

    steps:
      - name: Get current date for tagging commit
        id: date
        run: echo "current_date=$(date +'%Y-%m-%dT%H-%M')" >> $GITHUB_ENV

      - name: tag commit with deployed
        uses: actions/github-script@v7
        with:
          github-token: ${{ github.token }}
          script: |
            github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: "refs/tags/alleskoenner-backend-${{ env.current_date }}",
              sha: context.sha
            })
