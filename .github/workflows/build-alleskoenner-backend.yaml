name: "Build Alleskoenner Backend"

# This workflow uses the following GitHub secrets:
# - AZURE_CREDENTIALS_LIVE (See keepass. This is a JSON object containing the service principal credentials. See https://github.com/Azure/login?tab=readme-ov-file#creds)

on:
  #  push:
  #    paths:
  #      - alleskoenner_backend/**
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to build"
        required: false
        default: "master"

env:
  AZURE_CONTAINER_REGISTRY_NAME: alleskoennerlive
  DOCKER_IMAGE_NAME: alleskoenner-backend

permissions:
  contents: write
  pull-requests: write
  issues: write
  repository-projects: write
  packages: write
  checks: write
  id-token: write

jobs:

  build-project-and-run-unit-tests:
    name: Build project and run unit tests
    runs-on: githubrunner-kb-bi
    defaults:
      run:
        working-directory: ./alleskoenner_backend

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: '23'
          distribution: 'corretto'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Build and run tests
        run: ./gradlew build

      - name: Report Test Results
        if: always()
        uses: dorny/test-reporter@v2
        with:
          name: JUnit Test Results
          path: '**/build/test-results/test/TEST-*.xml'
          reporter: java-junit

  build-and-push-acr-image:
    name: Build and push image to ACR
    runs-on: githubrunner-kb-bi
    needs: build-project-and-run-unit-tests
    defaults:
      run:
        working-directory: ./alleskoenner_backend

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch || github.ref }}

      - name: Get current commit hash
        id: get_commit_hash
        run: echo "commit_hash=$(git rev-parse HEAD)" >> $GITHUB_ENV

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: '23'
          distribution: 'corretto'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Build
        run: ./gradlew build -x test

      - name: Login to Azure via Service Principal
        uses: azure/login@v2
        with:
          # Make sure the service principal has the necessary Azure Roles:
          # Subscription: Reader
          # Azure Container Registry: Container Registry Repository Contributor
          # Azure Container Registry: Container Registry Repository Catalog Lister
          # and also this:
          # az role assignment create --assignee <service-principal-id> \
          #    --role "AcrPush" \
          #    --scope /subscriptions/<subscription-id>/resourceGroups/alleskoenner-live/providers/Microsoft.ContainerRegistry/registries/<registry-name>
          creds: ${{ secrets.AZURE_CREDENTIALS_LIVE }}

      - name: Login to Azure Container Registry
        run: |
          az acr login --name ${{ env.AZURE_CONTAINER_REGISTRY_NAME }}

      - name: Build Docker image
        run: |
          docker build \
            -t ${{ env.AZURE_CONTAINER_REGISTRY_NAME }}.azurecr.io/${{ env.DOCKER_IMAGE_NAME }}:${{ env.commit_hash }} \
            -t ${{ env.AZURE_CONTAINER_REGISTRY_NAME }}.azurecr.io/${{ env.DOCKER_IMAGE_NAME }}:latest \
            .

      - name: Push Docker images
        run: |
          docker push ${{ env.AZURE_CONTAINER_REGISTRY_NAME }}.azurecr.io/${{ env.DOCKER_IMAGE_NAME }}:${{ env.commit_hash }}
          docker push ${{ env.AZURE_CONTAINER_REGISTRY_NAME }}.azurecr.io/${{ env.DOCKER_IMAGE_NAME }}:latest
