ARG NODE_VERSION=22.11.0

FROM node:${NODE_VERSION}-alpine as base

WORKDIR /app

# Build
FROM base as build

COPY --link package.json package-lock.json ./
RUN npm install

COPY --link . .

# The npm run build process will automatically use the .env.production file
COPY .env.test-production /app/.env.production

RUN npm run build

# Run
FROM nginx:alpine as production-stage
RUN mkdir /app
COPY --from=build /app/dist /app
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
