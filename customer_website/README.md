# customer_website

## Project Setup

```sh
npm run add-npm-repository-for-here-maps
npm install
```

## Here Maps

Please add the Here Maps API key to the .env file of the frontend. Create the .env file if it does not exist.

> VITE_HERE_API_KEY=Vc7OP-foobar-maps-api-key-S2xa

API key is stored in alleskoenner keepass.
Registered App in Here Maps is called "Alleskoenner Backend".
To access it, go to https://platform.here.com/access/apps. Credentials are in the keepass.

Here Maps is secured via "Trusted Domains". These can be configured in the app on the here maps plattform:
e.g. https://platform.here.com/access/apps/W1le2hscDrK7IZKjLYez