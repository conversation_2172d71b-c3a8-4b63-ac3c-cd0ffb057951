describe('navigate from index page through the request process', () => {
  beforeEach(() => {
    cy.visit('http://localhost:4173');
  });

  function loginWithUsernameAndPassword(username: string) {
    cy.intercept('GET', 'http://localhost:8080/customer/details', {
      fixture: 'customer-details.json',
    }).as('getUserDetails');
    cy.loginWithKeycloak({ username, password: '2Ap8dxbFCX4vHEj' });
    cy.wait('@getUserDetails');
  }

  it('should fill order request information and check page contents and navigate back', () => {
    cy.get('#task').click();

    function verifyRedirectToTaskPageAndCheckContent() {
      cy.url().should('include', '/task');
      cy.get('h2').contains('Was soll erledigt werden?');
      cy.get('[test-id="taskCategory"]');
      cy.get('[test-id="suggestions"]').children().contains('Allgemeiner Möbelaufbau').click();
      cy.get('#task').type('Küchenmontage');
      cy.get('button[test-id="submit-button"]').click();
    }

    function verifyRedirectToLocationPageAndCheckContent() {
      cy.url().should('include', '/location');
      cy.get('button[test-id="input-summary-button"]').contains('Dienstleistung');
      cy.get('h2').contains('Wo soll es erledigt werden?');
      cy.focused().should('have.id', 'location');
      cy.get('#location').should('have.attr', 'placeholder', 'Straße und Hausnummer eingeben');
      cy.intercept('GET', 'https://autocomplete.search.hereapi.com/**', {
        fixture: 'location-suggestions.json',
      });
      cy.get('#location').type('Herschel 1');
      cy.get('[test-id="suggestions"]').children().first().click();
    }

    function verifyRedirectToTimePageAndCheckContent() {
      cy.url().should('include', '/time');
      cy.get('button[test-id="input-summary-button"]').contains('Dienstleistung');
      cy.get('button[test-id="input-summary-button"]').contains('Standort');
      cy.get('h2').contains('Wann soll es erledigt werden?');
      cy.get('button').contains('So schnell wie möglich!');
      cy.contains('Innerhalb von 24 Stunden');
      cy.contains('oder Wunschtermin auswählen');
      cy.get('[test-id="asap-button"]').click();
    }

    function verifyRedirectToLoginPageAndCheckContent() {
      cy.url().should('include', '/login');
      cy.get('button[test-id="input-summary-button"]').contains('Dienstleistung');
      cy.get('button[test-id="input-summary-button"]').contains('Standort');
      cy.get('button[test-id="input-summary-button"]').contains('Wunschtermin');

      loginWithUsernameAndPassword('cypress-test-user1');
      cy.visit('/user-details');
    }

    function verifyRedirectToUserDetailsPageAndCheckContent() {
      cy.url().should('include', '/user-details');
      cy.get('h2').contains('Kontaktdaten');

      cy.get('[test-id="name"]').find('input').clear();
      cy.get('[test-id="name"]').find('input').type('Wilhelm Wudke');
      cy.get('[test-id="phone-number"]').type('0174 654987321');
      cy.get('[test-id="email-address"]').find('input').should('exist').and('be.disabled');
      cy.get('button[test-id="save-button"]').contains('Speichern').click();
    }

    function verifyRedirectToSummaryPageAndCheckContent() {
      cy.url().should('include', '/summary');
      cy.get('h2').contains('Gleich geschafft!');
      cy.get('h3').contains('Auftragsübersicht');
      cy.get('button[test-id="input-summary-button"]').contains('Dienstleistung');
      cy.get('button[test-id="input-summary-button"]').contains('Wunschtermin');
      cy.get('h3').contains('Ihre Daten');
      cy.get('button[test-id="input-summary-button"]').contains('Name');
      cy.get('button[test-id="input-summary-button"]').contains('Wilhelm Wudke');
      cy.get('button[test-id="input-summary-button"]').contains('0174 654987321');
      cy.get('button[test-id="input-summary-button"]').contains('Arbeitsadresse');
      cy.get('button[test-id="input-summary-button"]').contains('Rechnungsanschrift');
      cy.get('h3').contains('Kosten');

      cy.get('[test-id="sales-order-line"]')
        .eq(0)
        .should('contain.text', 'Anfahrtskosten')
        .and('contain.text', '29,30 €');
      cy.get('[test-id="sales-order-line"]')
        .eq(1)
        .should('contain.text', 'Erste Arbeitsstunde')
        .and('contain.text', '+ 81,90 €');
      cy.get('[test-id="sales-order-line"]')
        .eq(2)
        .should('contain.text', 'Kosten für die erste Anfahrt netto')
        .and('contain.text', '111,20 €');
      cy.get('[test-id="sales-order-line"]')
        .eq(3)
        .should('contain.text', '+ 19% MwSt.')
        .and('contain.text', '+ 21,10 €');
      cy.get('[test-id="sales-order-line"]')
        .eq(4)
        .should('contain.text', 'Gesamtkosten für die erste Anfahrt')
        .and('contain.text', '= 132,30 €');

      cy.get('button').contains('Zahlungspflichtigen Auftrag erteilen').as('button');
      cy.get('@button').should('be.disabled');
      cy.get('[test-id="conditions-checkbox"]').find('a').should('have.attr', 'href');
      cy.get('[test-id="conditions-checkbox"]').click();
      cy.get('@button').should('not.be.disabled');
    }

    function verifyBackwardsNavigationLeadsToPreviousSteps() {
      cy.get('button[test-id="input-summary-button"]').contains('Name');
      cy.get('button[test-id="input-summary-button"]').contains('Wunschtermin').click();
      cy.url().should('include', '/time');
      cy.get('button').contains('Standort').click();
      cy.url().should('include', '/location');
      cy.get('button[test-id="input-summary-button"]').contains('Dienstleistung').click();
      cy.url().should('include', '/task');
    }

    verifyRedirectToTaskPageAndCheckContent();
    verifyRedirectToLocationPageAndCheckContent();
    verifyRedirectToTimePageAndCheckContent();
    verifyRedirectToLoginPageAndCheckContent();
    verifyRedirectToUserDetailsPageAndCheckContent();
    verifyRedirectToSummaryPageAndCheckContent();
    verifyBackwardsNavigationLeadsToPreviousSteps();
  });

  function navigateToLoginPage(username: string) {
    cy.get('[test-id="task"]').click();
    cy.get('[test-id="taskCategory"]');
    cy.get('[test-id="suggestions"]').children().contains('Allgemeiner Möbelaufbau').click();
    cy.get('[test-id="taskTextArea"]').type(
      'Küchenmontage mit einer ganz langen Beschreibung, damit sie auf jeden Fall lang genug ist, falls die Komponente zu langsam lädt.',
    );
    cy.get('button[test-id="submit-button"]').click();
    cy.intercept('GET', 'https://autocomplete.search.hereapi.com/**', {
      fixture: 'location-suggestions.json',
    });
    cy.get('#location').type('Herschel 1');
    cy.get('[test-id="suggestions"]').children().first().click();
    cy.get('[test-id="asap-button"]').click();
    loginWithUsernameAndPassword(username);
    cy.visit('/summary');
  }

  it('should fill order request information and send request', () => {
    function validateConfirmationPage() {
      cy.get('[test-id="conditions-checkbox"]').click();

      cy.intercept('POST', 'http://localhost:8080/order', {
        fixture: 'order.json',
      });
      cy.get('button').contains('Zahlungspflichtigen Auftrag erteilen').click();
    }

    function checkPageContent() {
      cy.url().should('include', '/confirmation');
      const expectedTexts = [
        'Ihre Auftragsbestätigung <NAME_EMAIL> geschickt.',
        'Unser System oder Team weist Ihren Auftrag einem qualifizierten Handwerker in Ihrer Nähe zu.',
        'Der Handwerker wird den Auftrag vor Ort überprüfen und einen Kostenvoranschlag erstellen.',
        'Sie vereinbaren mit dem Handwerker eine neuen Termin für die Durchführung Ihres Auftrags.',
        'Der Handwerker führt die Arbeit pünktlich und fachgerecht aus und informiert Sie über den Fortschritt.',
      ];
      expectedTexts.forEach((text, index) => {
        cy.get('[test-id="next-step"]').eq(index).contains(text);
      });
      cy.get('[test-id="card"]').contains('Vielen Dank für Ihren Auftrag, Max Mustermann!');
      cy.get('[test-id="confirmation-text"]').contains(
        'Wir haben Ihren Auftrag mit der Vorgangsnummer 101047 erhalten!',
      );
      cy.get('[test-id="card"]').should('have.class', 'bg-akSuccessFlavor');
    }

    navigateToLoginPage('cypress-test-user1');
    cy.get('button[test-id="input-summary-button"]').contains('Allgemeiner Möbelaufbau');
    validateConfirmationPage();
    checkPageContent();
  });

  it('should check page contents and navigation for user with impersonation role', () => {
    function checkCustomerImpersonation() {
      cy.get('button[test-id="input-summary-button"]').contains('Name').click();

      cy.url().should('include', '/user-details');
      cy.get('[test-id="customer-impersonation-banner"]').contains(
        'Sie sind als Kunden-Stellvertretung angemeldet und können Aufträge für Kunden anlegen.',
      );
      cy.get('[test-id="user-details-info"]').contains(
        'Sie haben die Berechtigung für einen Kunden einen Auftrag zu erteilen. Bitte achten Sie auf sorgfältige Eingabe der Kundendaten.',
      );
      cy.get('[test-id="email-address"]').find('input').should('exist').and('not.be.disabled');

      cy.get('button[test-id="save-button"]').contains('Speichern').click();
      cy.url().should('include', '/summary');
    }

    navigateToLoginPage('cypress-test-user-impersonation-role');
    cy.get('button[test-id="input-summary-button"]').contains('Allgemeiner Möbelaufbau');
    cy.get('button[test-id="input-summary-button"]').contains(
      'Küchenmontage mit einer ganz langen Beschreibung, damit sie auf jeden Fall lang genug ist, falls die Komponente zu langsam lädt.',
    );
    checkCustomerImpersonation();
  });
});
