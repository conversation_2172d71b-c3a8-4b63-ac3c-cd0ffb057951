/// <reference types="cypress" />
// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
//
// declare global {
//   namespace Cypress {
//     interface Chainable {
//       login(email: string, password: string): Chainable<void>
//       drag(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       dismiss(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       visit(originalFn: CommandOriginalFn, url: string, options: Partial<VisitOptions>): Chainable<Element>
//     }
//   }
// }

import { type IdTokenClaims, User, UserManager } from 'oidc-client-ts';

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Cypress {
    interface Chainable {
      // Performs the login via Keycloak (Direct Access Grant) and saves tokens in LocalStorage + fires userLoaded
      loginWithKeycloak(options: { username: string; password: string }): Chainable<void>;
    }

    interface Window {
      userManager?: UserManager;
    }
  }
}

Cypress.Commands.add('loginWithKeycloak', function (options: { username: string; password: string }) {
  const { username, password } = options;
  const clientId = 'alleskoenner24-customer-client-cypress';
  const realm = 'alleskoenner24';
  const keycloakBaseUrl = 'https://oidc.klosebrothers.de';
  const tokenUrl = `${keycloakBaseUrl}/realms/${realm}/protocol/openid-connect/token`;
  const issuer = `${keycloakBaseUrl}/realms/${realm}`;

  const tokenRequest = {
    method: 'POST',
    url: tokenUrl,
    form: true,
    body: {
      grant_type: 'password',
      client_id: clientId,
      username,
      password,
      scope: 'openid email profile',
    },
  };

  cy.request(tokenRequest).then((response) => {
    const body = response.body;
    const { access_token, id_token, expires_in, refresh_token } = body;
    const nowInSeconds = Math.floor(Date.now() / 1000);
    const profileFromToken = decodeJwtPayload(access_token) as IdTokenClaims;

    const user = new User({
      id_token,
      access_token,
      refresh_token,
      token_type: 'Bearer',
      scope: 'openid profile email',
      session_state: null,
      profile: profileFromToken,
      expires_at: nowInSeconds + expires_in,
    });

    // OIDC client expects the user to be stored in localStorage under a specific key
    const storageKey = `oidc.user:${issuer}:alleskoenner24-customer-client`;

    cy.window().then((win) => {
      const oidcUser = new User(user);

      // Manually sets the user in localStorage (as the OIDC client would automatically do after normal login)
      win.localStorage.setItem(storageKey, JSON.stringify(user));

      // userManager is the OIDC client instance that manages the user session and was exposed explicitly for the cypress tests
      const userManager = win.userManager;
      if (userManager) {
        // Triggers the userLoaded event in the OIDC client so that the application can react to the new user
        return userManager.storeUser(oidcUser).then(async () => {
          await userManager.events.load(oidcUser);
        });
      }
    });
  });

  function decodeJwtPayload(token: string): Record<string, any> {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join(''),
    );
    return JSON.parse(jsonPayload);
  }
});

export {};
