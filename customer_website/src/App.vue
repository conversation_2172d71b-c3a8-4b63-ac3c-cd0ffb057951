<script lang="ts" setup>
import { RouterView } from 'vue-router';
import Navbar from '@/components/Navbar.vue';
import { hasCustomerImpersonationRole } from '@/auth/auth.ts';
</script>

<template>
  <div class="flex flex-col h-screen w-full">
    <div
      v-if="hasCustomerImpersonationRole"
      class="bg-cerulean-500 text-white text-center py-2"
      test-id="customer-impersonation-banner"
    >
      <p class="text-sm">
        Sie sind als <span class="font-bold">Kunden-Stellvertretung</span> angemeldet und können Aufträge für Kunden
        anlegen.
      </p>
    </div>
    <Navbar />
    <div class="overflow-y-auto flex-1">
      <main class="max-w-screen-xl mx-auto pb-16">
        <RouterView />
      </main>
    </div>
  </div>
</template>
