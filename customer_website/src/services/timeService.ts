import dayjs, { Dayjs } from 'dayjs';
import Holidays from 'date-holidays';

export type DateOption = {
  date: Dayjs;
  availableHours: number[];
};

const defaultFirstAvailableHour = 6;
const defaultLastAvailableHour = 21; // exclusive -> 21 = 20:00 (8pm)

function getFirstAvailableHourToday() {
  // first available hour should be at least 3h 30min from "now", so we add 4h 29min and round the time off
  const now = dayjs();
  const firstAvailableTime = now.add(4, 'hour').add(29, 'minute');
  const midnightCrossing = firstAvailableTime.startOf('day').diff(now.startOf('day'), 'day');
  return Math.max(defaultFirstAvailableHour, firstAvailableTime.hour() + midnightCrossing * 24);
}

function isSunday(date: dayjs.Dayjs) {
  return date.day() === 0;
}

/**
 * Generates an array of available hours within a specified range.
 *
 * @param {number} firstAvailableHour - The first hour in the range (inclusive).
 * @param {number} lastAvailableHour - The last hour in the range (exclusive).
 * @returns {number[]} An array of numbers representing the available hours.
 *
 * @example
 * generateAvailableHours(9, 12); // Returns [9, 10, 11]
 * generateAvailableHours(6, 0); // Returns []
 */
function generateAvailableHours(firstAvailableHour: number, lastAvailableHour: number) {
  return Array.from({ length: lastAvailableHour - firstAvailableHour }, (_, i): number => i + firstAvailableHour);
}

function isHalfHoliday(date: Dayjs) {
  const isDecember = date.month() === 11;
  const isDayInMonth24Or31 = date.date() === 24 || date.date() === 31;
  return isDecember && isDayInMonth24Or31;
}

function isPublicOrBankHoliday(date: Dayjs) {
  const country = 'DE';
  const county = 'BE';
  const holidays = new Holidays(country, county);
  const holiday = holidays.isHoliday(date.toDate());
  return holiday && (holiday[0].type === 'public' || holiday[0].type === 'bank');
}

function getAvailableHoursFor(date: Dayjs): number[] {
  const isFeatureDisabled = true;
  if (isSunday(date) || isPublicOrBankHoliday(date) || isFeatureDisabled) {
    return [];
  }
  const today = dayjs().startOf('day');
  const firstAvailableHour = date.isSame(today, 'day') ? getFirstAvailableHourToday() : defaultFirstAvailableHour;

  // TODO: check the holidays depending on the location, once other regions than Berlin are available
  const lastAvailableHour = isHalfHoliday(date) ? 14 : defaultLastAvailableHour;

  return generateAvailableHours(firstAvailableHour, lastAvailableHour);
}

export function getDateOptions(): DateOption[] {
  const today = dayjs().startOf('day');
  return Array.from({ length: 15 }, (_, i) => ({
    date: today.add(i, 'day'),
    availableHours: getAvailableHoursFor(today.add(i, 'day')),
  }));
}

export const _forTesting = { getFirstAvailableHourToday };
