import { authTokenHeader } from '@/auth/auth.ts';

export function useUserDetailService() {
  async function getUserDetails(): Promise<Response> {
    const baseUrl = import.meta.env.VITE_ALLESKOENNER_BACKEND_URL;

    return await fetch(baseUrl + '/customer/details', {
      method: 'GET',
      headers: {
        ...authTokenHeader(),
        Accept: 'application/json',
      },
    });
  }

  return {
    getUserDetails,
  };
}
