import { useOrderRequestStore } from '@/stores/orderRequest.ts';
import type { OrderDetailsRequest } from '@/types/order/OrderRequest.type.ts';
import { authTokenHeader } from '@/auth/auth.ts';
import dayjs from 'dayjs';
import { useUserDetailsStore } from '@/stores/userDetails.ts';

function buildOrderRequestPayloadFromStore(): OrderDetailsRequest {
  const orderRequest = useOrderRequestStore();
  const userDetails = useUserDetailsStore();

  if (
    !orderRequest.taskDescription ||
    !orderRequest.taskCategory ||
    !orderRequest.shipToAddress ||
    !orderRequest.time ||
    !userDetails
  ) {
    throw new Error('Missing required fields in order request.');
  }

  const isAsap = orderRequest.time === 'asap';
  if (!orderRequest.isBillToDifferent) {
    orderRequest.billToAddress = orderRequest.shipToAddress;
    orderRequest.billToName = userDetails.name;
  }
  return {
    shipToName: userDetails.name,
    shipToAddressLine1: `${orderRequest.shipToAddress.address.street!} ${orderRequest.shipToAddress.address.houseNumber!}`,
    shipToAddressLine2: '',
    shipToCity: orderRequest.shipToAddress.address.city!,
    shipToPostalCode: orderRequest.shipToAddress.address.postalCode!,
    shipToEmail: userDetails.email,
    shipToPhoneNumber: userDetails.phone,
    billToName: userDetails.name,
    billToAddressLine1: `${orderRequest.billToAddress!.address.street!} ${orderRequest.billToAddress!.address.houseNumber!}`,
    billToAddressLine2: '',
    billToCity: orderRequest.billToAddress!.address.city!,
    billToPostalCode: orderRequest.billToAddress!.address.postalCode!,
    billToEmail: userDetails.email,
    billToPhoneNumber: userDetails.phone,
    email: userDetails.email,
    customerType: userDetails.type,
    taskCategory: orderRequest.taskCategory.id,
    taskDescription: orderRequest.taskDescription,
    isConditionsChecked: orderRequest.isConditionsChecked,
    isAsSoonAsPossible: isAsap,
    appointmentRequestDate: isAsap ? undefined : dayjs(orderRequest.time).toDate(),
    requestedHandymanSkill: '',
  };
}

export async function postOrderRequest() {
  const payload = buildOrderRequestPayloadFromStore();

  try {
    const baseUrl = import.meta.env.VITE_ALLESKOENNER_BACKEND_URL;
    const orderEndpoint = '/order';

    return fetch(baseUrl + orderEndpoint, {
      method: 'POST',
      headers: {
        ...authTokenHeader(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
  } catch (error) {
    console.error('Error posting order request:', error);
    throw error;
  }
}

export const _forTesting = { buildOrderRequestPayloadFromStore };
