import H from '@here/maps-api-for-javascript';
import type {
  Address,
  AutocompleteResponse,
  AutocompleteResponseItem,
} from '@/types/HereMapsAutocompleteResponse.type.ts';

export async function getLocationSuggestionsFor(locationInput: string): Promise<Address[] | undefined> {
  if (!locationInput) {
    return [];
  }

  const platform = new H.service.Platform({
    apikey: import.meta.env.VITE_HERE_API_KEY,
  });

  const service = platform.getSearchService();

  return new Promise((resolve, reject) => {
    service.autocomplete(
      {
        q: locationInput,
        at: '52.51279225883005,13.32174551368921', // Example: Berlin
        in: 'countryCode:DEU',
        limit: 10,
      },
      (result) => {
        resolve(mapAutoCompleteResultToLocationItems(result as AutocompleteResponse));
      },
      (error) => {
        reject(error);
      },
    );
  });
}

function mapAutoCompleteResultToLocationItems(result: AutocompleteResponse): Address[] {
  const isAddressValid = (item: AutocompleteResponseItem) =>
    !!item.address && !!item.address.street && !!item.address.postalCode && !!item.address.city;

  return result.items
    .filter(isAddressValid)
    .sort((a, b) => a.distance - b.distance)
    .slice(0, 5)
    .map((item) => item.address);
}
