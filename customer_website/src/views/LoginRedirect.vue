<script lang="ts" setup>
import { onMounted } from 'vue';
import { handleLoginRedirectCallback } from '@/auth/auth.ts';

onMounted(async () => {
  try {
    await handleLoginRedirectCallback();
  } catch (error) {
    console.error('Error processing OIDC response', error);
  }
});
</script>

<template>
  <div class="fixed inset-0 flex flex-col items-center justify-center">
    <p class="text-gray-700 text-lg font-medium"><PERSON>e werden weitergeleitet…</p>
  </div>
</template>
