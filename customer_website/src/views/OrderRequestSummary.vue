<script lang="ts" setup>
import InputSummaryButton from '../components/InputSummaryButton.vue';
import { faCheck, faClockFour } from '@fortawesome/free-solid-svg-icons';
import { formattedTime } from '../services/orderRequestUtils.ts';
import router from '@/router/routes.ts';
import { useOrderRequestResponseStore, useOrderRequestStore } from '@/stores/orderRequest.ts';
import DefaultButton from '@/components/DefaultButton.vue';
import { postOrderRequest } from '@/services/orderRequestService.ts';
import HorizontalLine from '@/components/order-request-summary/HorizontalLine.vue';
import { computed, ref } from 'vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import SalesOrderLine from '@/components/order-request-summary/SalesOrderLine.vue';
import { useUserDetailsStore } from '@/stores/userDetails.ts';
import { hasCustomerImpersonationRole } from '@/auth/auth.ts';

const userDetails = useUserDetailsStore();
const orderRequest = useOrderRequestStore();
const orderRequestResponseStore = useOrderRequestResponseStore();

const isConditionsChecked = ref();

const billToAddressLabel = computed(() =>
  orderRequest.isBillToDifferent ? orderRequest.billToAddress!.label : 'Identisch mit Arbeitsadresse',
);

const billToAddressButtonLabel = computed(() =>
  orderRequest.isBillToDifferent ? 'Rechnungsadresse anpassen' : 'Abweichende Rechnungsadresse angeben',
);

async function handleSubmitOrderRequest() {
  orderRequest.isConditionsChecked = isConditionsChecked.value;
  try {
    await router.push('confirmation');
    const response = await postOrderRequest();
    if (response.status === 200) {
      const responsePayload = await response.json();
      orderRequestResponseStore.$reset();
      orderRequestResponseStore.$patch(responsePayload);
      orderRequest.$reset();
    } else {
      await router.push('summary');
      alert('Ein Fehler ist aufgetreten. Überprüfen Sie Ihre Eingabe und versuchen Sie es später bitte erneut.');
      console.log(response);
    }
  } catch (error) {
    // typically this would be a network error / backend not reached
    console.error(error);
    await router.push('summary');
    alert(
      'Ein Fehler ist aufgetreten. Bitte überprüfen Sie ihre Internetverbindung und versuchen Sie es später erneut.',
    );
  }
}

async function navigateToBillToAddress() {
  await router.push('bill-to-address');
}
</script>

<template>
  <div class="items-start flex flex-col py-8 max-w-screen-sm px-6 self-center m-auto gap-1">
    <h2 class="text-2xl font-medium">Gleich geschafft!</h2>

    <h3 class="text-base font-normal text-cerulean-500 mt-4">Auftragsübersicht</h3>
    <InputSummaryButton
      :prefix-icon="orderRequest.taskCategory?.icon"
      :value="`${orderRequest.taskCategory?.label || ''}: ${orderRequest.taskDescription || ''}`"
      class="mt-2"
      placeholder="Dienstleistung"
      @click.prevent="router.push('task')"
    />
    <InputSummaryButton
      :prefix-icon="faClockFour"
      :value="formattedTime(orderRequest.time)"
      placeholder="Wunschtermin"
      @click.prevent="router.push('time')"
    />
    <HorizontalLine class="mt-4" />

    <h3 class="text-base font-normal text-cerulean-500 mt-4">Ihre Daten</h3>
    <InputSummaryButton
      :value="userDetails?.name"
      class="mt-2"
      placeholder="Name"
      @click.prevent="router.push('user-details')"
    />
    <InputSummaryButton
      :value="userDetails?.phone"
      placeholder="Telefonnummer"
      @click.prevent="router.push('user-details')"
    />
    <InputSummaryButton
      v-if="hasCustomerImpersonationRole"
      :value="userDetails?.email"
      placeholder="E-Mail Adresse"
      @click.prevent="router.push('user-details')"
    />
    <InputSummaryButton
      :value="orderRequest.shipToAddress?.label"
      class="mt-2"
      placeholder="Arbeitsadresse"
      @click.prevent="router.push('location')"
    />
    <InputSummaryButton
      :value="billToAddressLabel"
      placeholder="Rechnungsanschrift"
      @click.prevent="navigateToBillToAddress"
    />
    <DefaultButton style-name="link" @click.prevent="navigateToBillToAddress">
      {{ billToAddressButtonLabel }}
    </DefaultButton>
    <HorizontalLine class="mt-4" />

    <h3 class="text-base font-normal text-cerulean-500 mt-4">Kosten</h3>
    <p class="text-sm mt-2">
      Der Handwerker wird Ihren gewünschten Auftrag vor Ort überprüfen und einen Kostenvoranschlag erstellen.
    </p>
    <p class="text-sm">Pauschal berechnen wir Ihnen dafür folgende Kosten:</p>
    <SalesOrderLine description="Anfahrtskosten" price="29,30 €" />
    <SalesOrderLine description="Erste Arbeitsstunde" price="+ 81,90 €" />
    <HorizontalLine line-style="strong" />
    <SalesOrderLine description="Kosten für die erste Anfahrt netto" price="111,20 €" />
    <SalesOrderLine description="+ 19% MwSt." price="+ 21,10 €" />
    <SalesOrderLine description="Gesamtkosten für die erste Anfahrt" is-price-blue price="= 132,30 €" />

    <div
      class="my-6 flex items-baseline gap-2"
      test-id="conditions-checkbox"
      @click.prevent="isConditionsChecked = !isConditionsChecked"
    >
      <div class="relative flex flex-col items-center">
        <input
          id="conditions-checkbox"
          v-model="isConditionsChecked"
          class="size-5 rounded-md appearance-none checked:bg-cerulean-500 checked:border-cerulean-500 border-2 outline-none self-center"
          name="isConditionsChecked"
          type="checkbox"
          @click.stop
        />
        <FontAwesomeIcon v-if="isConditionsChecked" :icon="faCheck" class="text-white absolute z-10 h-4 m-0.5" />
      </div>
      <label class="sr-only" for="conditions-checkbox "> Ich akzeptiere die AGB. </label>
      <span class="text-sm self-center">
        Ich akzeptiere die
        <a
          class="text-cerulean-500"
          href="https://deinhandwerker365.de/agb/"
          rel="noopener noreferrer"
          target="_blank"
          @click.stop
        >
          AGB
        </a>
        .*
      </span>
    </div>

    <DefaultButton
      :disabled="!isConditionsChecked"
      class="w-full"
      style-name="ak-red"
      @click.prevent="handleSubmitOrderRequest"
    >
      Zahlungspflichtigen Auftrag erteilen
    </DefaultButton>
  </div>
</template>
