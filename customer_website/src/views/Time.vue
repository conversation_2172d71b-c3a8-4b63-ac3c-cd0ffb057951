<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import InputSummaryButton from '../components/InputSummaryButton.vue';
import type { Ref } from 'vue';
import { onMounted, ref } from 'vue';
import type { DateOption } from '../services/timeService';
import { getDateOptions } from '../services/timeService';
import HourSelector from '../components/time/HourSelector.vue';
import DateSelector from '../components/time/DateSelector.vue';
import router from '@/router/routes.ts';
import { useOrderRequestStore } from '@/stores/orderRequest.ts';
import DefaultButton from '@/components/DefaultButton.vue';
import dayjs from 'dayjs';
import { useNightTime } from '@/composables/useNightTime.ts';

const requestStore = useOrderRequestStore();
const { isNightTime } = useNightTime();

function handleAsapClick() {
  requestStore.time = 'asap';
  router.push('login');
}

const dateOptions: Ref<DateOption[]> = ref(getDateOptions());

const selectedDateOption: Ref<DateOption | undefined> = ref();
const selectedHourOption: Ref<number | undefined> = ref();

function selectDate(newSelectedDateOption: DateOption) {
  selectedHourOption.value = undefined;
  selectedDateOption.value = newSelectedDateOption;
}

function selectHour(newSelectedHourOption: number) {
  selectedHourOption.value = newSelectedHourOption;

  requestStore.time = selectedDateOption.value?.date.hour(newSelectedHourOption).startOf('hour').toISOString();
  router.push('login');
}

onMounted(() => {
  if (requestStore.time && requestStore.time != 'asap') {
    selectedDateOption.value = dateOptions.value.find((dateOption) => dateOption.date.isSame(requestStore.time, 'day'));
    selectedHourOption.value = dayjs(requestStore.time).hour();
  }
});
</script>

<template>
  <div class="items-start flex flex-col py-8 max-w-screen-sm px-6 self-center m-auto gap-1">
    <InputSummaryButton
      :prefix-icon="requestStore.taskCategory?.icon"
      :value="`${requestStore.taskCategory?.label || ''}: ${requestStore.taskDescription || ''}`"
      placeholder="Dienstleistung"
      @click.prevent="router.push('task')"
    />
    <InputSummaryButton
      :prefix-icon="requestStore.shipToAddress?.icon"
      :value="requestStore.shipToAddress?.label"
      placeholder="Standort"
      @click.prevent="router.push('location')"
    />
    <h2 class="text-xl font-medium mt-2">Wann soll es erledigt werden?</h2>
    <DefaultButton class="w-full mt-3" style-name="ak-blue" test-id="asap-button" @click.prevent="handleAsapClick">
      So schnell wie möglich!
    </DefaultButton>
    <span :class="isNightTime ? 'text-red-500' : 'text-gray-400'" class="text-sm mt-2">
      <template v-if="isNightTime">
        <p>Sie befinden sich außerhalb unserer Geschäftszeiten.</p>
        <p>Die Anfrage wird erst am nächsten Tag ab 6 Uhr beantwortet. Bei Notfällen rufen Sie bitte an!</p>
      </template>
      <template v-else> Innerhalb von 24 Stunden wird ein Handwerker bei Ihnen vor Ort sein </template>
    </span>
    <div class="flex items-center w-full mt-3">
      <div class="flex-grow h-px bg-gray-200" />
      <span class="px-3 text-sm">oder Wunschtermin auswählen</span>
      <div class="flex-grow h-px bg-gray-200" />
    </div>
    <span class="text-gray-500 mt-3">
      In Zukunft wird es möglich sein, einen bestimmten Wunschtermin anzugeben. Diese Funktion ist noch in Arbeit.
    </span>
    <DateSelector
      :date-options="dateOptions"
      :selected-date-option="selectedDateOption"
      test-id="date-selector"
      @select-date="selectDate"
    />
    <HourSelector
      v-if="selectedDateOption"
      :hour-options="selectedDateOption.availableHours"
      :selected-hour-option="selectedHourOption"
      test-id="hour-selector"
      @select-hour="selectHour"
    />
  </div>
</template>
