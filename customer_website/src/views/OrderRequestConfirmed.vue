<script lang="ts" setup>
import { useOrderRequestResponseStore } from '@/stores/orderRequest.ts';
import NextStep from '@/components/order-request-confirmed/NextStep.vue';
import { faCalendar, faEnvelope } from '@fortawesome/free-regular-svg-icons';
import { faClipboardQuestion, faMagnifyingGlass, faTools } from '@fortawesome/free-solid-svg-icons';
import { nextTick, ref, watch } from 'vue';
import LoadingSpinner from '@/components/LoadingSpinner.vue';
import Card from '@/components/Card.vue';

const orderRequestResponse = useOrderRequestResponseStore();

const topMostElement = ref<HTMLElement | null>(null);

watch(
  () => orderRequestResponse.$state,
  async (newValue) => {
    if (newValue) {
      await nextTick();
      topMostElement.value?.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  },
  { once: true },
);
</script>

<template>
  <div class="items-start flex flex-col pb-8 max-w-screen-sm px-6 self-center m-auto gap-1">
    <LoadingSpinner v-if="!orderRequestResponse.salesOrderNumber" class="mx-auto pt-15 pb-12" />
    <section v-else ref="topMostElement" class="w-full pb-3 pt-15 gap-3 flex flex-col">
      <Card :message="`Vielen Dank für Ihren Auftrag, ${orderRequestResponse.shipToName}!`" card-type="success" />
      <span class="mt-5 text-pretty" test-id="confirmation-text">
        Wir haben Ihren Auftrag mit der Vorgangsnummer
        <span class="font-bold">{{ orderRequestResponse.salesOrderNumber }}</span>
        erhalten!
      </span>
    </section>

    <h2 class="text-xl font-medium mt-2">Was passiert als Nächstes?</h2>
    <section class="relative flex flex-col mt-5 gap-8">
      <div class="absolute top-5 bottom-5 left-[19px] border-cerulean-100 border-l-2 -z-10"></div>

      <NextStep :icon="faEnvelope">
        Ihre Auftragsbestätigung wird an
        <span :class="{ 'font-semibold': !!orderRequestResponse.shipToEmail }">{{
          orderRequestResponse.shipToEmail ?? 'die angegebene E-Mail-Adresse'
        }}</span>
        geschickt.
      </NextStep>
      <NextStep :icon="faClipboardQuestion">
        Unser System oder Team weist Ihren Auftrag einem qualifizierten Handwerker in Ihrer Nähe zu.
      </NextStep>
      <NextStep :icon="faMagnifyingGlass">
        Der Handwerker wird den Auftrag vor Ort überprüfen und einen Kostenvoranschlag erstellen.
      </NextStep>
      <NextStep :icon="faCalendar">
        Sie vereinbaren mit dem Handwerker eine neuen Termin für die Durchführung Ihres Auftrags.
      </NextStep>
      <NextStep :icon="faTools">
        Der Handwerker führt die Arbeit pünktlich und fachgerecht aus und informiert Sie über den Fortschritt.
      </NextStep>
    </section>
  </div>
</template>
