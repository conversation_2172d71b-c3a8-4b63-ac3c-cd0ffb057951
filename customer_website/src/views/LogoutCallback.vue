<script lang="ts" setup>
import { onMounted } from 'vue';
import { userManager } from '@/auth/auth.ts';
import { useUserDetailsStore } from '@/stores/userDetails.ts';
import { useOrderRequestResponseStore, useOrderRequestStore } from '@/stores/orderRequest.ts';
import router from '@/router/routes.ts';

onMounted(async () => {
  try {
    await userManager.signoutCallback();
    await userManager.removeUser();
  } catch (error) {
    console.error('Logout callback failed', error);
  }

  useUserDetailsStore().$reset();
  useOrderRequestStore().$reset();
  useOrderRequestResponseStore().$reset();
  await router.replace('/');
});
</script>

<template>
  <p>Du wirst ausgeloggt...</p>
</template>
