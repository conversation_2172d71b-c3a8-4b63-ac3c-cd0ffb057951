<script lang="ts" setup>
import { onUnmounted, ref } from 'vue';
import DefaultButton from '@/components/DefaultButton.vue';
import InputWithIcons from '@/components/InputWithIcons.vue';
import { faAngleLeft, faEnvelope } from '@fortawesome/free-solid-svg-icons';
import router from '@/router/routes.ts';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import Card from '@/components/Card.vue';

const email = ref('');
const isLoading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');
const isButtonDisabled = ref(false);

let timeout: ReturnType<typeof setTimeout>;

const sendMagicLink = async () => {
  if (!email.value) {
    errorMessage.value = 'Bitte geben Sie eine E-Mail-Adresse ein.';
    return;
  }

  isButtonDisabled.value = true;

  errorMessage.value = '';
  successMessage.value = '';
  isLoading.value = true;

  try {
    const baseUrl = import.meta.env.VITE_ALLESKOENNER_BACKEND_URL;
    const response = await fetch(baseUrl + '/auth/magic-link-request', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify({ email: email.value }),
    });

    if (!response.ok) {
      throw new Error(`Fehler beim Senden des Login-Links: ${response.status}`);
    }

    successMessage.value =
      'Ein Login-Link wurde an Ihre E-Mail-Adresse gesendet. Bitte öffnen Sie den Link im selben Browser wie diese Seite.';

    timeout = setTimeout(() => {
      isButtonDisabled.value = false;
    }, 60 * 1000);
  } catch (error) {
    console.error('Error sending magic link:', error);
    errorMessage.value = 'Es ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.';
  } finally {
    isLoading.value = false;
  }
};

onUnmounted(() => {
  if (!!timeout) {
    clearTimeout(timeout);
  }
});
</script>

<template>
  <div class="items-start flex flex-col py-8 max-w-screen-sm px-6 self-center m-auto gap-4">
    <nav class="flex items-center gap-2" @click="router.back()">
      <FontAwesomeIcon :icon="faAngleLeft" class="text-cerulean-500" />
      <span class="text-cerulean-500">Zurück</span>
    </nav>

    <h2 class="text-xl font-medium">Anmeldung mit E-Mail</h2>

    <p class="text-gray-600 mb-4">
      Bitte geben Sie Ihre E-Mail-Adresse ein. Wir senden Ihnen dann einen Einmal-Login-Link zu. Ein Passwort muss nicht
      vergeben werden.
    </p>

    <div v-if="errorMessage" class="text-akRed text-sm mb-2">
      {{ errorMessage }}
    </div>

    <Card v-if="successMessage" :message="successMessage" card-type="success" class="mb-2" />

    <InputWithIcons
      v-model="email"
      :disabled="isLoading"
      :prefixIcon="faEnvelope"
      class="w-full"
      inputId="email-input"
      inputType="email"
      placeholder="E-Mail-Adresse"
      prefixIconColor="text-gray-400"
      @keydown.enter.prevent="sendMagicLink"
    />

    <DefaultButton
      :disabled="isLoading || isButtonDisabled"
      class="mt-3 w-full"
      size="lg"
      style-name="ak-blue"
      @click.prevent="sendMagicLink"
    >
      {{ isLoading ? 'Wird gesendet...' : 'Absenden' }}
    </DefaultButton>
  </div>
</template>
