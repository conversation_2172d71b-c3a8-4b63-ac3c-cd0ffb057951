<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import { faClipboardQuestion, faList, faThumbsUp, faTools, faWrench } from '@fortawesome/free-solid-svg-icons';
import InputWithIcons from '@/components/InputWithIcons.vue';
import StepDescription from '@/components/index_page/StepDescription.vue';
import MainDescription from '@/components/index_page/MainDescription.vue';
import router from '@/router/routes.ts';
</script>

<template>
  <div class="home-page-container items-start flex flex-col max-w-screen-sm self-center m-auto">
    <div class="mb-4 title-container w-full px-6 pt-12 pb-16">
      <h1 class="text-filled-cyan font-black text-7xl mb-4 tracking-tight">Handwerker</h1>

      <h2 class="text-stroke-white font-black text-7xl tracking-tight">Sofort-Service</h2>
    </div>
    <div class="w-full px-6 pb-8">
      <section class="w-full sticky z-10 top-4 -mt-10">
        <InputWithIcons
          :prefix-icon="faWrench"
          input-id="task"
          placeholder="Was soll erledigt werden?"
          placeholder-color="placeholder:text-gray-600"
          prefix-icon-background="bg-gray-100"
          prefix-icon-color="text-gray-400"
          test-id="task"
          @input-focussed="router.push('task')"
        />
      </section>
      <section>
        <MainDescription />
        <h3 class="text-bamboo-600 font-bold mt-7 self-start">So funktioniert DeinHandwerker365!</h3>
        <StepDescription
          :icon="faList"
          description="Reichen Sie Ihren Auftrag über unsere App oder Hotline ein und geben Sie Details zu Ihrem Bedarf an."
          title="Auftrag"
        />
        <StepDescription
          :icon="faClipboardQuestion"
          description="Unser System oder Team weist Ihren Auftrag einem qualifizierten Handwerker in Ihrer Nähe zu."
          title="Zuordnung"
        />
        <StepDescription
          :icon="faTools"
          description="Der Handwerker führt die Arbeit pünktlich und fachgerecht aus und informiert Sie über den Fortschritt."
          title="Durchführung"
        />
        <StepDescription
          :icon="faThumbsUp"
          description="Nach Abschluss bestätigen Sie die Arbeit und geben Ihr Feedback zur Qualität."
          title="Zufriedenheit"
        />
      </section>
    </div>
  </div>
</template>

<style scoped>
.title-container {
  background: #3a539a;
}

.home-page-container {
  min-height: 50.1vh;
}

.text-stroke-white {
  -webkit-text-stroke: 3px white;
  text-stroke: 3px white;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
}

.text-filled-cyan {
  -webkit-text-stroke: 3px white;
  text-stroke: 3px white;
  -webkit-text-fill-color: #06b6d4;
  text-fill-color: #06b6d4;
}
</style>
