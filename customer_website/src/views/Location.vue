<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import InputWithSuggestions from '../components/InputWithSuggestions.vue';
import { faHouse, faLocationDot } from '@fortawesome/free-solid-svg-icons';
import { getLocationSuggestionsFor } from '../services/locationSuggestionService.ts';
import InputSummaryButton from '../components/InputSummaryButton.vue';
import { type ComponentPublicInstance, onMounted, ref, watch } from 'vue';
import { type AddressStore } from '../types/OrderRequestStore.type.ts';
import router from '@/router/routes.ts';
import { useOrderRequestStore } from '@/stores/orderRequest.ts';
import type { Address } from '@/types/HereMapsAutocompleteResponse.type.ts';

const requestStore = useOrderRequestStore();

const addressInput = ref<string>('');
const addressInputRef = ref<ComponentPublicInstance<{
  focusInput: (position?: number) => void;
}> | null>(null);
const addressSuggestions = ref<AddressStore[]>([]);
const isHouseNumberMissing = ref<boolean>(false);
const isPostalCodeWarningVisible = ref<boolean>(false);

async function handleLocationSelection(newSuggestion: AddressStore) {
  const supportedPostalCodes = [
    '10585',
    '10587',
    '10589',
    '10623',
    '10625',
    '10627',
    '10629',
    '13627',
    '14057',
    '14059',
  ];
  const isPostalCodeSupported =
    !!newSuggestion.address.postalCode && supportedPostalCodes.includes(newSuggestion.address.postalCode);
  isPostalCodeWarningVisible.value = !isPostalCodeSupported;
  if (isPostalCodeSupported) {
    if (!!newSuggestion.address.houseNumber) {
      requestStore.shipToAddress = newSuggestion;
      await router.push('time');
    } else {
      isHouseNumberMissing.value = true;
      const label = newSuggestion.label;
      const positionOfHouseNumber = label.indexOf(',');
      addressInput.value = label.slice(0, positionOfHouseNumber) + ' ' + label.slice(positionOfHouseNumber);
      addressInputRef.value?.focusInput(positionOfHouseNumber + 1);
    }
  } else {
    addressInput.value = newSuggestion.label;
  }
}

onMounted(() => {
  if (requestStore.shipToAddress) {
    addressInput.value = requestStore.shipToAddress.label;
  }
  addressInputRef.value?.focusInput();
});

watch(addressInput, async (newLocationInput) => {
  const sleep = (ms: number) => {
    return new Promise((r) => setTimeout(r, ms));
  };

  addressSuggestions.value = [];
  await sleep(300);
  const hasLocationInputChangedDuringSleep = newLocationInput !== addressInput.value;
  if (!hasLocationInputChangedDuringSleep) {
    const response: Address[] = (await getLocationSuggestionsFor(newLocationInput)) ?? [];

    const toLocationStore = (item: Address): AddressStore => ({
      icon: faHouse,
      label: item.label,
      address: item,
    });

    addressSuggestions.value = response.map(toLocationStore);
  }
});
</script>

<template>
  <div class="items-start flex flex-col py-8 max-w-screen-sm px-6 self-center m-auto gap-1">
    <InputSummaryButton
      :prefix-icon="requestStore.taskCategory?.icon"
      :value="`${requestStore.taskCategory?.label || ''}: ${requestStore.taskDescription || ''}`"
      placeholder="Dienstleistung"
      @click.prevent="router.push('task')"
    />
    <h2 class="text-xl font-medium my-2">Wo soll es erledigt werden?</h2>
    <span v-if="isHouseNumberMissing" class="text-red-500">Bitte die Hausnummer ergänzen!</span>
    <p v-if="isPostalCodeWarningVisible" class="text-red-500">
      Wir sind ein junges Unternehmen und können daher noch nicht alle Gebiete in Berlin bedienen. Die eingegebene
      Adresse wird derzeit leider noch nicht unterstützt.
    </p>
    <InputWithSuggestions
      ref="addressInputRef"
      v-model="addressInput"
      :postfix-icon="faLocationDot"
      :suggestions="addressSuggestions"
      class="w-full"
      input-id="location"
      placeholder="Straße und Hausnummer eingeben"
      suggestion-icon-color="text-gray-200"
      @suggestion-selected="handleLocationSelection"
    />
  </div>
</template>
