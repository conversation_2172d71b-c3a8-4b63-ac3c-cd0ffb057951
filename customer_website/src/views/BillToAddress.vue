<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import InputWithSuggestions from '../components/InputWithSuggestions.vue';
import { faHouse, faLocationDot, faPerson } from '@fortawesome/free-solid-svg-icons';
import { getLocationSuggestionsFor } from '../services/locationSuggestionService.ts';
import InputSummaryButton from '../components/InputSummaryButton.vue';
import { type ComponentPublicInstance, onMounted, ref, watch } from 'vue';
import { type AddressStore } from '../types/OrderRequestStore.type.ts';
import router from '@/router/routes.ts';
import { useOrderRequestStore } from '@/stores/orderRequest.ts';
import type { Address } from '@/types/HereMapsAutocompleteResponse.type.ts';
import InputWithIcons from '@/components/InputWithIcons.vue';
import DefaultButton from '@/components/DefaultButton.vue';
import { useUserDetailsStore } from '@/stores/userDetails.ts';

const userDetails = useUserDetailsStore();
const orderRequest = useOrderRequestStore();

const addressInput = ref<string>(orderRequest.billToAddress?.label ?? '');
const addressInputRef = ref<ComponentPublicInstance<{
  focusInput: (position?: number) => void;
}> | null>(null);
const confirmButtonRef = ref(null);

const addressSuggestions = ref<AddressStore[]>([]);
const selectedAddress = ref<AddressStore>();
const name = ref<string>('');

const isNameMissing = ref<boolean>(false);
const isAddressMissing = ref<boolean>(false);
const isHouseNumberMissing = ref<boolean>(false);

function focusAddressInput() {
  addressInputRef.value?.focusInput();
}

async function handleNameEnterPress() {
  isNameMissing.value = !name.value;
  if (!isNameMissing.value) {
    focusAddressInput();
  }
}

async function handleAddressSelection(newSuggestion: AddressStore) {
  isAddressMissing.value = false;
  selectedAddress.value = newSuggestion;
  isHouseNumberMissing.value = !newSuggestion.address.houseNumber;
  if (!isHouseNumberMissing.value) {
    addressInput.value = newSuggestion.label;
    if (addressSuggestions.value.length <= 1) {
      await handleConfirmation();
    } else {
      addressSuggestions.value = [];
    }
  } else {
    isHouseNumberMissing.value = true;
    const label = newSuggestion.label;
    const positionOfHouseNumber = label.indexOf(',');
    addressInput.value = label.slice(0, positionOfHouseNumber) + ' ' + label.slice(positionOfHouseNumber);
    addressInputRef.value?.focusInput(positionOfHouseNumber + 1);
  }
}

async function handleAddressEnterPressedWithoutSuggestions() {
  if (addressInput.value === selectedAddress.value?.label) {
    await handleConfirmation();
  } else {
    isAddressMissing.value = true;
    selectedAddress.value = undefined;
  }
}

async function handleConfirmation() {
  isNameMissing.value = !name.value;
  isAddressMissing.value = !selectedAddress.value;
  if (isNameMissing.value || isAddressMissing.value || isHouseNumberMissing.value) {
    return;
  }
  orderRequest.isBillToDifferent = true;
  orderRequest.billToName = name.value;
  orderRequest.billToAddress = selectedAddress.value;
  await router.push('summary');
}

async function handleSameAsShipTo() {
  orderRequest.isBillToDifferent = false;
  orderRequest.billToName = null;
  orderRequest.billToAddress = null;
  await router.push('summary');
}

onMounted(() => {
  name.value = orderRequest.billToName ?? userDetails.name ?? '';
  if (!!orderRequest.billToAddress) {
    selectedAddress.value = orderRequest.billToAddress;
  } else {
    focusAddressInput();
  }
});

watch(addressInput, async (newLocationInput) => {
  const sleep = (ms: number) => {
    return new Promise((r) => setTimeout(r, ms));
  };

  addressSuggestions.value = [];
  await sleep(300);
  const hasLocationInputChangedDuringSleep = newLocationInput !== addressInput.value;
  if (!hasLocationInputChangedDuringSleep) {
    const response: Address[] = (await getLocationSuggestionsFor(newLocationInput)) ?? [];

    const toLocationStore = (item: Address): AddressStore => ({
      icon: faHouse,
      label: item.label,
      address: item,
    });

    addressSuggestions.value = response.map(toLocationStore);
  }
});
</script>

<template>
  <div class="items-start flex flex-col py-8 max-w-screen-sm px-6 self-center m-auto gap-1">
    <InputSummaryButton
      :value="orderRequest.shipToAddress?.label"
      class="mt-2"
      placeholder="Arbeitsadresse"
      @click.prevent="router.push('location')"
    />
    <DefaultButton style-name="link" @click.prevent="handleSameAsShipTo">
      Arbeitsadresse als Rechnungsadresse verwenden
    </DefaultButton>
    <h2 class="text-xl font-medium my-2">An wen soll die Rechnung gehen?</h2>
    <span v-if="isNameMissing" class="text-red-500">Bitte den Rechnungsempfänger angeben!</span>
    <InputWithIcons
      v-model="name"
      :postfix-icon="faPerson"
      class="w-full"
      input-id="name"
      placeholder="Name des Rechnungsnehmers eingeben"
      @keydown.enter.prevent="handleNameEnterPress"
    />
    <span v-if="isAddressMissing" class="text-red-500">Bitte die Adresse angeben!</span>
    <span v-else-if="isHouseNumberMissing" class="text-red-500">Bitte die Hausnummer ergänzen!</span>
    <InputWithSuggestions
      ref="addressInputRef"
      v-model="addressInput"
      :postfix-icon="faLocationDot"
      :suggestions="addressSuggestions"
      class="w-full mb-2"
      input-id="location"
      placeholder="Straße und Hausnummer eingeben"
      suggestion-icon-color="text-gray-200"
      @suggestion-selected="handleAddressSelection"
      @enter-pressed-without-suggestions="handleAddressEnterPressedWithoutSuggestions"
    />
    <DefaultButton
      ref="confirmButtonRef"
      class-name="w-full mt-6"
      style-name="ak-blue"
      @click.prevent="handleConfirmation"
    >
      Bestätigen
    </DefaultButton>
  </div>
</template>
