<script lang="ts" setup>
import InputSummaryButton from '../components/InputSummaryButton.vue';
import { faClockFour } from '@fortawesome/free-solid-svg-icons';
import { formattedTime } from '../services/orderRequestUtils.ts';
import router from '@/router/routes.ts';
import { useOrderRequestStore } from '@/stores/orderRequest.ts';
import LoginOptions from '@/components/LoginOptions.vue';
import { login } from '@/auth/auth.ts';

const requestStore = useOrderRequestStore();

function handleGoogleLogin() {
  login('/summary', 'google');
}

function handleMicrosoftLogin() {
  login('/summary', 'microsoft');
}
</script>

<template>
  <div class="items-start flex flex-col py-8 max-w-screen-sm px-6 self-center m-auto gap-1">
    <InputSummaryButton
      :prefix-icon="requestStore.taskCategory?.icon"
      :value="`${requestStore.taskCategory?.label || ''}: ${requestStore.taskDescription || ''}`"
      placeholder="Dienstleistung"
      @click.prevent="router.push('task')"
    />
    <InputSummaryButton
      :prefix-icon="requestStore.shipToAddress?.icon"
      :value="requestStore.shipToAddress?.label"
      placeholder="Standort"
      @click.prevent="router.push('location')"
    />
    <InputSummaryButton
      :prefix-icon="faClockFour"
      :value="formattedTime(requestStore.time)"
      placeholder="Wunschtermin"
      @click.prevent="router.push('time')"
    />
    <LoginOptions :on-google-login="handleGoogleLogin" :on-microsoft-login="handleMicrosoftLogin" />
  </div>
</template>
