<script lang="ts" setup>
import { onMounted } from 'vue';
import router from '@/router/routes.ts';
import { fetchTokenAndSetUserDetailsFromBackend } from '@/auth/auth.ts';

onMounted(async () => {
  try {
    const params = new URLSearchParams(window.location.search);
    await fetchTokenAndSetUserDetailsFromBackend(params.get('code'));

    await router.push('/summary');
  } catch (error) {
    console.error('Magic link callback error:', error);
  }
});
</script>

<template>
  <div class="flex h-screen items-center justify-center text-gray-500 text-sm">Anmeldung wird verarbeitet ...</div>
</template>
