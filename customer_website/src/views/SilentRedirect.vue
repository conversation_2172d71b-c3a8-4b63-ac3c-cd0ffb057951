<script lang="ts" setup>
import { onMounted } from 'vue';
import { userManager } from '@/auth/auth.ts';

onMounted(async () => {
  try {
    await userManager.signinSilentCallback();
    console.log('Silent signin callback processed');
  } catch (err) {
    console.error('Silent signin failed', err);
  }
});
</script>

<template>
  <div class="text-sm text-gray-500">Silent Login ...</div>
</template>
