<script lang="ts" setup>
import { login } from '@/auth/auth.ts';
import LoginOptions from '@/components/LoginOptions.vue';

function handleGoogleLogin() {
  login('/summary', 'google');
}

function handleMicrosoftLogin() {
  login('/summary', 'microsoft');
}
</script>

<template>
  <div class="items-start flex flex-col py-8 max-w-screen-sm px-6 self-center m-auto gap-1">
    <LoginOptions :on-google-login="handleGoogleLogin" :on-microsoft-login="handleMicrosoftLogin" />
  </div>
</template>
