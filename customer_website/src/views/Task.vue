<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import { type ComponentPublicInstance, computed, onMounted, ref } from 'vue';
import { useOrderRequestResponseStore, useOrderRequestStore } from '@/stores/orderRequest.ts';
import router from '@/router/routes.ts';
import { availableTaskSuggestions } from '@/data/taskSuggestions';
import DefaultButton from '@/components/DefaultButton.vue';
import TaskTextArea from '@/components/TaskTextArea.vue';
import { useTaskInput } from '@/composables/useTaskInput.ts';
import Dropdown from '@/components/Dropdown.vue';
import type { TaskCategoryStore } from '@/types/OrderRequestStore.type.ts';
import { faWrench } from '@fortawesome/free-solid-svg-icons';

const taskInputRef = ref<ComponentPublicInstance<{ focusInput: () => void }> | null>(null);
const taskCategoryRef = ref<ComponentPublicInstance<{ focusInput: () => void }> | null>(null);
const { taskInput, isTaskInputValid, shouldShowInputError, wasSubmitted } = useTaskInput();

const orderRequest = useOrderRequestStore();

const taskCategoryInput = ref<TaskCategoryStore | null>(null);
const isTaskCategorySelected = computed(() => !!taskCategoryInput.value);
const shouldShowCategoryError = computed(() => wasSubmitted.value && !isTaskCategorySelected.value);
const taskCategoryIcon = computed(() => taskCategoryInput.value?.icon || faWrench);
const taskCategoryIconColor = computed(() => (isTaskCategorySelected.value ? 'text-cerulean-500' : 'text-gray-400'));

onMounted(() => {
  useOrderRequestResponseStore().$reset();

  if (orderRequest.taskCategory) {
    taskCategoryInput.value = orderRequest.taskCategory;
  }
  if (orderRequest.taskDescription) {
    taskInput.value = orderRequest.taskDescription;
  }
  taskCategoryRef.value?.focusInput();
});

async function handleTaskCategorySelection(choice: TaskCategoryStore) {
  taskCategoryInput.value = choice;
  taskInputRef.value?.focusInput();
}

async function handleTaskSubmission() {
  wasSubmitted.value = true;

  if (!isTaskCategorySelected.value) {
    taskCategoryRef.value?.focusInput();
    return;
  }

  if (!isTaskInputValid.value) {
    taskInputRef.value?.focusInput();
    return;
  }

  orderRequest.taskCategory = taskCategoryInput.value;
  orderRequest.taskDescription = taskInput.value;

  await router.push('location');
}
</script>

<template>
  <div class="items-start flex flex-col py-8 max-w-screen-sm px-6 self-center m-auto">
    <h2 class="text-xl font-medium">Was soll erledigt werden?</h2>

    <Dropdown
      ref="taskCategoryRef"
      v-model="taskCategoryInput"
      :options="availableTaskSuggestions"
      :prefix-icon="taskCategoryIcon"
      :prefix-icon-color="taskCategoryIconColor"
      class="w-full mb-2"
      input-id="taskCategory"
      placeholder="Bitte wählen Sie eine Kategorie"
      prefix-icon-background="bg-gray-100"
      test-id="taskCategory"
      @suggestion-selected="handleTaskCategorySelection"
    />

    <span v-if="shouldShowCategoryError" class="text-sm text-akRed mb-2">Bitte wählen Sie eine Kategorie aus.</span>

    <span class="text-sm mt-2 mb-4 text-pretty">
      Bitte beschreiben Sie Ihre Handwerker-Anfrage so, dass ein Handwerker grob abschätzen kann, wie viel Arbeit es
      sein wird.
    </span>

    <span v-if="shouldShowInputError" class="text-sm text-akRed">Die Beschreibung darf nicht zu kurz sein.</span>

    <TaskTextArea ref="taskInputRef" v-model="taskInput" class="mt-1" @submit="handleTaskSubmission" />

    <DefaultButton
      :style-name="isTaskCategorySelected && isTaskInputValid ? 'ak-blue' : 'mockDisabledBlue'"
      class="mt-3 w-full"
      size="lg"
      test-id="submit-button"
      @click.prevent="handleTaskSubmission"
    >
      Weiter
    </DefaultButton>
  </div>
</template>
