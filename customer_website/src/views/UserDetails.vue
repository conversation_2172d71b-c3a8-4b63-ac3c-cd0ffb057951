<script lang="ts" setup>
import {
  faAngleLeft,
  faEnvelope,
  faPhone,
  faSignature,
  faTriangleExclamation,
} from '@fortawesome/free-solid-svg-icons';
import InputWithIcons from '@/components/InputWithIcons.vue';
import { computed, onMounted, ref } from 'vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import router from '@/router/routes.ts';
import DefaultButton from '@/components/DefaultButton.vue';
import { useUserDetailsStore } from '@/stores/userDetails.ts';
import { hasCustomerImpersonationRole, userState } from '@/auth/auth.ts';

const userDetails = useUserDetailsStore();

const nameInput = ref<string>('');
const phoneInput = ref<string>('');
const emailInput = ref<string>('');

onMounted(() => {
  if (!!userState.value) {
    userDetails.name = userDetails.name ? userDetails.name : userState.value?.profile.name || '';
    userDetails.email = userDetails.email ? userDetails.email : userState.value?.profile.email || '';
    userDetails.type = 'Person';
  }
  nameInput.value = userDetails.name;
  emailInput.value = userDetails.email;
  phoneInput.value = userDetails.phone;
});

const isValidNameFlag = ref(true);
const isValidPhoneNumberFlag = ref(true);
const isValidEmailFlag = ref(true);

const minimumNumberOfLettersForName = 3;
const isValidName = computed(() => {
  return nameInput.value.length >= minimumNumberOfLettersForName;
});

const minimumNumberOfLettersForPhoneNumber = 5;
const isValidPhone = computed(() => {
  const hasValidNumberOfSymbols = phoneInput.value.length >= minimumNumberOfLettersForPhoneNumber;
  const hasNotLetters = !/[a-zA-Z]/.test(phoneInput.value);
  return hasValidNumberOfSymbols && hasNotLetters;
});

const minimumNumberOfLettersForEmail = 5;
const isValidEmail = computed(() => {
  const validNumberOfLetters = emailInput.value.length >= minimumNumberOfLettersForEmail;
  const hasNotLetters = /.*@.*/.test(emailInput.value);
  return validNumberOfLetters && hasNotLetters;
});
const areAllFieldsValid = computed(() => {
  return isValidName.value && isValidPhone.value && isValidEmail.value;
});

const handleSaveClick = () => {
  if (!areAllFieldsValid.value) {
    isValidNameFlag.value = isValidName.value;
    isValidPhoneNumberFlag.value = isValidPhone.value;
    isValidEmailFlag.value = isValidEmail.value;
    return;
  }

  if (userDetails) {
    userDetails.name = nameInput.value;
    userDetails.phone = phoneInput.value;
    if (hasCustomerImpersonationRole) {
      userDetails.email = emailInput.value;
    }
  }
  router.push('summary');
};
</script>

<template>
  <div class="items-start flex flex-col py-8 max-w-screen-sm px-6 self-center m-auto gap-1">
    <nav class="flex items-center gap-2" @click="router.back()">
      <FontAwesomeIcon :icon="faAngleLeft" class="text-cerulean-500" />
      <span class="text-cerulean-500">Zurück</span>
    </nav>

    <h2 class="text-xl font-medium mt-2">Kontaktdaten</h2>

    <span v-if="!hasCustomerImpersonationRole" class="text-sm my-2 text-pretty" test-id="user-details-info">
      Bitte ergänzen Sie Ihre Kontaktdaten, damit wir Sie bei Fragen zu Ihren Aufträgen erreichen können.
    </span>

    <div
      v-else
      class="my-2 border-2 border-cerulean-500 p-3 rounded flex items-center gap-2"
      test-id="user-details-info"
    >
      <FontAwesomeIcon :icon="faTriangleExclamation" class="flex p-2 pr-3 text-xl text-yellow-400" />
      <span class="flex text-sm font-bold text-pretty">
        Sie haben die Berechtigung für einen Kunden einen Auftrag zu erteilen. Bitte achten Sie auf sorgfältige Eingabe
        der Kundendaten.
      </span>
    </div>

    <section ref="userDetailsInputSection" class="w-full flex flex-col gap-2">
      <span v-if="!isValidNameFlag" class="text-sm my-1 text-akRed">Der Name scheint nicht korrekt zu sein.</span>
      <InputWithIcons
        v-model="nameInput"
        :auto-complete="'name'"
        :prefix-icon="faSignature"
        input-id="name"
        placeholder="Vor- und Nachname *"
        placeholder-color="placeholder:text-gray-600"
        prefix-icon-background="bg-gray-100"
        prefix-icon-color="text-gray-400"
        test-id="name"
      />

      <span v-if="!isValidPhoneNumberFlag" class="text-sm my-1 text-akRed">
        Die Telefonnummer scheint nicht korrekt zu sein.
      </span>
      <InputWithIcons
        v-model="phoneInput"
        :auto-complete="'on'"
        :inputType="'tel'"
        :prefix-icon="faPhone"
        input-id="phone"
        placeholder="Telefonnummer *"
        placeholder-color="placeholder:text-gray-600"
        prefix-icon-background="bg-gray-100"
        prefix-icon-color="text-gray-400"
        test-id="phone-number"
      />

      <span v-if="!isValidEmailFlag" class="text-sm my-1 text-akRed">
        Die E-Mail-Adresse scheint nicht korrekt zu sein.
      </span>
      <InputWithIcons
        v-model="emailInput"
        :auto-complete="'on'"
        :disabled="!hasCustomerImpersonationRole"
        :inputType="'email'"
        :prefix-icon="faEnvelope"
        input-id="email"
        placeholder="E-Mail-Adresse"
        placeholder-color="placeholder:text-gray-600"
        prefix-icon-background="bg-gray-100"
        prefix-icon-color="text-gray-400"
        test-id="email-address"
      />
    </section>

    <DefaultButton
      :style-name="areAllFieldsValid ? 'ak-blue' : 'mockDisabledBlue'"
      class="w-full mt-3"
      test-id="save-button"
      @click.prevent="handleSaveClick"
    >
      Speichern
    </DefaultButton>
  </div>
</template>
