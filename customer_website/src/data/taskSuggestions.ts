import {
  faBorder<PERSON>ll,
  faHouse<PERSON>ser,
  fa<PERSON>ey,
  faP<PERSON>t<PERSON>oller,
  faScrewdriver,
  faScrewdriverWrench,
  faToilet,
  faWrench,
} from '@fortawesome/free-solid-svg-icons';
import type { TaskCategoryStore } from '../types/OrderRequestStore.type';

export const availableTaskSuggestions: TaskCategoryStore[] = [
  {
    icon: faToilet,
    label: 'Sanitärreparaturen und -Installationen (Armaturen, Spülkästen, Abflüsse)',
    id: 'SanitaryWorks',
  },
  {
    icon: faPaintRoller,
    label: 'Maler- und Ausbesserungsarbeiten (Wände, Fliesen, Böden)',
    id: 'Painting',
  },
  {
    icon: faScrewdriver,
    label: 'Allgemeiner Möbelaufbau',
    id: 'FurnitureAssembly',
  },
  {
    icon: faWrench,
    label: 'Allgemeine Reparaturen (Elektro, Türen, Fenster)',
    id: 'Repairs',
  },
  {
    icon: faBorder<PERSON>ll,
    label: 'Verleg<PERSON> von Wand- und Bodenfliesen',
    id: 'Tiling',
  },
  {
    icon: faHouseUser,
    label: 'Regelmäßiger Hausmeisterdienst (Kontrolle, Kleinreparaturen)',
    id: 'Caretaker',
  },
  {
    icon: faKey,
    label: 'Wohnungsübergaben & Nutzerbetreuung (Abnahmen, Schlüssel, Hilfe)',
    id: 'Handover',
  },
  {
    icon: faScrewdriverWrench,
    label: 'Sonstiges',
    id: 'Other',
  },
];
