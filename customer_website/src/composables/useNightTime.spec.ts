import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';

import { useNightTime } from '@/composables/useNightTime';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(isSameOrAfter);

function setTimeInBerlin(hour: number, minute = 0) {
  const date = dayjs.tz('2025-01-01', 'Europe/Berlin').hour(hour).minute(minute).second(0).millisecond(0).toDate();
  vi.setSystemTime(date);
}

function setTimeUtc(isoString: string) {
  vi.setSystemTime(new Date(isoString));
}

function setEnv(start = '22:00', end = '06:00', reaction = '15') {
  vi.stubEnv('VITE_QUIET_HOURS_START', start);
  vi.stubEnv('VITE_QUIET_HOURS_END', end);
  vi.stubEnv('VITE_APPOINTMENT_REQUEST_ACCEPTANCE_TIMEOUT_WINDOW_IN_MINUTES', reaction);
}

describe('useNightTime', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.restoreAllMocks();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Berlin local time', () => {
    it('returns true at 21:45 (reactiontime applied)', () => {
      setEnv('22:00', '06:00', '15');
      setTimeInBerlin(21, 45);
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(true);
    });

    it('returns false at 21:44 (just before night window)', () => {
      setEnv('22:00', '06:00', '15');
      setTimeInBerlin(21, 44);
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(false);
    });

    it('returns true at 05:59 (still night)', () => {
      setEnv('22:00', '06:00', '15');
      setTimeInBerlin(5, 59);
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(true);
    });

    it('returns false at 06:00 (night ends)', () => {
      setEnv('22:00', '06:00', '15');
      setTimeInBerlin(6, 0);
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(false);
    });

    it('returns false at 10:00 (clear daytime)', () => {
      setEnv('22:00', '06:00', '15');
      setTimeInBerlin(10, 0);
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(false);
    });

    it('handles invalid reaction time gracefully (fallback to 0)', () => {
      setEnv('22:00', '06:00', 'not-a-number');
      setTimeInBerlin(22, 0);
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(true);
    });

    it('handles same-day window (start < end)', () => {
      setEnv('01:00', '05:00', '10');

      setTimeInBerlin(0, 49);
      const { isNightTime: beforeWindow } = useNightTime();
      expect(beforeWindow.value).toBe(false);

      setTimeInBerlin(0, 50);
      const { isNightTime: insideWindow } = useNightTime();
      expect(insideWindow.value).toBe(true);
    });
  });

  describe('UTC-based time (timezone shift)', () => {
    it('returns true at 21:00 UTC (23:00 Berlin = night)', () => {
      setEnv('22:00', '06:00', '0');
      setTimeUtc('2025-05-01T21:00:00Z');
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(true);
    });

    it('returns true at 04:00 UTC (06:00 Berlin with reactiontime)', () => {
      setEnv('06:15', '08:00', '15');
      setTimeUtc('2025-05-01T04:00:00Z');
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(true);
    });

    it('returns false at 05:00 UTC (07:00 Berlin = not night anymore)', () => {
      setEnv('22:00', '06:00', '15');
      setTimeUtc('2025-05-01T05:00:00Z');
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(false);
    });

    it('returns false at 12:00 UTC (14:00 Berlin = daytime)', () => {
      setEnv('22:00', '06:00', '15');
      setTimeUtc('2025-05-01T12:00:00Z');
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(false);
    });

    it('returns true at 20:45 UTC (22:45 Berlin, inside night window)', () => {
      setEnv('22:00', '06:00', '15');
      setTimeUtc('2025-05-01T20:45:00Z');
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(true);
    });

    it('returns true at 20:44 UTC (22:44 Berlin, already night)', () => {
      setEnv('22:00', '06:00', '15');
      setTimeUtc('2025-05-01T20:44:00Z');
      const { isNightTime } = useNightTime();
      expect(isNightTime.value).toBe(true);
    });
  });
});
