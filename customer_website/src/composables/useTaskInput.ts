import { computed, ref } from 'vue';

export function useTaskInput(minLength = 10) {
  const taskInput = ref('');
  const wasSubmitted = ref(false);

  const shouldShowInputError = computed(() => {
    return wasSubmitted.value && !isTaskInputValid.value;
  });

  const isTaskInputValid = computed(() => {
    return taskInput.value.trim().length >= minLength;
  });

  return {
    taskInput,
    isTaskInputValid,
    shouldShowInputError,
    wasSubmitted,
  };
}
