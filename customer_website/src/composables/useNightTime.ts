import { computed } from 'vue';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(isSameOrAfter);

function parseTimeString(time: string): dayjs.Dayjs {
  return dayjs(time, 'HH:mm');
}

function parseReactionTime(): number {
  const minutes = Number(import.meta.env.VITE_APPOINTMENT_REQUEST_ACCEPTANCE_TIMEOUT_WINDOW_IN_MINUTES);
  const fallbackTimeoutWindow = 15;
  return isNaN(minutes) ? fallbackTimeoutWindow : minutes;
}

export function useNightTime() {
  const startTime = parseTimeString(import.meta.env.VITE_QUIET_HOURS_START || '22:00');
  const endTime = parseTimeString(import.meta.env.VITE_QUIET_HOURS_END || '06:00');
  const reactionMinutes = parseReactionTime();

  const effectiveNightStart = startTime.subtract(reactionMinutes, 'minute');

  const isNightTime = computed(() => {
    const now = dayjs().tz('Europe/Berlin');
    const timeOnly = parseTimeString(now.format('HH:mm'));

    if (startTime.isBefore(endTime)) {
      return timeOnly.isSameOrAfter(effectiveNightStart) && timeOnly.isBefore(endTime);
    } else {
      return timeOnly.isSameOrAfter(effectiveNightStart) || timeOnly.isBefore(endTime);
    }
  });

  return { isNightTime };
}
