import { describe, expect, it, vi } from 'vitest';
import { defineComponent, ref } from 'vue';
import { mount } from '@vue/test-utils';
import { useClickOutside } from '@/composables/useClickOutside';

describe('useClickOutside', () => {
  it('triggers handler on outside click and cleans up listener on unmount', async () => {
    const handler = vi.fn();
    const addSpy = vi.spyOn(document, 'addEventListener');
    const removeSpy = vi.spyOn(document, 'removeEventListener');

    const TestComp = defineComponent({
      template: '<div ref="aRef"><span>inside</span></div>',
      setup() {
        const aRef = ref<HTMLElement | null>(null);
        useClickOutside(aRef, handler);
        return { aRef };
      },
    });

    const wrapper = mount(TestComp);

    expect(addSpy).toHaveBeenCalledWith('click', expect.any(Function));
    const onClick = addSpy.mock.calls[0][1] as EventListener;

    document.body.click();
    expect(handler).toHaveBeenCalledTimes(1);

    await wrapper.find('span').trigger('click');
    expect(handler).toHaveBeenCalledTimes(1);

    wrapper.unmount();
    expect(removeSpy).toHaveBeenCalledWith('click', onClick);

    document.body.click();
    expect(handler).toHaveBeenCalledTimes(1);
  });
});
