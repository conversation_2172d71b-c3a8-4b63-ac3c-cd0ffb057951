import { describe, expect, it } from 'vitest';
import { useTaskInput } from '@/composables/useTaskInput';

describe('useTaskInput', () => {
  it('validates input length', () => {
    const { taskInput, isTaskInputValid } = useTaskInput(5);

    taskInput.value = '  foo ';
    expect(isTaskInputValid.value).toBe(false);

    taskInput.value = '  hello  ';
    expect(isTaskInputValid.value).toBe(true);
  });

  it('shows error only when submitted with invalid input', () => {
    const { taskInput, wasSubmitted, shouldShowInputError } = useTaskInput(4);

    taskInput.value = 'bar';
    expect(shouldShowInputError.value).toBe(false);

    wasSubmitted.value = true;
    expect(shouldShowInputError.value).toBe(true);

    taskInput.value = 'foobar';
    expect(shouldShowInputError.value).toBe(false);
  });
});
