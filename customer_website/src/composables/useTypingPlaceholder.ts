import { computed, onBeforeUnmount, onMounted, ref } from 'vue';

export function useTypingPlaceholder(
  texts: string[],
  options?: {
    typingSpeed?: number;
    pauseDuration?: number;
    isPausedExternally?: () => boolean;
  },
) {
  const placeholder = ref('');
  const currentTextIndex = ref(0);
  const currentCharIndex = ref(0);

  const typingSpeed = options?.typingSpeed ?? 7;
  const pauseDuration = options?.pauseDuration ?? 2000;

  const isPaused = computed(() => options?.isPausedExternally?.() ?? false);

  let lastTimestamp = 0;
  let pauseStart = 0;
  let frameHandle = 0;

  const typeNextChar = (timestamp: number) => {
    if (isPaused.value || shouldWaitForNextFrame(timestamp)) {
      return scheduleNextFrame();
    }

    lastTimestamp = timestamp;

    if (hasMoreCharsToType()) {
      appendNextChar();
    } else {
      handlePauseAfterText(timestamp);
    }

    scheduleNextFrame();
  };

  const shouldWaitForNextFrame = (timestamp: number) => timestamp - lastTimestamp < typingSpeed;

  const hasMoreCharsToType = () => {
    const currentText = texts[currentTextIndex.value];
    return currentCharIndex.value < currentText.length;
  };

  const appendNextChar = () => {
    const currentText = texts[currentTextIndex.value];
    placeholder.value = currentText.substring(0, currentCharIndex.value + 1);
    currentCharIndex.value++;
  };

  const handlePauseAfterText = (timestamp: number) => {
    if (!pauseStart) {
      pauseStart = timestamp;
    }

    const isPauseElapsed = timestamp - pauseStart >= pauseDuration;

    if (isPauseElapsed) {
      currentTextIndex.value = (currentTextIndex.value + 1) % texts.length;
      currentCharIndex.value = 0;
      placeholder.value = '';
      pauseStart = 0;
    }
  };

  const scheduleNextFrame = () => {
    frameHandle = requestAnimationFrame(typeNextChar);
  };

  onMounted(() => {
    scheduleNextFrame();
  });

  onBeforeUnmount(() => {
    cancelAnimationFrame(frameHandle);
  });

  return {
    placeholder,
  };
}
