import { onBeforeUnmount, onMounted, type Ref } from 'vue';

export function useClickOutside(refElement: Ref<HTMLElement | null>, handler: (e: MouseEvent) => void) {
  const onClick = (event: MouseEvent) => {
    if (refElement.value && !refElement.value.contains(event.target as Node)) {
      handler(event);
    }
  };

  onMounted(() => {
    document.addEventListener('click', onClick);
  });

  onBeforeUnmount(() => {
    document.removeEventListener('click', onClick);
  });
}
