export type Highlight = {
  start: number;
  end: number;
};

export type AddressHighlights = {
  label?: Highlight[];
  country?: Highlight[];
  city?: Highlight[];
  state?: Highlight[];
};

export type Highlights = {
  title?: Highlight[];
  address?: AddressHighlights;
};

export type Address = {
  label: string;
  countryCode: string;
  countryName: string;
  stateCode?: string;
  state?: string;
  street?: string;
  countyCode?: string;
  county?: string;
  city?: string;
  postalCode?: string;
  houseNumber?: string;
};

export type AutocompleteResponseItem = {
  title: string;
  id: string;
  language: string;
  resultType: string;
  administrativeAreaType?: string;
  localityType?: string;
  address: Address;
  distance: number;
  highlights: Highlights;
};

export type AutocompleteResponse = {
  items: AutocompleteResponseItem[];
};
