export type OrderDetailsRequest = {
  shipToName: string;
  shipToAddressLine1: string;
  shipToAddressLine2?: string;
  shipToCity: string;
  shipToPostalCode: string;
  shipToEmail: string;
  shipToPhoneNumber?: string;
  billToName: string;
  billToAddressLine1: string;
  billToAddressLine2?: string;
  billToCity: string;
  billToPostalCode: string;
  billToEmail: string;
  billToPhoneNumber?: string;
  email: string;
  customerType: string;
  taskCategory: string;
  taskDescription: string;
  isConditionsChecked: boolean;
  isAsSoonAsPossible: boolean;
  appointmentRequestDate?: Date;
  requestedHandymanSkill?: string;
};
