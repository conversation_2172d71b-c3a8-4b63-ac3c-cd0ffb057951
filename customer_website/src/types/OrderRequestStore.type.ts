import type { Address } from '@/types/HereMapsAutocompleteResponse.type.ts';
import type { Suggestion } from '@/components/InputWithSuggestions.vue';

export type TaskCategoryStore = Suggestion & {
  id: string;
};

export type AddressStore = Suggestion & {
  address: Address;
};

export type TimeStore = string | 'asap';

export type UserDetailsStore = {
  name: string;
  email: string;
  phone: string;
  type: 'Person' | 'Company';
};

export type OrderRequestStore = {
  taskCategory?: TaskCategoryStore | null;
  shipToAddress?: AddressStore | null;
  taskDescription?: string | null;
  isBillToDifferent: boolean;
  billToAddress?: AddressStore | null;
  billToName?: string | null;
  time?: TimeStore | null;
  isConditionsChecked: boolean;
};

export type OrderRequestResponseStore = {
  shipToName?: string | null;
  shipToAddressLine1?: string | null;
  shipToAddressLine2?: string | null;
  shipToCity?: string | null;
  shipToPostalCode?: string | null;
  shipToEmail?: string | null;
  shipToPhoneNumber?: string | null;
  billToName?: string | null;
  billToAddressLine1?: string | null;
  billToAddressLine2?: string | null;
  billToCity?: string | null;
  billToPostalCode?: string | null;
  billToEmail?: string | null;
  billToPhoneNumber?: string | null;
  email?: string | null;
  type?: string | null;
  taskCategory?: string;
  taskDescription?: string | null;
  isConditionsChecked?: boolean;
  salesOrderNumber?: string | null;
};
