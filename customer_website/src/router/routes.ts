import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';
import { useOrderRequestStore } from '@/stores/orderRequest.ts';
import { hasCustomerImpersonationRole, userManager, userState } from '@/auth/auth.ts';
import { useUserDetailsStore } from '@/stores/userDetails.ts';
import LogoutCallback from '@/views/LogoutCallback.vue';

const redirectIf = (
  conditionCallback: () => boolean,
  redirectRouteName: string,
  orElseCallback?: (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext,
  ) => Promise<void>,
) => {
  return async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    const isConditionTrue = conditionCallback();
    if (isConditionTrue) {
      next({ name: redirectRouteName });
    } else {
      if (!!orElseCallback) {
        await orElseCallback(to, from, next);
      } else {
        next();
      }
    }
  };
};

const routes = [
  {
    path: '/',
    name: 'index',
    component: () => import('../views/Index.vue'),
  },
  {
    path: '/task',
    name: 'task',
    component: () => import('../views/Task.vue'),
  },
  {
    path: '/location',
    name: 'location',
    component: () => import('../views/Location.vue'),
    beforeEnter: redirectIf(
      () => !useOrderRequestStore().taskCategory || !useOrderRequestStore().taskDescription,
      'index',
    ),
  },
  {
    path: '/time',
    name: 'time',
    component: () => import('../views/Time.vue'),
    beforeEnter: redirectIf(
      () =>
        !useOrderRequestStore().taskCategory ||
        !useOrderRequestStore().taskDescription ||
        !useOrderRequestStore().shipToAddress,
      'index',
    ),
  },
  {
    path: '/logout-callback',
    name: 'LogoutCallback',
    component: LogoutCallback,
  },
  {
    path: '/user-login',
    name: 'UserLogin',
    component: () => import('@/views/UserLogin.vue'),
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/Login.vue'),
    beforeEnter: redirectIf(
      () =>
        !useOrderRequestStore().taskCategory ||
        !useOrderRequestStore().taskDescription ||
        !useOrderRequestStore().shipToAddress ||
        !useOrderRequestStore().time,
      'index',
      async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
        const isUserAlreadyLoggedIn = !!(await userManager.getUser());
        if (isUserAlreadyLoggedIn) {
          if (!isAllMandatoryUserDetailsFilled()) {
            console.log('Not all mandatory user details are filled. Redirecting to user-details');
            next('user-details');
          } else if (hasCustomerImpersonationRole.value) {
            console.log('User has impersonation role. Redirecting to user-details');
            next('/user-details');
          } else {
            next('summary');
          }
        } else {
          next();
        }
      },
    ),
  },
  {
    path: '/email-login',
    name: 'email-login',
    component: () => import('../views/EmailLogin.vue'),
  },
  {
    path: '/user-details',
    name: 'user-details',
    component: () => import('../views/UserDetails.vue'),
    beforeEnter: redirectIf(
      () =>
        !useOrderRequestStore().taskCategory ||
        !useOrderRequestStore().taskDescription ||
        !useOrderRequestStore().shipToAddress ||
        !useOrderRequestStore().time,
      'index',
      async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
        next();
      },
    ),
  },
  {
    path: '/email-login-redirect',
    name: 'email-login-redirect',
    component: () => import('../views/EmailLoginRedirect.vue'),
  },
  {
    path: '/login-redirect',
    name: 'login-redirect',
    component: () => import('../views/LoginRedirect.vue'),
  },
  {
    path: '/silent-redirect',
    name: 'silent-redirect',
    component: () => import('@/views/SilentRedirect.vue'),
  },
  {
    path: '/summary',
    name: 'orderRequestSummary',
    component: () => import('../views/OrderRequestSummary.vue'),
    beforeEnter: redirectIf(
      () =>
        !useOrderRequestStore().taskCategory ||
        !useOrderRequestStore().taskDescription ||
        !useOrderRequestStore().shipToAddress ||
        !useOrderRequestStore().time,
      'index',
      async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
        if (!!userState.value) {
          if (!isAllMandatoryUserDetailsFilled()) {
            console.log('Not all mandatory user details are filled. Redirecting to user-details');
            next('/user-details');
          } else {
            next();
          }
        } else {
          next('/login');
        }
      },
    ),
  },
  {
    path: '/bill-to-address',
    name: 'billToAddress',
    component: () => import('../views/BillToAddress.vue'),
    beforeEnter: redirectIf(
      () =>
        !useOrderRequestStore().taskCategory ||
        !useOrderRequestStore().taskDescription ||
        !useOrderRequestStore().shipToAddress ||
        !useOrderRequestStore().time,
      'index',
    ),
  },
  {
    path: '/confirmation',
    name: 'orderRequestConfirmed',
    component: () => import('../views/OrderRequestConfirmed.vue'),
  },
];

function isAllMandatoryUserDetailsFilled() {
  return useUserDetailsStore()?.name && useUserDetailsStore()?.phone && useUserDetailsStore()?.email;
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

export default router;
