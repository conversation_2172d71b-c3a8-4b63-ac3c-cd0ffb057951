import type { IconDefinition } from '@fortawesome/free-solid-svg-icons';

export type IconColor = 'text-cerulean-500' | 'text-gray-600' | 'text-gray-400' | 'text-gray-200';
export type IconBackground = 'bg-transparent' | 'bg-gray-100';
type PlaceholderColor = 'placeholder:text-gray-400' | 'placeholder:text-gray-600';

export type InputWithIconsProps = {
  autoComplete?: string;
  disabled?: boolean;
  inputId: string;
  inputType?: 'text' | 'email' | 'password' | 'number' | 'tel';
  placeholder?: string;
  prefixIcon?: IconDefinition;
  prefixIconColor?: IconColor;
  prefixIconBackground?: IconBackground;
  postfixIcon?: IconDefinition;
  postfixIconColor?: IconColor;
  postfixIconBackground?: IconBackground;
  placeholderColor?: PlaceholderColor;
};
