<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { type IconDefinition } from '@fortawesome/free-solid-svg-icons';

type InputSummaryButtonProps = {
  placeholder: string;
  value: string | undefined;
  prefixIcon?: IconDefinition;
};

defineProps<InputSummaryButtonProps>();
</script>

<template>
  <button class="w-full flex items-start rounded-lg border border-gray-200" test-id="input-summary-button">
    <FontAwesomeIcon
      v-if="prefixIcon"
      :icon="prefixIcon"
      class="size-5 p-2 m-auto ml-3 rounded bg-gray-100 text-cerulean-500"
    />
    <span class="w-full p-2 rounded-lg flex flex-col items-start min-w-0">
      <span class="text-gray-400 text-xs">{{ placeholder }}</span>
      <span class="text-sm truncate w-full text-left flex-shrink-0">{{ value }}</span>
    </span>
  </button>
</template>
