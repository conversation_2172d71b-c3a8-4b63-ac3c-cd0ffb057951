<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import type { InputWithIconsProps } from '@/components/props/InputWithIconsProps.ts';
import { ref } from 'vue';

const model = defineModel();

withDefaults(defineProps<InputWithIconsProps>(), {
  autoComplete: 'off',
  inputType: 'text',
  prefixIconColor: 'text-gray-400',
  postfixIconColor: 'text-gray-400',
  placeholderColor: 'placeholder:text-gray-400',
});

const emit = defineEmits(['inputFocussed', 'enter-pressed']);

const inputRef = ref<HTMLInputElement | null>(null);

async function focusInput(position?: number) {
  await inputRef.value?.focus();
  if (position) {
    await inputRef.value?.setSelectionRange(position, position);
  }
}

defineExpose({
  focusInput,
});
</script>

<template>
  <div class="relative shadow-md rounded-lg">
    <FontAwesomeIcon
      v-if="prefixIcon"
      :class="[prefixIconColor, prefixIconBackground]"
      :icon="prefixIcon"
      class="absolute size-5 min-w-5 p-2 left-3 top-1/2 -translate-y-1/2 rounded"
    />
    <label :for="inputId" class="sr-only">{{ placeholder }}</label>
    <input
      :id="inputId"
      ref="inputRef"
      v-model="model"
      :autocomplete="autoComplete"
      :class="[
        {
          'pl-14': prefixIcon,
          'pr-14': postfixIcon,
          'bg-gray-50 cursor-not-allowed text-gray-500': disabled,
          'bg-white': !disabled,
        },
        placeholderColor,
      ]"
      :disabled="disabled"
      :name="inputId"
      :placeholder
      :type="inputType"
      class="w-full p-3 rounded-lg"
      @focus.prevent="emit('inputFocussed')"
      @keydown.enter.prevent="emit('enter-pressed')"
    />
    <FontAwesomeIcon
      v-if="postfixIcon"
      :class="[postfixIconColor, postfixIconBackground]"
      :icon="postfixIcon"
      class="absolute size-5 min-w-5 p-2 right-3 top-1/2 -translate-y-1/2"
    />
  </div>
</template>
