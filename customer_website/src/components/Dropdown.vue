<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { ref } from 'vue';
import type { InputWithIconsProps } from '@/components/props/InputWithIconsProps.ts';
import type { Suggestion } from '@/components/InputWithSuggestions.vue';
import { useClickOutside } from '@/composables/useClickOutside.ts';

const selectedOption = defineModel<Suggestion | null>();

const props = withDefaults(
  defineProps<
    InputWithIconsProps & {
      options?: Suggestion[];
    }
  >(),
  {
    options: () => [],
  },
);

const emit = defineEmits(['suggestionSelected']);

const wrapperRef = ref<HTMLElement | null>(null);
const buttonRef = ref<HTMLElement | null>(null);
const isOpen = ref(true);

function focusInput() {
  buttonRef.value?.focus();
}

useClickOutside(wrapperRef, () => {
  isOpen.value = false;
});

function selectOption(option: Suggestion) {
  selectedOption.value = option;
  isOpen.value = false;
  emit('suggestionSelected', option);
}

defineExpose({ focusInput });
</script>

<template>
  <div ref="wrapperRef" class="mt-4 rounded-lg relative">
    <button
      ref="buttonRef"
      class="relative w-full rounded-lg shadow-md cursor-pointer text-left p-3"
      type="button"
      @focus="isOpen = true"
      @keydown.tab="isOpen = false"
      @keydown.esc="isOpen = false"
    >
      <FontAwesomeIcon
        v-if="props.prefixIcon"
        :class="[props.prefixIconColor, props.prefixIconBackground]"
        :icon="props.prefixIcon"
        class="absolute size-5 min-w-5 p-2 left-3 top-1/2 -translate-y-1/2 rounded"
      />
      <span :class="['block truncate', { 'pl-11': props.prefixIcon, 'text-gray-400': !selectedOption }]">
        {{ selectedOption?.label || props.placeholder }}
      </span>
    </button>

    <section
      v-if="isOpen"
      class="mt-2 w-full bg-white border-gray-50 border rounded-lg shadow-lg absolute z-10"
      test-id="suggestions"
    >
      <div
        v-for="(option, index) in props.options"
        :key="index"
        class="flex flex-row w-full items-center hover:bg-gray-100 py-2 rounded-lg cursor-pointer"
        test-id="suggestion"
        @click.prevent="selectOption(option)"
      >
        <FontAwesomeIcon :icon="option.icon" class="size-5 min-w-5 rounded p-2 ml-3 text-cerulean-500 bg-transparent" />
        <span class="ml-2">{{ option.label }}</span>
      </div>
    </section>
  </div>
</template>
