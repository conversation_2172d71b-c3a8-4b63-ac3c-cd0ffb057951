<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import type { IconDefinition } from '@fortawesome/free-solid-svg-icons';
import { computed, ref } from 'vue';
import type { IconBackground, IconColor, InputWithIconsProps } from '@/components/props/InputWithIconsProps.ts';
import InputWithIcons from '@/components/InputWithIcons.vue';

const model = defineModel();

export type Suggestion = {
  label: string;
  icon: IconDefinition;
};

const props = withDefaults(
  defineProps<
    InputWithIconsProps & {
      suggestions?: Suggestion[];
      suggestionIconColor?: IconColor;
      suggestionIconBackground?: IconBackground;
      showSuggestions?: boolean;
    }
  >(),
  {
    suggestions: () => [],
    suggestionIconColor: 'text-cerulean-500',
    suggestionIconBackground: 'bg-transparent',
    showSuggestions: undefined,
  },
);

const emit = defineEmits(['inputFocussed', 'suggestionSelected', 'enterPressedWithoutSuggestions']);

const inputRef = ref();

function focusInput(position?: number) {
  inputRef.value?.focusInput(position);
}

function handleEnterPressed() {
  if (props.suggestions?.length > 0) {
    emit('suggestionSelected', props.suggestions[0]);
  } else {
    emit('enterPressedWithoutSuggestions');
  }
}

defineExpose({
  focusInput,
});

const shouldShowSuggestions = computed(() => {
  return (props.showSuggestions ?? true) && props.suggestions.length > 0;
});
</script>

<template>
  <div class="mt-4 rounded-lg">
    <InputWithIcons
      ref="inputRef"
      v-model="model"
      v-bind="props"
      @input-focussed="emit('inputFocussed')"
      @enter-pressed="handleEnterPressed"
    />
    <section
      v-if="shouldShowSuggestions"
      class="mt-2 w-full relative bg-white border-gray-50 border rounded-lg shadow-lg"
      test-id="suggestions"
    >
      <div
        v-for="(suggestion, index) in suggestions"
        :key="index"
        class="flex flex-row w-full items-center hover:bg-gray-100 py-2 rounded-lg cursor-pointer"
        test-id="suggestion"
        @click.prevent="emit('suggestionSelected', suggestion)"
      >
        <FontAwesomeIcon
          :class="[suggestionIconColor, suggestionIconBackground]"
          :icon="suggestion.icon"
          class="size-5 min-w-5 rounded p-2 ml-3"
        />
        <span class="ml-2">{{ suggestion.label }}</span>
      </div>
    </section>
  </div>
</template>
