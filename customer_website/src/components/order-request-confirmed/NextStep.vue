<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import type { IconDefinition } from '@fortawesome/free-solid-svg-icons';

defineProps<{
  icon: IconDefinition;
}>();
</script>

<template>
  <div class="flex items-center" test-id="next-step">
    <div class="bg-cerulean-100 rounded-full size-10 min-w-10 min-h-10 flex items-center justify-center">
      <FontAwesomeIcon :icon="icon" class="text-cerulean-500 min-h-5 min-w-5" />
    </div>
    <span class="text-sm ml-6">
      <slot />
    </span>
  </div>
</template>
