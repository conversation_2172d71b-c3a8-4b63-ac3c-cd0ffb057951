<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import NavbarLink from '@/components/NavbarLink.vue';
import { ref } from 'vue';
import { logout as authLogout } from '@/auth/auth.ts';
import HorizontalLine from '@/components/order-request-summary/HorizontalLine.vue';
import { useUserDetailsStore } from '@/stores/userDetails.ts';

const menuItems = [
  {
    title: 'Home',
    path: 'https://deinhandwerker365.de/',
  },
  {
    title: 'Impressum',
    path: 'https://deinhandwerker365.de/impressum/',
  },
];

const userDetails = useUserDetailsStore();

const open = ref(false);

function logout() {
  authLogout();
}
</script>

<template>
  <div class="px-5 shadow-md z-50">
    <header class="flex flex-col lg:flex-row justify-between items-center my-3">
      <div class="flex w-full lg:w-auto items-center justify-between">
        <a id="logo" href="/" test-id="navbar-logo">
          <img alt="DeinHandwerker365 Logo" class="h-12 lg:h-18" src="../assets/img/deinhandwerker-logo.png" />
        </a>
        <div class="block lg:hidden">
          <button class="text-gray-800" @click="open = !open">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <title>Menu</title>
              <path
                v-show="open"
                clip-rule="evenodd"
                d="M18.278 16.864a1 1 0 01-1.414 1.414l-4.829-4.828-4.828 4.828a1 1 0 01-1.414-1.414l4.828-4.829-4.828-4.828a1 1 0 011.414-1.414l4.829 4.828 4.828-4.828a1 1 0 111.414 1.414l-4.828 4.829 4.828 4.828z"
                fill-rule="evenodd"
              ></path>
              <path
                v-show="!open"
                d="M4 5h16a1 1 0 010 2H4a1 1 0 110-2zm0 6h16a1 1 0 010 2H4a1 1 0 010-2zm0 6h16a1 1 0 010 2H4a1 1 0 010-2z"
                fill-rule="evenodd"
              ></path>
            </svg>
          </button>
        </div>
      </div>
      <nav :class="{ block: open, hidden: !open }" class="w-full lg:w-auto mt-2 lg:flex lg:mt-0">
        <ul class="flex flex-col gap-2 lg:hidden">
          <li>
            <a class="flex lg:px-3 py-2 text-gray-600 hover:text-gray-900 hover:underline rounded" href="/task">
              Neue Anfrage
            </a>
          </li>
          <li v-for="(item, index) of menuItems" :key="index">
            <a :href="item.path" class="flex lg:px-3 py-2 text-gray-600 hover:text-gray-900 hover:underline rounded">
              {{ item.title }}
            </a>
          </li>
          <li>
            <HorizontalLine class="my-4" />
            <span v-if="!!userDetails.name || !!userDetails.email" class="flex flex-col gap-3">
              <span>
                Eingeloggt als <b>{{ userDetails.name || userDetails.email }}</b>
              </span>
              <NavbarLink class-name="m-a self-start" to="#" @click.prevent="logout()">Abmelden</NavbarLink>
            </span>
            <span v-else class="my-4">
              <NavbarLink to="/user-login">Anmelden</NavbarLink>
            </span>
          </li>
        </ul>
      </nav>
      <div class="hidden lg:flex items-center gap-4">
        <ul class="flex flex-row gap-3">
          <li v-for="(item, index) of menuItems" :key="index">
            <a :href="item.path" class="flex lg:px-3 py-2 text-gray-600 hover:text-gray-900 hover:underline rounded">
              {{ item.title }}
            </a>
          </li>
        </ul>
        <NavbarLink style-name="secondary" to="/task"> Neue Anfrage</NavbarLink>
        <template v-if="!!userDetails.name || !!userDetails.email">
          <NavbarLink to="#" @click.prevent="logout()">Abmelden</NavbarLink>
          <span>
            Eingeloggt als <b>{{ userDetails.name || userDetails.email }}</b>
          </span>
        </template>
        <template v-else>
          <NavbarLink to="/user-login">Anmelden</NavbarLink>
        </template>
      </div>
    </header>
  </div>
</template>

<style scoped>
#logo {
  max-width: 240px;
}
</style>
