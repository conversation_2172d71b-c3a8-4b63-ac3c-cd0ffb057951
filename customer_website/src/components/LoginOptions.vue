<script lang="ts" setup>
import DefaultButton from '@/components/DefaultButton.vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const { onGoogleLogin, onMicrosoftLogin } = defineProps<{
  onGoogleLogin: () => void;
  onMicrosoftLogin: () => void;
}>();
</script>

<template>
  <h2 class="text-xl font-medium mt-2">Login</h2>
  <DefaultButton
    class="mt-3 w-full"
    size="lg"
    style-name="ak-blue"
    test-id="google-login-button"
    @click.prevent="onGoogleLogin"
  >
    Mit Google anmelden
  </DefaultButton>

  <DefaultButton
    class="mt-3 w-full"
    size="lg"
    style-name="ak-blue"
    test-id="microsoft-login-button"
    @click.prevent="onMicrosoftLogin"
  >
    Mit Microsoft anmelden
  </DefaultButton>

  <DefaultButton
    class="mt-3 w-full"
    size="lg"
    style-name="ak-blue"
    test-id="email-login-button"
    @click.prevent="router.push('/email-login')"
  >
    Mit E-Mail anmelden
  </DefaultButton>
</template>
