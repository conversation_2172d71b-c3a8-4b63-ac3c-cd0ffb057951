<script lang="ts" setup>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import type { IconDefinition } from '@fortawesome/free-solid-svg-icons';

defineProps<{
  icon: IconDefinition;
  title: string;
  description: string;
}>();
</script>

<template>
  <div class="flex flex-row gap-5 items-start mt-5">
    <FontAwesomeIcon :icon="icon" class="min-h-6 min-w-6 mt-1 text-bamboo-600" />
    <div>
      <h4 class="font-bold">{{ title }}</h4>
      <p class="mt-1">
        {{ description }}
      </p>
    </div>
  </div>
</template>
