<script lang="ts" setup>
interface Props {
  to: string;
  size?: 'md' | 'lg';
  block?: boolean;
  styleName?: 'outline' | 'primary' | 'secondary' | 'inverted' | 'muted';
  className?: string;
}

withDefaults(defineProps<Props>(), {
  size: 'lg',
  styleName: 'primary',
});

const sizes = {
  lg: 'px-5 py-2.5',
  md: 'px-4 py-2',
};

const styles = {
  outline: 'bg-white border-2 border-black hover:bg-gray-100 text-black',
  primary: 'text-cerulean-400 font-bold hover:bg-cerulean-50 border-2',
  secondary:
    'text-white font-bold bg-second-blue-600 hover:bg-second-blue-800 border-2 border-second-blue-600 hover:border-second-blue-800',
  inverted: 'bg-white text-black border-2 border-transparent',
  muted: 'bg-gray-100 text-black hover:bg-gray-200 border-2 border-transparent',
};
</script>

<template>
  <RouterLink
    :class="[
      'rounded text-center transition focus-visible:ring-2 ring-offset-2 ring-gray-200',
      block && 'w-full',
      sizes[size],
      styles[styleName],
      className,
    ]"
    :to="to"
  >
    <slot />
  </RouterLink>
</template>
