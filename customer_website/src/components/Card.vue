<!--eslint-disable vue/multi-word-component-names-->
<script lang="ts" setup>
import { faCheckCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

export type CardType = keyof typeof cardStyle;

defineProps<{
  cardType: CardType;
  message: string;
}>();

const cardStyle = {
  success: 'bg-akSuccessFlavor',
};

const iconStyle = {
  success: 'text-akSuccess',
};
</script>

<template>
  <div
    ref="topMostElement"
    :class="[cardStyle[cardType]]"
    class="flex w-full rounded-lg py-3 px-5 items-center"
    test-id="card"
  >
    <FontAwesomeIcon :class="[iconStyle[cardType]]" :icon="faCheckCircle" class="min-h-8 min-w-8" />
    <span class="text-base pl-4">{{ message }}</span>
  </div>
</template>
