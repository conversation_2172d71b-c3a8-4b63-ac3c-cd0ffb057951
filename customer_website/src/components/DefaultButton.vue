<script lang="ts" setup>
import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

interface Props {
  size?: keyof typeof sizes;
  block?: boolean;
  styleName?: keyof typeof styles;
  className?: string;
  disabled?: boolean;
}

withDefaults(defineProps<Props>(), {
  size: 'md',
  styleName: 'primary',
});

const sizes = {
  md: 'p-2.5',
  lg: 'p-4',
};

const styles = {
  outline: 'border-2 border-black hover:bg-black text-black hover:text-white',
  primary: 'bg-black text-white hover:bg-slate-900 border-2 border-transparent primary-button',
  'ak-blue': 'bg-gradient-cerulean-with-hover text-white focus-visible:outline-2',
  'ak-red': 'bg-gradient-bamboo-with-hover text-white',
  mockDisabledBlue: 'bg-gradient-cerulean-with-hover text-white focus-visible:outline-2 opacity-50',
  link: 'bg-transparent text-cerulean-600 focus-visible:outline-2',
};
</script>

<template>
  <button
    :class="[
      'rounded-lg text-center transition py-4 ',
      block && 'w-full',
      sizes[size],
      styles[styleName],
      className,
      disabled ? 'opacity-50' : '',
    ]"
    :disabled
  >
    <FontAwesomeIcon v-if="styleName === 'link'" :icon="faArrowRight" class="mr-2" />
    <slot />
  </button>
</template>

<style lang="postcss">
/* Needs to be imported for using @apply */
@import '@/assets/tailwind.css';

/* is used in script */
/*noinspection CssUnusedSymbol*/
.primary-button[disabled] {
  opacity: 0.8;
  @apply: bg-black;

  &:hover {
    @apply bg-black;
  }
}

/*noinspection CssUnusedSymbol*/
.bg-gradient-cerulean-with-hover {
  background: linear-gradient(126.61deg, #2fb6d8 0%, #3a539a 100%);

  &:hover {
    background: linear-gradient(126.61deg, #248ea6 0%, #273970 100%);
  }
}

/*noinspection CssUnusedSymbol*/
.bg-gradient-bamboo-with-hover {
  background: linear-gradient(126.61deg, #ff5454 0%, #c40202 100%);

  &:hover {
    background: linear-gradient(126.61deg, #cc4444 0%, #990202 100%);
  }
}
</style>
