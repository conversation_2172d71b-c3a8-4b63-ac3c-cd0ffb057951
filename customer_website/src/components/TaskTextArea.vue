<script lang="ts" setup>
import { computed, ref } from 'vue';
import { useTypingPlaceholder } from '@/composables/useTypingPlaceholder';

const exampleTexts = [
  'Ich benötige einen Klempner, um ein tropfendes Waschbecken im Badezimmer zu reparieren. Es tropft seit etwa einer Woche.\n\nHinweis: Die Wohnung befindet sich im Hinterhof.',
  'In meiner Küche ist eine Boden-Fliese gebrochen und müsste ersetzt werden. Die Fliese befindet sich in der Mitte des Raums.',
  'Die Steckdose im Wohnzimmer funktioniert nicht mehr und muss überprüft werden. Es kommt kein Strom mehr an.',
  'Ich brauche einen Maler, der mein Schlafzimmer (20qm) neu streichen kann. Die Decke hat Wasserflecken.',
  'Die Heizung in unserem Büro macht seltsame Geräusche und wird nicht richtig warm. Sie ist etwa 15 Jahre alt.',
];

const model = defineModel<string>();
const isPaused = computed(() => !!model.value && model.value?.length > 0);
const textareaRef = ref<HTMLTextAreaElement | null>(null);

function focusInput() {
  textareaRef.value?.focus();
}

defineExpose({
  focusInput,
});

const { placeholder } = useTypingPlaceholder(exampleTexts, {
  typingSpeed: 7,
  pauseDuration: 2000,
  isPausedExternally: () => isPaused.value,
});

const emit = defineEmits(['submit']);

function onEnter(e: KeyboardEvent) {
  if (e.ctrlKey) {
    emit('submit');
  }
}

const maxLength = 450;

const remainingChars = computed(() => {
  return model.value?.length || 0;
});
</script>

<template>
  <div class="relative w-full shadow-md bg-white rounded-lg focus-within:ring-2 focus-within:ring-cerulean-500">
    <textarea
      id="task"
      ref="textareaRef"
      v-model="model"
      :maxlength="maxLength"
      :placeholder="placeholder"
      class="resize-none p-4 w-full min-h-48 md:min-h-64 bg-white rounded-lg outline-none border-none transition-all"
      test-id="taskTextArea"
      @keydown.enter="onEnter"
    />
    <div class="absolute bottom-2 right-4 text-sm text-gray-500">{{ remainingChars }} / {{ maxLength }}</div>
  </div>
</template>
