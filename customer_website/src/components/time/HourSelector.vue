<script lang="ts" setup>
import SelectorContainer from '@/components/time/SelectorContainer.vue';
import SelectorItem from '@/components/time/SelectorItem.vue';

defineProps<{
  hourOptions: number[];
  selectedHourOption?: number;
}>();
const emit = defineEmits(['selectHour']);
</script>

<template>
  <SelectorContainer>
    <SelectorItem
      v-for="hourOption in hourOptions"
      :key="hourOption"
      :is-selected="selectedHourOption === hourOption"
      test-id="hour-selector-item"
      @click.prevent="emit('selectHour', hourOption)"
    >
      <div class="text-sm my-2">{{ hourOption.toString().padStart(2, '0') }}:00</div>
    </SelectorItem>
  </SelectorContainer>
</template>
