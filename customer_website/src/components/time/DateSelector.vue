<script lang="ts" setup>
import SelectorContainer from './SelectorContainer.vue';
import type { DateOption } from '@/services/timeService.ts';
import SelectorItem from '@/components/time/SelectorItem.vue';

defineProps<{
  dateOptions: DateOption[];
  selectedDateOption?: DateOption;
}>();
const emit = defineEmits(['selectDate']);

function hasAnyAvailableHour(dateOption: DateOption) {
  return dateOption.availableHours.length > 0;
}
</script>

<template>
  <SelectorContainer>
    <SelectorItem
      v-for="(dateOption, index) in dateOptions"
      :key="index"
      :disabled="!hasAnyAvailableHour(dateOption)"
      :is-selected="selectedDateOption?.date.isSame(dateOption.date, 'day')"
      test-id="date-selector-item"
      @click.prevent="hasAnyAvailableHour(dateOption) && emit('selectDate', dateOption)"
    >
      <div class="text-sm">{{ dateOption.date.format('MMM').substring(0, 3) }}</div>
      <div class="text-lg font-medium my-2">{{ dateOption.date.format('DD.') }}</div>
      <div class="text-sm">{{ dateOption.date.format('dd') }}</div>
    </SelectorItem>
  </SelectorContainer>
</template>
