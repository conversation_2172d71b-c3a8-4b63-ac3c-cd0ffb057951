import { describe, expect, it } from 'vitest';
import { mount } from '@vue/test-utils';
import Navbar from '../Navbar.vue';
import { createPinia, setActivePinia } from 'pinia';

describe('Navbar', () => {
  it('contains Alleskoenner logo with correct href', async () => {
    setActivePinia(createPinia());
    const wrapper = mount(Navbar);
    const logo = wrapper.find('a[test-id="navbar-logo"]');
    expect(logo.exists()).toBeTruthy();
    expect(logo.attributes('href')).toBe('/');
  });
});
