import { describe, expect, it } from 'vitest';
import { mount } from '@vue/test-utils';
import InputWithSuggestions from '../InputWithSuggestions.vue';

const globalStubs = { FontAwesomeIcon: true };

describe('InputWithSuggestions', () => {
  it('emits enterPressedWithoutSuggestions when no suggestions exist and enter is pressed', async () => {
    const wrapper = mount(InputWithSuggestions, {
      props: { inputId: 'location' },
      global: { stubs: globalStubs },
    });

    await wrapper.find('input').trigger('keydown.enter');

    expect(wrapper.emitted('enterPressedWithoutSuggestions')).toBeTruthy();
  });
});
