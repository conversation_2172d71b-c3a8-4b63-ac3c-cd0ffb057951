import { beforeEach, describe, expect, it, vi } from 'vitest';
import { _forTesting } from '@/services/orderRequestService.ts';
import { faHouse, faPaintRoller } from '@fortawesome/free-solid-svg-icons';
import { useOrderRequestStore } from '@/stores/orderRequest.ts';
import { createPinia, setActivePinia } from 'pinia';
import { useUserDetailsStore } from '@/stores/userDetails.ts';

global.fetch = vi.fn();

beforeEach(() => {
  setActivePinia(createPinia());
});

describe('buildOrderRequestPayloadFromStore', () => {
  it('should build the payload with a specific appointmentRequestDate with same bill to address', () => {
    const orderRequestStore = useOrderRequestStore();
    orderRequestStore.$patch({
      taskDescription: 'Malerarbeiten',
      taskCategory: {
        icon: faPaint<PERSON>oll<PERSON>,
        label: 'Maler- und Ausbesserungsarbeiten (Wände, Fliesen, Böden)',
        id: 'Painting',
      },
      shipToAddress: {
        icon: faHouse,
        label: 'Stresemannstraße 123, 10963 Berlin, Deutschland',
        address: {
          label: 'Stresemannstraße 123, 10963 Berlin, Deutschland',
          street: 'Stresemannstraße',
          houseNumber: '123',
          city: 'Berlin',
          countryCode: 'DEU',
          countryName: 'Deutschland',
          postalCode: '10963',
          county: 'Berlin',
          state: 'Berlin',
        },
      },
      time: '2025-02-07T06:00:00.000Z',
      isConditionsChecked: true,
      isBillToDifferent: false,
    });
    const userDetailStore = useUserDetailsStore();
    userDetailStore.$patch({
      name: 'Max Mustermann',
      email: '<EMAIL>',
      phone: '0123456789',
      type: 'Person',
    });

    const payload = _forTesting.buildOrderRequestPayloadFromStore();

    expect(payload).toMatchObject({
      shipToName: 'Max Mustermann',
      shipToAddressLine1: 'Stresemannstraße 123',
      shipToAddressLine2: '',
      shipToCity: 'Berlin',
      shipToPostalCode: '10963',
      shipToPhoneNumber: '0123456789',
      billToName: 'Max Mustermann',
      billToAddressLine1: 'Stresemannstraße 123',
      billToAddressLine2: '',
      billToCity: 'Berlin',
      billToPostalCode: '10963',
      billToPhoneNumber: '0123456789',
      customerType: 'Person',
      taskDescription: 'Malerarbeiten',
      taskCategory: 'Painting',
      isConditionsChecked: true,
      isAsSoonAsPossible: false,
      appointmentRequestDate: new Date('2025-02-07T06:00:00.000Z'),
      requestedHandymanSkill: '',
    });
  });

  it('should build the payload with a specific appointmentRequestDate with different bill to address', () => {
    const orderRequestStore = useOrderRequestStore();
    orderRequestStore.$patch({
      taskDescription: 'Malerarbeiten',
      taskCategory: {
        icon: faPaintRoller,
        label: 'Maler- und Ausbesserungsarbeiten (Wände, Fliesen, Böden)',
        id: 'Painting',
      },
      shipToAddress: {
        icon: faHouse,
        label: 'Stresemannstraße 123, 10963 Berlin, Deutschland',
        address: {
          label: 'Stresemannstraße 123, 10963 Berlin, Deutschland',
          street: 'Stresemannstraße',
          houseNumber: '123',
          city: 'Berlin',
          countryCode: 'DEU',
          countryName: 'Deutschland',
          postalCode: '10963',
          county: 'Berlin',
          state: 'Berlin',
        },
      },
      billToAddress: {
        icon: faHouse,
        label: 'Musterstraße 99, 50667 Köln, Deutschland',
        address: {
          label: 'Musterstraße 99, 50667 Köln, Deutschland',
          street: 'Musterstraße',
          houseNumber: '99',
          city: 'Köln',
          countryCode: 'DEU',
          countryName: 'Deutschland',
          postalCode: '50667',
          county: 'Köln',
          state: 'Nordrhein-Westfalen',
        },
      },
      time: '2025-02-07T06:00:00.000Z',
      isConditionsChecked: true,
      isBillToDifferent: true,
    });
    const userDetailStore = useUserDetailsStore();
    userDetailStore.$patch({
      name: 'Max Mustermann',
      email: '<EMAIL>',
      phone: '0123456789',
      type: 'Person',
    });

    const payload = _forTesting.buildOrderRequestPayloadFromStore();

    expect(payload).toMatchObject({
      shipToName: 'Max Mustermann',
      shipToAddressLine1: 'Stresemannstraße 123',
      shipToAddressLine2: '',
      shipToCity: 'Berlin',
      shipToPostalCode: '10963',
      shipToPhoneNumber: '0123456789',
      billToName: 'Max Mustermann',
      billToAddressLine1: 'Musterstraße 99',
      billToAddressLine2: '',
      billToCity: 'Köln',
      billToPostalCode: '50667',
      billToPhoneNumber: '0123456789',
      customerType: 'Person',
      taskDescription: 'Malerarbeiten',
      isConditionsChecked: true,
      isAsSoonAsPossible: false,
      appointmentRequestDate: new Date('2025-02-07T06:00:00.000Z'),
      requestedHandymanSkill: '',
    });
  });

  it('should build the payload with appointmentRequestDate as "asap"', () => {
    const orderRequestStore = useOrderRequestStore();
    orderRequestStore.$patch({
      taskDescription: 'Malerarbeiten',
      taskCategory: {
        icon: faPaintRoller,
        label: 'Maler- und Ausbesserungsarbeiten (Wände, Fliesen, Böden)',
        id: 'Painting',
      },
      shipToAddress: {
        icon: faHouse,
        label: 'Stresemannstraße 123, 10963 Berlin, Deutschland',
        address: {
          label: 'Stresemannstraße 123, 10963 Berlin, Deutschland',
          street: 'Stresemannstraße',
          houseNumber: '123',
          city: 'Berlin',
          countryCode: 'DEU',
          countryName: 'Deutschland',
          postalCode: '10963',
          county: 'Berlin',
          state: 'Berlin',
        },
      },
      time: 'asap',
      isConditionsChecked: true,
    });
    const userDetailStore = useUserDetailsStore();
    userDetailStore.$patch({
      name: 'Max Mustermann',
      email: '<EMAIL>',
      phone: '0123456789',
      type: 'Person',
    });

    const payload = _forTesting.buildOrderRequestPayloadFromStore();

    expect(payload).toMatchObject({
      isAsSoonAsPossible: true,
      appointmentRequestDate: undefined,
    });
  });
});
