import { afterAll, describe, expect, it, vi } from 'vitest';
import dayjs from 'dayjs';
import { _forTesting, getDateOptions } from '@/services/timeService.ts';

describe('getDateOptions', () => {
  it('generate 15 days of date options', () => {
    const dateOptions = getDateOptions();
    expect(dateOptions.length).toBe(15);
  });

  // TODO: use these tests once the date/hour selector is enabled again
  it.skip('all available hours for non-holiday Monday', () => {
    const someMonday = dayjs('2025-01-13');
    vi.setSystemTime(someMonday.toDate());

    const dateOptions = getDateOptions();

    const expectedHours = [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20];
    dateOptions.slice(0, 6).forEach((dateOptionOnWeekday) => {
      expect(dateOptionOnWeekday.availableHours).toEqual(expectedHours);
    });
  });

  it('an empty array for Sundays', () => {
    const sunday = dayjs('2025-01-12');
    vi.setSystemTime(sunday.toDate());

    const dateOptions = getDateOptions();

    expect(dateOptions[0].availableHours).toEqual([]);
  });

  it('an empty array for public holidays', () => {
    const holiday = dayjs('2025-12-25');
    vi.setSystemTime(holiday.toDate());

    const dateOptions = getDateOptions();

    expect(dateOptions[0].availableHours).toEqual([]);
  });

  // TODO: use these tests once the date/hour selector is enabled again
  const halfHolidayDates = [dayjs('2025-12-24'), dayjs('2025-12-31')];
  halfHolidayDates.forEach((halfHoliday) => {
    it.skip(`hours 6 to 13 array for ${halfHoliday.format('YYYY-MM-DD')} public half-holidays (holiday in the afternoon)`, () => {
      vi.setSystemTime(halfHoliday.startOf('day').toDate());

      const dateOptions = getDateOptions();

      expect(dateOptions[0].availableHours).toEqual([6, 7, 8, 9, 10, 11, 12, 13]);
    });
  });
});

describe('getFirstAvailableHourToday', () => {
  const testCases = [
    { time: '0:00', expected: 6 },
    { time: '0:30', expected: 6 },
    { time: '12:30', expected: 16 },
    { time: '12:31', expected: 17 },
    { time: '12:59', expected: 17 },
    { time: '13:00', expected: 17 },
    { time: '13:15', expected: 17 },
    { time: '13:29', expected: 17 },
    { time: '13:30', expected: 17 },
    { time: '13:31', expected: 18 },
    { time: '19:31', expected: 24 },
    { time: '23:31', expected: 28 },
  ];

  testCases.forEach(({ time, expected }) => {
    it(`first available hour is ${expected} when time is ${time}`, () => {
      const [hours, minutes] = time.split(':').map(Number);
      vi.setSystemTime(dayjs().hour(hours).minute(minutes).toDate());

      const result = _forTesting.getFirstAvailableHourToday();
      expect(result).toBe(expected);
    });
  });

  afterAll(() => {
    vi.useRealTimers();
  });
});
