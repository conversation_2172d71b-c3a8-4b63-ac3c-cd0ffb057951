import { defineStore } from 'pinia';
import type { OrderRequestResponseStore, OrderRequestStore } from '@/types/OrderRequestStore.type.ts';
// vue-tsc doesnt get the persist plugin.. soo ignore it
// @ts-ignore
export const useOrderRequestStore = defineStore('orderRequest', {
  state: (): OrderRequestStore => ({
    taskCategory: null,
    taskDescription: null,
    shipToAddress: null,
    isBillToDifferent: false,
    billToAddress: null,
    billToName: null,
    time: null,
    isConditionsChecked: false,
  }),
  persist: true,
});

// @ts-ignore
export const useOrderRequestResponseStore = defineStore('orderRequestResponse', {
  state: (): OrderRequestResponseStore =>
    ({
      shipToName: null,
      shipToAddressLine1: null,
      shipToAddressLine2: null,
      shipToCity: null,
      shipToPostalCode: null,
      shipToEmail: null,
      shipToPhoneNumber: null,
      billToName: null,
      billToAddressLine1: null,
      billToAddressLine2: null,
      billToCity: null,
      billToPostalCode: null,
      billToEmail: null,
      billToPhoneNumber: null,
      email: null,
      type: null,
      taskCategory: undefined,
      taskDescription: null,
      isConditionsChecked: false,
      salesOrderNumber: null,
    }) as OrderRequestResponseStore,
  persist: true,
});
