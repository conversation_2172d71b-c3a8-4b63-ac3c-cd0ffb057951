import { Log, User, UserManager, WebStorageStateStore } from 'oidc-client-ts';
import { computed, ref } from 'vue';
import router from '@/router/routes.ts';
import { useUserDetailService } from '@/services/userDetailService.ts';
import { useUserDetailsStore } from '@/stores/userDetails.ts';

Log.setLevel(Log.NONE);
Log.setLogger(console);

const CUSTOMER_IMPERSONATION_ROLE = 'customer_impersonation';

export const userState = ref<User | null | undefined>(null);

console.log(window.location.origin + '/login-redirect');
export const userManager = new UserManager({
  userStore: new WebStorageStateStore({ store: window.localStorage }),
  authority: import.meta.env.VITE_OIDC_AUTHORITY,
  client_id: import.meta.env.VITE_OIDC_CLIENT_ID,
  redirect_uri: window.location.origin + '/login-redirect',
  response_type: 'code',
  scope: 'openid profile email',
  post_logout_redirect_uri: window.location.origin,
  silent_redirect_uri: window.location.origin,
  accessTokenExpiringNotificationTimeInSeconds: 10,
  automaticSilentRenew: true,
  filterProtocolClaims: true,
  loadUserInfo: true,
});

// Expose userManager globally for cypress tests
if (import.meta.env.MODE === 'ci' || (window as any).Cypress) {
  // @ts-expect-error - window.userManager must be exposed for cypress tests
  window.userManager = userManager;
}

userManager.events.addUserLoaded(async () => {
  userState.value = await userManager.getUser();
  await setUserDetailsFromBackend();
  console.log('User loaded!');
});

export async function setUserDetailsFromBackend() {
  const userDetailsResponse = await useUserDetailService().getUserDetails();

  const isUserDetailsKnown = userDetailsResponse.ok;
  if (!isUserDetailsKnown && userDetailsResponse.status != 404) {
    throw new Error(`Failed to fetch user details (${userDetailsResponse.status} - ${userDetailsResponse.statusText})`);
  }

  useUserDetailsStore().email = userState.value?.profile.email ?? '';
  if (isUserDetailsKnown) {
    const userDetails = await userDetailsResponse.json();
    useUserDetailsStore().name = userDetails.displayName;
    useUserDetailsStore().type = userDetails.type;
    useUserDetailsStore().phone = userDetails.phoneNumber;
  } else {
    useUserDetailsStore().name = userState.value?.profile.name ?? '';
  }
}

userManager.events.addUserUnloaded(() => {
  console.log('User unloaded!');
  userState.value = null;
});

userManager.events.addUserSignedIn(() => {
  console.log('User signed in!');
});

userManager.events.addUserSignedOut(() => {
  console.log('User signed out!');
});

userManager.events.addSilentRenewError((e) => {
  console.log('addSilentRenewError', e);
});

userManager.events.addUserSessionChanged(() => {
  console.log('addUserSessionChanged');
});

userManager.events.addAccessTokenExpiring(async () => {
  console.log('Access token will expire');
  userState.value = await userManager.getUser();
});

userManager.events.addAccessTokenExpired(() => {
  console.log('Access token expired!');
  renewToken();
});

async function renewToken() {
  try {
    const user = await userManager.signinSilent();
    console.log('Token refreshed');
    userState.value = user;
  } catch (error) {
    console.error('Silent token renewal failed. Redirecting user to login page.', error);
    await router.push('/user-login');
  }
}

export function authTokenHeader(): { Authorization: string } {
  if (userState.value) {
    return {
      Authorization: `Bearer ${userState.value.access_token}`,
    };
  }
  throw new Error('User not logged in');
}

export async function login(
  redirectPath: string = window.location.pathname,
  idpHint: 'google' | 'microsoft',
): Promise<void> {
  console.log('Logging in... with redirect_uri:', redirectPath, 'idpHint:', idpHint);
  await userManager.signinRedirect({
    url_state: redirectPath,
    extraQueryParams: { kc_idp_hint: idpHint },
  });
}

export async function handleLoginRedirectCallback(): Promise<void> {
  console.log('Handling login redirect callback...');
  const user = await userManager.signinCallback();
  userState.value = user;
  await router.replace(user?.url_state ?? '/');
}

export async function logout(): Promise<void> {
  if (!userState.value?.id_token) {
    userState.value = null;
    useUserDetailsStore().$reset();
  } else {
    await userManager.signoutRedirect({
      post_logout_redirect_uri: `${window.location.origin}/logout-callback`,
    });
  }
}

export const hasCustomerImpersonationRole = computed(() => {
  if (userState.value) {
    // @ts-expect-error - roles claim is defined in the Keycloak user profile
    const roles = userState.value.profile?.resource_access['alleskoenner24-customer-client']?.roles || [];
    return roles?.includes(CUSTOMER_IMPERSONATION_ROLE);
  }
  return false;
});

userManager.getUser().then((user) => {
  userState.value = user;
});

function decodeJwt(token: string): string {
  const base64Url = token.split('.')[1];
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  const jsonPayload = decodeURIComponent(
    atob(base64)
      .split('')
      .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
      .join(''),
  );
  return JSON.parse(jsonPayload);
}

async function fetchTokenAndExtractUserProfile(code: string | null) {
  if (!code) {
    throw new Error('Authentication code missing');
  }
  const response = await fetch('https://oidc.klosebrothers.de/realms/alleskoenner24/protocol/openid-connect/token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: 'alleskoenner24-customer-client-magic-link',
      code,
      redirect_uri: window.location.origin + '/email-login-redirect',
    }),
  });
  if (!response.ok) {
    throw new Error('Invalid token');
  }
  const tokenResponse = await response.json();

  const profile = decodeJwt(tokenResponse.access_token);
  return new User({
    ...tokenResponse,
    expires_at: Math.floor(Date.now() / 1000) + tokenResponse.expires_in,
    profile: profile,
  });
}

export async function fetchTokenAndSetUserDetailsFromBackend(code: string | null) {
  userState.value = await fetchTokenAndExtractUserProfile(code);
  await setUserDetailsFromBackend();
}
